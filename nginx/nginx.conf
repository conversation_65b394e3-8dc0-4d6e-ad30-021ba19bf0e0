# =============================================================================
# Main Nginx Configuration for Elder Wand Multi-Project Deployment
# =============================================================================
# 
# This configuration handles routing for all Elder Wand projects
# under a single base URL with environment-specific configurations.
# =============================================================================

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    # =====================================================================
    # Basic Settings
    # =====================================================================
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Custom MIME types
    types {
        application/javascript env.js;
    }
    
    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    
    # Performance
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # =====================================================================
    # Rate Limiting
    # =====================================================================
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
    
    # =====================================================================
    # Upstream Servers
    # =====================================================================
    upstream console_backend {
        server console:8080;
    }
    
    upstream experience_backend {
        server experience-studio:8080;
    }
    
    upstream elder_wand_backend {
        server elder-wand:8080;
    }
    
    upstream product_backend {
        server product-studio:8080;
    }
    
    upstream marketing_backend {
        server marketing:8080;
    }
    
    # =====================================================================
    # Include Environment-Specific Configurations (if any)
    # =====================================================================
    # include /etc/nginx/conf.d/*.conf;
    
    # =====================================================================
    # Default Server Block
    # =====================================================================
    server {
        # Dedicated env.js proxy for nested paths (example paths, add more as needed)
        location = /console/libraries/prompts/assets/env.js {
            add_header Content-Type application/javascript;
            rewrite ^/console(/.*)$ $1 break;
            proxy_pass http://console_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        location = /console/some-feature/assets/env.js {
            add_header Content-Type application/javascript;
            proxy_pass http://console_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            try_files $uri =404;
        }
        location = /experience/some-feature/assets/env.js {
            add_header Content-Type application/javascript;
            proxy_pass http://experience_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            try_files $uri =404;
        }
        location = /launchpad/some-nested-path/assets/env.js {
            add_header Content-Type application/javascript;
            proxy_pass http://elder_wand_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            try_files $uri =404;
        }
        location = /product/some-feature/assets/env.js {
            add_header Content-Type application/javascript;
            proxy_pass http://product_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            try_files $uri =404;
        }
        listen 80 default_server;
        server_name _;
        
        # Health Check Endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # =====================================================================
        # Dedicated env.js proxy for each app (must be above static asset rules)
        # =====================================================================
        location = /console/assets/env.js {
            rewrite ^/console(/.*)$ $1 break;
            proxy_pass http://console_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        location = /experience/assets/env.js {
            rewrite ^/experience(/.*)$ $1 break;
            proxy_pass http://experience_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        location = /launchpad/assets/env.js {
            rewrite ^/launchpad(/.*)$ $1 break;
            proxy_pass http://elder_wand_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        location = /product/assets/env.js {
            rewrite ^/product(/.*)$ $1 break;
            proxy_pass http://product_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # =====================================================================
        # Static Assets Handling (before application routes)
        # =====================================================================
        
        # Console static assets - handle all paths including deep routes
        location ~ ^/console/.*\.(js|mjs|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$ {
            # Remove /console prefix and proxy to backend
            rewrite ^/console(/.*)$ $1 break;
            proxy_pass http://console_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Cache static assets
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # Experience static assets
        location ~ ^/experience/.*\.(js|mjs|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map|env\.js)$ {
            rewrite ^/experience(/.*)$ $1 break;
            proxy_pass http://experience_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Cache static assets
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # Launchpad static assets
        location ~ ^/launchpad/.*\.(js|mjs|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map|env\.js)$ {
            rewrite ^/launchpad(/.*)$ $1 break;
            proxy_pass http://elder_wand_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Cache static assets
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # Product static assets
        location ~ ^/product/.*\.(js|mjs|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map|env\.js)$ {
            rewrite ^/product(/.*)$ $1 break;
            proxy_pass http://product_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Cache static assets
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # =====================================================================
        # Console Application Routing
        # =====================================================================
        location /console/ {
            # Handle env.js files specially
            if ($request_uri ~* "/console/.*/assets/env\.js$") {
                rewrite ^/console/.*/assets/env\.js$ /assets/env.js break;
            }
            
            # Remove /console prefix before forwarding
            rewrite ^/console(/.*)$ $1 break;
            
            # Proxy to console backend
            proxy_pass http://console_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Handle Angular routing
            proxy_intercept_errors on;
            error_page 404 = @console_fallback;
            
            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
        
        # Console root redirect
        location = /console {
            return 301 /console/;
        }

        # Console fallback for SPA routing
        location @console_fallback {
            rewrite ^.*$ /index.html break;
            proxy_pass http://console_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # =====================================================================
        # Experience Studio Application Routing
        # =====================================================================
        location /experience/ {
            # Remove /experience prefix before forwarding
            rewrite ^/experience(/.*)$ $1 break;
            
            # Proxy to experience backend
            proxy_pass http://experience_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Handle Angular routing
            proxy_intercept_errors on;
            error_page 404 = @experience_fallback;
            
            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
        
        # Experience root redirect
        location = /experience {
            return 301 /experience/;
        }

        # Experience fallback for SPA routing
        location @experience_fallback {
            rewrite ^.*$ /index.html break;
            proxy_pass http://experience_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # =====================================================================
        # Elder Wand (Launchpad) Application Routing
        # =====================================================================
        location /launchpad/ {
            # Remove /launchpad prefix before forwarding
            rewrite ^/launchpad(/.*)$ $1 break;
            
            # Proxy to elder wand backend
            proxy_pass http://elder_wand_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Handle Angular routing
            proxy_intercept_errors on;
            error_page 404 = @elder_wand_fallback;
            
            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
        
        # Launchpad root redirect
        location = /launchpad {
            return 301 /launchpad/;
        }

        # Elder Wand fallback for SPA routing
        location @elder_wand_fallback {
            rewrite ^.*$ /index.html break;
            proxy_pass http://elder_wand_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # =====================================================================
        # Product Studio Application Routing
        # =====================================================================
        location /product/ {
            # Remove /product prefix before forwarding
            rewrite ^/product(/.*)$ $1 break;
            
            # Proxy to product backend
            proxy_pass http://product_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Handle Angular routing
            proxy_intercept_errors on;
            error_page 404 = @product_fallback;
            
            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
        
        # Product root redirect
        location = /product {
            return 301 /product/;
        }

        # Product fallback for SPA routing
        location @product_fallback {
            rewrite ^.*$ /index.html break;
            proxy_pass http://product_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # =====================================================================
        # Marketing Application Routing (Root)
        # =====================================================================
        location / {
            # Proxy to marketing backend
            proxy_pass http://marketing_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Handle Angular routing
            proxy_intercept_errors on;
            error_page 404 = @marketing_fallback;
            
            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
        
        # Marketing fallback for SPA routing
        location @marketing_fallback {
            rewrite ^.*$ /index.html break;
            proxy_pass http://marketing_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
} 