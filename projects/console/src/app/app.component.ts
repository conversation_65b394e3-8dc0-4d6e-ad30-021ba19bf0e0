import { Component, OnInit, ViewChild, ViewContainerRef } from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import {
  SharedAppHeaderComponent,
  HeaderConfig,
} from '@shared/components/app-header/app-header.component';
import { consoleHeaderConfig } from './config/header.config';
import { LoaderComponent } from './shared/components/loader/loader.component';
import { DrawerService } from './shared/services/drawer/drawer.service';
import { ThemeService } from './shared/services/theme/theme.service';
import { LoaderService } from './shared/services/loader/loader.service';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { AuthTokenService } from '@shared/auth/services/auth-token.service';
import { AuthConfig } from '@shared/auth/interfaces/auth-config.interface';
import { AuthService } from '@shared/auth/services/auth.service';
import { OrgConfigService } from './pages/org-config/services/org-config.service';
import { environment } from '../environments/environment';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    RouterOutlet,
    SharedAppHeaderComponent,
    LoaderComponent,
    CommonModule,
  ],
  providers: [OrgConfigService],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit {
  @ViewChild('dialogHost', { read: ViewContainerRef })
  dialogHost!: ViewContainerRef;
  showHeaderAndNav: boolean = true;

  // Header configuration
  headerConfig: HeaderConfig = consoleHeaderConfig;

  // Retractable header properties
  // isHeaderVisible: boolean = true;
  // private headerTimeout: any;
  // private readonly HEADER_SHOW_DURATION = 1000; // 1 second delay before hiding
  // private readonly HEADER_ACTIVATION_ZONE = 100; // Top 100px area to activate header
  // private isMouseOnHeader: boolean = false;

  constructor(
    private authTokenService: AuthTokenService,
    private tokenStorage: TokenStorageService,
    private drawerService: DrawerService,
    private themeService: ThemeService,
    public orgConfigService: OrgConfigService,
    private authService: AuthService,
    private router: Router,
    private loaderService: LoaderService, // Inject LoaderService
  ) {}

  // Mouse event handlers for retractable header
  // @HostListener('mousemove', ['$event'])
  // onMouseMove(event: MouseEvent) {
  //   if (this.showHeaderAndNav) {
  //     const mouseY = event.clientY;

  //     // Check if mouse is in the top activation zone
  //     if (mouseY <= this.HEADER_ACTIVATION_ZONE) {
  //       this.showHeader();
  //       return;
  //     }

  //     // Check if mouse is on the header area (when header is visible)
  //     if (this.isHeaderVisible) {
  //       // Get header element to check if mouse is on it
  //       const headerElement = document.querySelector(
  //         '.retractable-header',
  //       ) as HTMLElement;
  //       const breadcrumbElement = document.querySelector(
  //         '.retractable-breadcrumb',
  //       ) as HTMLElement;

  //       if (headerElement || breadcrumbElement) {
  //         const headerRect = headerElement?.getBoundingClientRect();
  //         const breadcrumbRect = breadcrumbElement?.getBoundingClientRect();

  //         // Check if mouse is on header or breadcrumb
  //         const isOnHeader =
  //           headerRect &&
  //           mouseY >= headerRect.top &&
  //           mouseY <= headerRect.bottom &&
  //           event.clientX >= headerRect.left &&
  //           event.clientX <= headerRect.right;

  //         const isOnBreadcrumb =
  //           breadcrumbRect &&
  //           mouseY >= breadcrumbRect.top &&
  //           mouseY <= breadcrumbRect.bottom &&
  //           event.clientX >= breadcrumbRect.left &&
  //           event.clientX <= breadcrumbRect.right;

  //         if (isOnHeader || isOnBreadcrumb) {
  //           // Mouse is on header - keep it visible and clear any hide timeout
  //           this.isMouseOnHeader = true;
  //           clearTimeout(this.headerTimeout);
  //           return;
  //         } else {
  //           // Mouse left header area - start hide timer
  //           this.isMouseOnHeader = false;
  //           this.startHideTimer();
  //         }
  //       }
  //     }
  //   }
  // }

  // @HostListener('mouseleave', ['$event'])
  // onMouseLeave(event: MouseEvent) {
  //   if (this.showHeaderAndNav && !this.isMouseOnHeader) {
  //     this.startHideTimer();
  //   }
  // }

  // private showHeader() {
  //   clearTimeout(this.headerTimeout);
  //   this.isHeaderVisible = true;
  //   this.isMouseOnHeader = false; // Reset mouse on header state
  // }

  // private startHideTimer() {
  //   clearTimeout(this.headerTimeout);
  //   this.headerTimeout = setTimeout(() => {
  //     if (!this.isMouseOnHeader) {
  //       this.isHeaderVisible = false;
  //     }
  //   }, this.HEADER_SHOW_DURATION);
  // }

  ngAfterViewInit() {
    this.drawerService.registerViewContainer(this.dialogHost);
  }

  ngOnInit(): void {
    const savedTheme = this.themeService.getCurrentTheme();
    this.themeService.setTheme(savedTheme);
    const authConfig: AuthConfig = {
      apiAuthUrl: environment.consoleApiAuthUrl,
      redirectUrl: environment.consoleRedirectUrl,
      postLoginRedirectUrl: '/build/agents',
      appName: 'console',
    };
    this.authService.setAuthConfig(authConfig);
    this.authTokenService.handleAuthCodeAndToken();
    this.authTokenService.startTokenCheck();
    if (!this.tokenStorage.getCookie('org_path')) {
      this.tokenStorage.setCookie(
        'org_path',
        'ascendion@core@genai@aava::201@202@203@204',
      );
    }
    this.router.events.subscribe((event) => {
      if (this.router.url === '/login') {
        this.showHeaderAndNav = false;
        //this.isHeaderVisible = false; // Ensure header is hidden on login page
      } else {
        this.showHeaderAndNav = true;
        //this.isHeaderVisible = true; // Show header on other pages
      }
      // Reset loader on navigation start
      if (event.constructor.name === 'NavigationStart') {
        this.loaderService.resetOnNavigation();
      }
    });
  }

  ngOnDestroy() {
    this.authTokenService.stopTokenCheck();
    // Clean up header timeout
    // if (this.headerTimeout) {
    //   clearTimeout(this.headerTimeout);
    // }
  }

  // Header event handlers
  onNavigation(route: string): void {
    console.log('Navigation to:', route);
  }

  onProfileAction(action: string): void {
    if (action === 'logout') {
      console.log('User logged out');
    }
  }

  onThemeToggle(theme: 'light' | 'dark'): void {
    this.themeService.setTheme(theme);
  }

  onOrgConfigChange(configData: any): void {
    console.log('Organization configuration changed:', configData);
    // Handle org path changes here if needed
  }

  onLanguageChange(languageCode: string): void {
    console.log('Language changed to:', languageCode);
    // Handle language changes here if needed - could integrate with i18n service
  }
}
