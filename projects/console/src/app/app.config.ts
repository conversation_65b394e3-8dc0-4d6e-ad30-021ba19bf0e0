import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';
import { MarkdownModule } from 'ngx-markdown';
import { importProvidersFrom } from '@angular/core';
import { environment } from '../environments/environment';
import { AGENT_ENVIRONMENT_CONFIG, ENVIRONMENT_CONFIG } from '@shared';
import {
  HTTP_INTERCEPTORS,
  provideHttpClient,
  withInterceptors,
  withInterceptorsFromDi,
} from '@angular/common/http';
import { routes } from './app.routes';
import { LoaderInterceptor } from './shared/interceptors/loader.interceptor';
import { AuthInterceptor } from '@shared/auth/interceptors/auth.interceptor';
import {
  LucideAngularModule,
  ShieldAlert,
  User,
  Settings,
  Info,
  ChevronLeft,
  ChevronRight,
  XCircle,
  CircleCheck,
  AlignVerticalDistributeStart,
  CircleCheckBig,
  Play,
  MoveLeft,
  CalendarDays,
  EllipsisVertical,
  SquarePen,
  Wifi,
  Search,
  AlertCircle,
  EyeOff,
  Mail,
  Phone,
  Check,
  X,
  Lock,
  Edit,
  Trash,
  Plus,
  Minus,
  Eye,
  Home,
  Layout,
  ChevronDown,
  ChevronUp,
  Bell,
  Grid,
  Star,
  Leaf,
  CheckCircle,
  AlertTriangle,
  XOctagon,
  Sparkles,
  Slash,
  Feather,
  Globe,
  Send,
  Box,
  Paperclip,
  Hourglass,
  Bot,
  Hammer,
  Archive,
  Copy,
  Trash2,
  TrendingUp,
  Users,
  Wrench,
  PanelLeft,
  BookOpen,
  NotebookText,
  Redo,
  RotateCcw,
  Swords,
  Undo,
  Image,
  Pencil,
  RotateCw,
  SendHorizontal,
  WandSparkles,
  MousePointer2,
  Hand,
  ZoomIn,
  ZoomOut,
  Clock,
  CircleX,
  FileText,
  Download,
  Save,
  RefreshCw,
  Redo2,
  Undo2,
  Bug,
  ShieldCheck,
  LibraryBig,
  Code,
  Workflow,
  Database,
  Funnel,
  ArrowLeft,
  Earth,
  LayoutGrid
} from 'lucide-angular';

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideHttpClient(withInterceptors([LoaderInterceptor])),
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
    provideHttpClient(withInterceptorsFromDi()),
    {
      provide: AGENT_ENVIRONMENT_CONFIG,
      useValue: {
        consoleApi: environment.consoleApi,
        consoleApiV2: environment.consoleApiV2
      }
    },
    {
      provide: ENVIRONMENT_CONFIG,
      useValue: {
        consoleApi: environment.consoleApi,
        consoleApiV2: environment.consoleApiV2,
        consoleEmbeddingApi: environment.consoleEmbeddingApi,
        consoleInstructionApi: environment.consoleInstructionApi,
        baseUrl: environment.consoleApi,
        apiUrl: environment.consoleApi
      }
    },
    importProvidersFrom(
      LucideAngularModule.pick({
        Undo2,
        Redo2,
        Hammer,
        User,
        Settings,
        Info,
        ChevronLeft,
        ChevronRight,
        ShieldAlert,
        Hourglass,
        CircleCheck,
        XCircle,
        AlignVerticalDistributeStart,
        CircleCheckBig,
        MoveLeft,
        Play,
        CalendarDays,
        EllipsisVertical,
        SquarePen,
        Wifi,
        Search,
        AlertCircle,
        EyeOff,
        Mail,
        Phone,
        Check,
        X,
        Lock,
        Edit,
        Trash,
        Plus,
        Minus,
        ChevronDown,
        ChevronUp,
        Eye,
        Home,
        Layout,
        Bell,
        Grid,
        Star,
        Leaf,
        CheckCircle,
        AlertTriangle,
        XOctagon,
        Sparkles,
        Slash,
        Feather,
        Globe,
        Send,
        Box,
        Paperclip,
        Bot,
        Archive,
        Copy,
        Trash2,
        Users,
        Wrench,
        TrendingUp,
        PanelLeft,
        BookOpen,
        NotebookText,
        Redo,
        RotateCcw,
        Swords,
        Undo,
        Image,
        Pencil,
        RotateCw,
        SendHorizontal,
        WandSparkles,
        MousePointer2,
        Hand,
        ZoomIn,
        ZoomOut,
        Clock,
        CircleX,
        FileText,
        Download,
        Save,
        RefreshCw,
        Bug,
        ShieldCheck,
        LibraryBig,
        Code,
        Workflow,
        Database,
        Funnel,
        ArrowLeft,
        Earth,
        LayoutGrid
      }),
      MarkdownModule.forRoot(),
    ),
  ],
};
