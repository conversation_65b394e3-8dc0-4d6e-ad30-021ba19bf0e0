import { Routes } from '@angular/router';
import { AuthGuard } from '@shared/auth/guards/auth.guard';
import { LoginComponent } from '@shared/auth/components/login/login.component';
import { CallbackComponent } from '@shared/auth/components/callback/callback.component';

export const routes: Routes = [
  { path: 'login', component: LoginComponent },
  { path: 'callback', component: CallbackComponent },

  {
    path: 'dashboard',
    canActivate: [AuthGuard],
    loadComponent: () =>
      import('./pages/dashboard/dashboard.component').then(
        (m) => m.DashboardComponent,
      ),
  },
  {
    path: 'chat-demo',
    canActivate: [AuthGuard],
    loadComponent: () =>
      import('./pages/chat-demo/chat-demo.component').then(
        (m) => m.ChatDemoComponent,
      ),
  },
  {
    path: 'build',
    canActivate: [AuthGuard],
    children: [
      {
        path: 'agents',
        loadComponent: () =>
          import('@shared/pages/agents/agents.component').then(
            (m) => m.AgentsComponent,
          ),
      },
      {
        path: 'agents/create',
        loadComponent: () =>
          import('./pages/create-agent/create-agent.component').then(
            (m) => m.CreateAgentComponent,
          ),
      },
      {
        path: 'agents/:type',
        loadComponent: () =>
          import('@shared/pages/agents/build-agents/build-agents.component').then(
            (m) => m.BuildAgentsComponent,
          ),
      },
      {
        path: 'agents/:type/execute',
        loadComponent: () =>
          import(
            '@shared/pages/agents/agent-execution/agent-execution.component'
          ).then((m) => m.AgentExecutionComponent),
      },
      {
        path: 'workflows',
        loadComponent: () =>
          import('@shared/pages/workflows/workflows.component').then(
            (m) => m.WorkflowsComponent,
          ),
      },
      {
        path: 'workflows/create',
        loadComponent: () =>
          import('@shared/pages/workflows/workflow-editor/workflow-editor.component').then(
            (m) => m.WorkflowEditorComponent,
          ),
      },
      {
        path: 'workflows/edit/:id',
        loadComponent: () =>
          import('@shared/pages/workflows/workflow-editor/workflow-editor.component').then(
            (m) => m.WorkflowEditorComponent,
          ),
      },
      {
        path: 'workflows/execute/:id',
        loadComponent: () =>
          import('@shared/pages/workflows/workflow-execution/workflow-execution.component').then(
            (m) => m.WorkflowExecutionComponent,
          ),
      },
      {
        path: '',
        redirectTo: '/dashboard',
        pathMatch: 'full',
      },
      {
        path: '**',
        redirectTo: '/login',
      },
    ],
  },
  {
    path: 'libraries',
    canActivate: [AuthGuard],
    children: [
      {
        path: 'prompts',
        loadComponent: () =>
          import('./pages/libraries/prompts/prompts.component').then(
            (m) => m.PromptsComponent,
          ),
      },
      {
        path: 'prompts/create',
        loadComponent: () =>
          import(
            './pages/libraries/prompts/create-prompts/create-prompts.component'
          ).then((m) => m.CreatePromptsComponent),
      },
      {
        path: 'prompts/edit/:id',
        loadComponent: () =>
          import(
            './pages/libraries/prompts/create-prompts/create-prompts.component'
          ).then((m) => m.CreatePromptsComponent),
      },
      {
        path: 'models',
        loadComponent: () =>
          import('./pages/libraries/models/models.component').then(
            (m) => m.ModelsComponent,
          ),
      },
      {
        path: 'models/create',
        loadComponent: () =>
          import(
            './pages/libraries/models/create-models/create-models.component'
          ).then((m) => m.CreateModelsComponent),
      },
      {
        path: 'models/edit/:id',
        loadComponent: () =>
          import(
            './pages/libraries/models/create-models/create-models.component'
          ).then((m) => m.CreateModelsComponent),
      },
      {
        path: 'knowledge-base',
        loadComponent: () =>
          import(
            './pages/libraries/knowledge-base/knowledge-base.component'
          ).then((m) => m.KnowledgeBaseComponent),
      },
      {
        path: 'knowledge-base/create',
        loadComponent: () =>
          import(
            './pages/libraries/knowledge-base/create-knowledge-base/create-knowledge-base.component'
          ).then((m) => m.CreateKnowledgeBaseComponent),
      },
      {
        path: 'knowledge-base/edit/:id',
        loadComponent: () =>
          import(
            './pages/libraries/knowledge-base/create-knowledge-base/create-knowledge-base.component'
          ).then((m) => m.CreateKnowledgeBaseComponent),
      },
      {
        path: 'tools',
        loadComponent: () =>
          import('./pages/libraries/tools/tools.component').then(
            (m) => m.ToolsComponent,
          ),
      },
      {
        path: 'tools/create',
        loadComponent: () =>
          import(
            './pages/libraries/tools/create-tools/create-tools.component'
          ).then((m) => m.CreateToolsComponent),
      },
      {
        path: 'tools/edit/:id',
        loadComponent: () =>
          import(
            './pages/libraries/tools/create-tools/create-tools.component'
          ).then((m) => m.CreateToolsComponent),
      },
      {
        path: 'tools/execute/:id',
        loadComponent: () =>
          import(
            './pages/libraries/tools/create-tools/create-tools.component'
          ).then((m) => m.CreateToolsComponent),
      },
      {
        path: 'tools/clone/:id',
        loadComponent: () =>
          import(
            './pages/libraries/tools/create-tools/create-tools.component'
          ).then((m) => m.CreateToolsComponent),
      },
      {
        path: 'guardrails',
        loadComponent: () =>
          import('./pages/libraries/guardrails/guardrails.component').then(
            (m) => m.GuardrailsComponent,
          ),
      },
      {
        path: 'guardrails/create',
        loadComponent: () =>
          import(
            './pages/libraries/guardrails/create-guardrails/create-guardrails.component'
          ).then((m) => m.CreateGuardrailsComponent),
      },
      {
        path: 'guardrails/edit/:id',
        loadComponent: () =>
          import(
            './pages/libraries/guardrails/create-guardrails/create-guardrails.component'
          ).then((m) => m.CreateGuardrailsComponent),
      },
      {
        path: '',
        redirectTo: '/libraries/prompts',
        pathMatch: 'full',
      },
    ],
  },
  {
    path: 'manage',
    canActivate: [AuthGuard],
    children: [
      {
        path: 'admin-management',
        loadChildren: () =>
          import(
            './pages/manage/admin-management/admin-management.routes'
          ).then((r) => r.ADMIN_MANAGEMENT_ROUTES),
      },
       {
        path: 'realm-management',
        loadChildren: () =>
          import('./pages/manage/realm-management/realm-management.routes').then(
            (r) => r.REALM_MANAGEMENT_ROUTES,
          ),
      },
      {
        path: '',
        redirectTo: '/admin-management',
        pathMatch: 'full',
      },
    ],
  },
  {
    path: 'approval',
    canActivate: [AuthGuard],
    loadComponent: () =>
      import('./pages/approval/approval.component').then(
        (m) => m.ApprovalComponent,
      ),
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'approval-agents',
      },
      {
        path: 'approval-agents',
        loadComponent: () =>
          import(
            './pages/approval/approval-agents/approval-agents.component'
          ).then((m) => m.ApprovalAgentsComponent),
      },
      {
        path: 'approval-tools',
        loadComponent: () =>
          import(
            './pages/approval/approval-tools/approval-tools.component'
          ).then((m) => m.ApprovalToolsComponent),
      },
      {
        path: 'approval-workflows',
        loadComponent: () =>
          import(
            './pages/approval/approval-workflows/approval-workflows.component'
          ).then((m) => m.ApprovalWorkflowsComponent),
      },
      {
        path: 'agents-preview-panel',
        loadComponent: () =>
          import(
            '@shared/pages/agents/build-agents/agents-preview-panel/agents-preview-panel.component'
          ).then((m) => m.AgentsPreviewPanelComponent),
      },
      {
        path: 'workflows-preview-panel',
        loadComponent: () =>
          import(
            '@shared/pages/workflows/workflow-editor/workflow-preview-panel/workflow-preview-panel.component'
          ).then((m) => m.WorkflowPreviewPanelComponent),
      },
    ],
  },
  {
    path: 'analytics',
    canActivate: [AuthGuard],
    loadComponent: () =>
      import('./pages/analytics/analytics.component').then(
        (m) => m.AnalyticsComponent,
      ),
  },
  {
    path: '',
    redirectTo: '/build/agents',
    pathMatch: 'full',
  },
  {
    path: '**',
    redirectTo: '/login',
  },
];
