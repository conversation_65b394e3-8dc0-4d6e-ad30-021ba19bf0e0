<div class="approval">
    <div class="approval-left-screen" [class.quick-actions-expanded]="quickActionsExpanded">
        <div class="quick-actions-wrapper" [class.expanded]="quickActionsExpanded">
            <div class="quick-actions-toggle" (click)="toggleQuickActions()">
                <div class="toggle-button">
                    <svg [class.rotate]="quickActionsExpanded" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M5 12h14M12 5l7 7-7 7" stroke="#6566CD" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" />
                    </svg>
                </div>
            </div>

            <!-- Expanded view with text labels -->
            <div class="quick-actions-content" *ngIf="quickActionsExpanded">
                <div class="action-buttons">
                    <button class="action-button" (click)="redirectToListOfApproval(labels.agents)">
                        <div class="action-icon">
                            <img [src]="'svgs/icons/' + 'awe_agents' + '.svg'" [alt]="labels.agents" width="24" height="24" title="{{labels.agents}}" />
                        </div>
                        <span class="action-label">{{labels.agents}}</span>
                    </button>
                    
                    <button class="action-button" (click)="redirectToListOfApproval(labels.workflows)">
                        <div class="action-icon">
                            <img [src]="'svgs/icons/' + 'awe_workflows' + '.svg'" [alt]="labels.workflows" width="24" height="24" title="{{labels.workflows}}" />
                        </div>
                        <span class="action-label">{{labels.workflows}}</span>
                    </button>
                    
                    <button class="action-button" (click)="redirectToListOfApproval(labels.tools)">
                        <div class="action-icon">
                            <img [src]="'svgs/icons/' + 'awe_tools' + '.svg'" [alt]="labels.tools" width="24" height="24" title="{{labels.tools}}" />
                        </div>
                        <span class="action-label">{{labels.tools}}</span>
                    </button>
                </div>
            </div>

            <!-- Collapsed view with icons only -->
            <div class="quick-actions-icons" *ngIf="!quickActionsExpanded">
                <button class="icon-button" (click)="redirectToListOfApproval(labels.agents)" [title]="labels.agents" title="{{labels.agents}}">
                    <div class="icon-wrapper">
                        <img [src]="'svgs/icons/' + 'awe_agents' + '.svg'" [alt]="labels.agents" width="24" height="24" title="{{labels.agents}}" />
                    </div>
                </button>
                 <button class="icon-button" (click)="redirectToListOfApproval(labels.workflows)" [title]="labels.workflows" title="{{labels.workflows}}">
                    <div class="icon-wrapper">
                        <img [src]="'svgs/icons/' + 'awe_workflows' + '.svg'" [alt]="labels.workflows" width="24" height="24" title="{{labels.workflows}}" />
                    </div>
                </button>
                <button class="icon-button" (click)="redirectToListOfApproval(labels.tools)" [title]="labels.tools" title="{{labels.tools}}">
                    <div class="icon-wrapper">
                        <img [src]="'svgs/icons/' + 'awe_tools' + '.svg'" [alt]="labels.tools" width="24" height="24" title="{{labels.tools}}" />
                    </div>
                </button>
            </div>
        </div>
    </div>
    
    <div class="approval-right-screen">
        <router-outlet></router-outlet>
    </div>
</div>

<ava-popup
  [show]="showToolApprovalPopup"
  [title]="labels.confirmApproval"
  [message]="labels.youAreAboutToApproveThis + ' ' + currentTab + '. ' + labels.itWillBeActiveAndAvailableIn + ' ' + currentTab + ' ' + labels.catalogueForUsersToExecute"
  [showClose]="true"
  [showCancel]="true"
  [showConfirm]="true"
  [confirmButtonLabel]="labels.approve"
  [confirmButtonVariant]="'primary'"
  [confirmButtonBackground]="'#dc3545'"
  (confirm)="handleApproval()"
  (cancel)="showToolApprovalPopup=false"
  (closed)="showToolApprovalPopup=false"
>
</ava-popup>

<ava-popup messageAlignment="center" [show]="showInfoPopup"
    title="SUCCESS!" message={{infoMessage}} [showHeaderIcon]="true"
    headerIconName="circle-check" iconColor="green" [showClose]="true" (closed)="handleInfoPopup()">
</ava-popup>

<ava-confirmation-popup [show]="showFeedbackPopup" title="Confirm Send Back"
    message="This {{currentTab}} will be send back for corrections and modification. Kindly comment what needs to be done."
    confirmationLabel="Send Back" (closed)="showFeedbackPopup = false" (confirm)="handleRejection($event)">
</ava-confirmation-popup>

<ava-popup messageAlignment="center" [show]="showErrorPopup"
    title="FAILED!" message={{infoMessage}} [showHeaderIcon]="true"
    headerIconName="circle-x" iconColor="red" [showClose]="true" (closed)="handleInfoPopup()" >
</ava-popup>