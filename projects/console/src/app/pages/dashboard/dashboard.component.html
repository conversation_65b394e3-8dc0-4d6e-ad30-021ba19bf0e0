<div id="dasboard-container" class="container-fluid">
  <div id="dashboard-cards-container" class="row">
    <div *ngFor="let details of dashboardDetails" class="dashboard-cards col-3">
      <ava-text-card [type]="'default'" [iconName]="'hourglass'" [title]="details.title" [value]="details.value"
        [description]="details.subtitle"></ava-text-card>
    </div>
  </div>
  <div id="dasborad-bottom-container" class="row">
    <div id="quick-action-container" class="col-1">
      <app-quick-actions [actions]="quickActions" (actionClick)="onQuickActionClick($event)">
      </app-quick-actions>
    </div>
    <div id="high-prioirty-container" class="col-7 ">
      <div class="box-wrapper high-priority-wrapper">
        <div class="col-12 active-monitoring-text header-section">
          <span >
            Priority Approvals
          </span>

          <ava-link label="View All" color="primary"></ava-link>
        </div>

        <!-- <ava-approval-card height="300">
              <div header>
                    <ava-icon iconSize="20" iconName="ellipsis-vertical"></ava-icon>
                    <div class="header">
                        <h2>Autonomous Systems.... Agent1</h2>
                        <ava-tag label="High" color="error" size="sm"></ava-tag>
                        <ava-tag label="Agent" color="info" size="sm"></ava-tag>
                    </div>
              </div>
              <div content class="a-content">
                  <div class="box tag-wrapper">
                      <ava-tag label="Individual" size="sm"></ava-tag>
                      <ava-tag label="Ascendion" size="sm"></ava-tag>
                      <ava-tag label="Digital Ascender" size="sm"></ava-tag>
                      <ava-tag label="Platform Engineering" size="sm"></ava-tag>
                  </div>

                  <div class="box info-wrapper">
                      <div class="f">
                          <ava-icon iconSize="13" iconName="user"></ava-icon>
                          <span>{{'<EMAIL>'}}</span>
                      </div>
                      <div class="ml-auto s">
                          <ava-icon iconSize="20" iconName="calendar-days"></ava-icon>
                          <span>12 May 2025</span>
                      </div>
                  </div>
              </div>
              <div footer>
              <div class="footer-content">
                  <div class="footer-left">
                      <span class="ex">Execution Status</span>
                      <div>
                          <ava-icon iconSize="20" iconName="circle-check-big"></ava-icon>
                          <span>Execution was Successful</span>
                      </div>

                  </div>
                  <div class="footer-right">
                      <ava-button label="Test" (userClick)="uClick($event)" variant="secondary" size="small" [customStyles]="{
                        'border': '2px solid transparent',
                        'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                        'background-origin': 'border-box',
                        'background-clip': 'padding-box, border-box',
                        '--button-effect-color': '33, 90, 214'
                      }"
                      state="default" iconName="play" iconPosition="left"></ava-button>
                      <ava-button label="Sendback" (userClick)="uClick($event)" variant="secondary" size="small" [customStyles]="{
                        'border': '2px solid transparent',
                        'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                        'background-origin': 'border-box',
                        'background-clip': 'padding-box, border-box',
                        '--button-effect-color': '33, 90, 214'
                      }"
                      state="default" iconName="move-left" iconPosition="left"></ava-button>
                      <ava-button label="Approve" (userClick)="uClick($event)" variant="primary" size="small" [customStyles]="{
                        background:
                          'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                        '--button-effect-color': '33, 90, 214',
                      }"
                      state="default" iconName="Check" iconPosition="left"></ava-button>
                  </div>
              </div>
          </div>
          </ava-approval-card> -->

        @for (item of workflowApprovals; track $index){
        <div class="approval-card-wrapper">
          <ava-approval-card height="300">
            <div header>
              <ava-icon iconSize="20" iconName="ellipsis-vertical"></ava-icon>
              <div class="header">
                <h2>{{item.session1.title}}</h2>
                <ava-tag label="workflow" color="info" size="sm"></ava-tag>
              </div>
            </div>
            <div content class="a-content">
              <div class="box tag-wrapper">
                <ava-tag label="Individual" size="sm"></ava-tag>
                <ava-tag label="Ascendion" size="sm"></ava-tag>
                <ava-tag label="Digital Ascender" size="sm"></ava-tag>
                <ava-tag label="Platform Engineering" size="sm"></ava-tag>
              </div>

              <div class="box info-wrapper">
                <div class="f">
                  <ava-icon iconSize="13" iconName="user"></ava-icon>
                  <span>{{item.session3[0].label}}</span>
                </div>
                <div class="ml-auto s">
                  <ava-icon iconSize="20" iconName="calendar-days"></ava-icon>
                  <span>{{item.session3[1].label}}</span>
                </div>
              </div>
            </div>
            <div footer>
              <div class="footer-content">
                <div class="footer-left">
                  <span class="ex">Execution Status</span>
                  <div>
                    <ava-icon iconSize="20" iconName="circle-check-big"></ava-icon>
                    <span>{{item?.session4.status}}</span>
                  </div>

                </div>
                <div class="footer-right">
                                <ava-button [label]="labels.view" (userClick)="uClick($index)" variant="secondary" size="small" [customStyles]="{
                                    'border': '2px solid transparent',
                                    'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                                    'background-origin': 'border-box',
                                    'background-clip': 'padding-box, border-box',
                                    '--button-effect-color': '33, 90, 214'
                                  }"
                                    state="default" ></ava-button>
                                <!-- <ava-button [label]="labels.sendback" (userClick)="rejectApproval($index)" variant="secondary" size="small" [customStyles]="{
                                    'border': '2px solid transparent',
                                    'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                                    'background-origin': 'border-box',
                                    'background-clip': 'padding-box, border-box',
                                    '--button-effect-color': '33, 90, 214'
                                  }"
                                    state="default" iconName="move-left" iconPosition="left"></ava-button>
                                <ava-button [label]="labels.approve" (userClick)="approveApproval($index)" variant="primary" size="small" [customStyles]="{
                                    background:
                                      'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                                    '--button-effect-color': '33, 90, 214',
                                  }"
                                    state="default" iconName="Check" iconPosition="left"></ava-button> -->
                            </div>
              </div>
            </div>
          </ava-approval-card>
        </div>
        }
      </div>

      <!-- <ava-approval-card
        height="auto"
        [contentTemplate]="footerTemplate"
        [cardData]="approvalCardData"
        [contentsBackground]="'#fff'"
        [cardContainerBackground]="'#F8F8F8'"
      >
      </ava-approval-card> -->


    </div>
    <div id="activity-monitoring-container" class="col-4">
      <div class="box-wrapper active-monitoring-wrapper">
        <div class="row active-monitoring">
          <div class="col-5 active-monitoring-text">Active Monitoring</div>
          <div class="col-5">

            <ava-dropdown dropdownTitle="Active Monitoring" [options]="options" [search]="false"
              (selectionChange)="onSelectionChange($event)">
            </ava-dropdown>
          </div>
          <div class="col-2">
            <ava-link label="View All" href="/approval" routerLink="/approval"  color="primary"></ava-link>
          </div>  
        </div>
        <div class="activity-monitoring-card-container" *ngFor="let activity of activityMonitoring">
          <div class="activity-monitoring-card">
            <div class="top-section row">
              <div class="col-7">
                <p class="fw-600">{{ activity.agentName }}</p>
              </div>
              <div class="col-5 font-12 activity-monitoring-status-label">
                <span class="active fw-600">
                  {{ activity.status }}
                </span>
                <span class="activity-monitoring-status-dot active"></span>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="activity-monitoring-user col-4 font-12">
              <ava-icon iconName="user" iconSize="16"></ava-icon>
              {{ activity.user }}
            </div>
            <div class="activity-monitoring-date col-4 font-12">
              <ava-icon iconName="calendar-days" iconSize="16"></ava-icon>
              {{ activity.date }}
            </div>
            <div class="activity-monitoring-runs col-4 font-12">
              Total Runs - {{ activity.totalRuns }}
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>

<!-- <div id="dashboard-container" class="container-fluid">
  <div class="dashboard-row">
    <div class="img-card mb-4">
      <ava-image-card
        [imageUrl]="'robot.svg'"
        [name]="'Shouvik Mazumdar'"
        [title]="'Welcome to Console 🚀'"
      ></ava-image-card>
    </div>
    <div *ngFor="let details of dashboardDetails" class="dashboard-cards mb-4">
      <ava-text-card
        [type]="'default'"
        [iconName]="'hourglass'"
        [title]="details.title"
        [value]="details.value"
        [description]="'Agents which are approved'"
      ></ava-text-card>
    </div>
  </div>

  <div class="dashboard-main-row">
    <div class="quick-actions-col" [class.collapsed]="!quickActionsExpanded">
      <div
        class="quick-actions-wrapper"
        [class.expanded]="quickActionsExpanded"
      >
        <div class="quick-actions-toggle" (click)="toggleQuickActions()">
          <div class="toggle-button">
            <svg
              [class.rotate]="quickActionsExpanded"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                d="M5 12h14M12 5l7 7-7 7"
                stroke="#6566CD"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
          <span *ngIf="quickActionsExpanded">Quick Actions</span>
        </div>
        <div class="quick-actions-content" *ngIf="quickActionsExpanded">
          <div class="action-buttons">
            <ava-button
              *ngFor="let action of quickActions"
              [label]="action.label"
              variant="primary"
              size="large"
              [pill]="true"
              iconPosition="left"
            >
              <span slot="icon-left" class="button-icon">
                <img
                  src="svgs/icons/awe_scanner.svg"
                  alt="Download"
                  width="20"
                  height="20"
                  style="display: inline-block; vertical-align: middle"
                />
              </span>
            </ava-button>
          </div>
        </div>
        <div class="quick-actions-icons" *ngIf="!quickActionsExpanded">
          <ava-button
            *ngFor="let action of quickActions"
            [pill]="true"
            iconPosition="only"
          >
            <span slot="icon-left" class="button-icon">
              <img
                [src]="'svgs/icons/' + action.icon + '.svg'"
                [alt]="action.label"
                width="24"
                height="24"
                style="display: inline-block; vertical-align: middle"
              />
            </span>
          </ava-button>
        </div>
      </div>
    </div>
    <div class="approval-card-col" id="high-prioirty-card-container">
      <ng-template let-i="index" let-label="label" #footerTemplate>
        <div class="footer-content">
          <ng-container *ngIf="label">
            <div class="footer-left">
              <ava-icon iconSize="20" [iconName]="label.iconName"></ava-icon>
              <span>{{ label.status }}</span>
            </div>
          </ng-container>
          <div class="footer-right">
            <ava-button
              label="Test"
              (userClick)="uClick(i)"
              variant="secondary"
              size="medium"
              state="default"
              iconName="play"
              iconPosition="left"
            ></ava-button>
            <ava-button
              label="Send Back"
              (userClick)="uClick(i)"
              variant="secondary"
              size="medium"
              state="default"
              iconName="move-left"
              iconPosition="left"
            ></ava-button>
            <ava-button
              label="Approve"
              (userClick)="uClick(i)"
              variant="primary"
              size="medium"
              state="default"
              iconName="Check"
              iconPosition="left"
            ></ava-button>
          </div>
        </div>
      </ng-template>

      <ava-approval-card
        height="auto"
        [contentTemplate]="footerTemplate"
        [cardData]="approvalCardData"
        [contentsBackground]="'#fff'"
        [cardContainerBackground]="'#F8F8F8'"
      >
      </ava-approval-card>
    </div>
    <div class="activity-monitoring-col">
      <div class="activity-monitoring-card">
        <div class="activity-monitoring-header">
          <span class="activity-monitoring-title">Activity Monitoring</span>
          <div class="activity-monitoring-controls">
            <select class="activity-monitoring-dropdown">
              <option>Agents</option>
            </select>
            <button class="activity-monitoring-viewall">View All</button>
          </div>
        </div>
        <div class="activity-monitoring-list">
          <div
            class="activity-monitoring-item"
            *ngFor="let activity of activityMonitoring"
          >
            <div class="activity-monitoring-info">
              <div class="d-flex">
              <div class="activity-monitoring-agent">
                {{ activity.agentName }}
              </div>
              <div class="activity-monitoring-status">
                <span class="activity-monitoring-status-label active">{{
                  activity.status
                }}</span>
                <span class="activity-monitoring-status-dot active"></span>
              </div>
              </div>
              <div class="activity-monitoring-meta">
                <span class="activity-monitoring-user">
                  <ava-icon iconName="user" iconSize="16"></ava-icon>
                  {{ activity.user }}
                </span>
                <span class="activity-monitoring-date">
                  <ava-icon iconName="calendar-days" iconSize="16"></ava-icon>
                  {{ activity.date }}
                </span>
                <span class="activity-monitoring-runs">
                  Total Runs - {{ activity.totalRuns }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div> -->
<!-- </div> -->