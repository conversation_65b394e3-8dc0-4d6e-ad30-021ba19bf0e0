#dasboard-container {
  margin-top: 3rem;
}

#dasborad-bottom-container {
  margin-top: 2rem;
}

#activity-monitoring-container {
  background: #f8f8f8;
  border-radius: 24px;
  height: 30rem;
  overflow: auto;
}

.activity-monitoring-card-container {
  background: var(--Global-colors-White-White, #ffffff);
  border: 1px solid var(--Brand-Neutral-n-200, #bbbec5);
  margin: 1rem;
  padding: 1rem;
  border-radius: 16px;
}

.fw-600 {
  font-weight: 600;
}

.activity-monitoring-status-label {
  text-align: end;
}

.font-12 {
  font-size: 12px;
}

.active-monitoring-text {
  font-size: 24px;
  font-weight: 700;
}

.active-monitoring {
  padding-top: 24px;
}

::ng-deep .ava-dropdown {
  width: 100% !important;
}

#dasborad-bottom-container {
  height: 30rem;
}

#high-prioirty-container,
#activity-monitoring-container {
  height: 100%;
  overflow-y: auto;
}

#high-prioirty-container {
  .high-priority-wrapper {
    padding: 12px;
  }
}

.box-wrapper {
  border-radius: 24px;
  border: 1px solid #dcdcdc;
  background: #f8f8f8;
  &.active-monitoring-wrapper {
    padding-left: 24px;
    padding-right: 24px;
    ava-dropdown {
      position: relative;
      top: -3px;
    }
  }
}

.create-type {
  .ava-card-container .ava-card {
    &.card {
      &::before {
        left: 16px;
      }
      &::after {
        right: -16px;
      }
    }
  }
}

.approval-card-wrapper {
  margin: 10px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
