import {
  Component,
  OnInit,
  ViewEncapsulation,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, FormControl, Validators, ValidatorFn, AbstractControl, ValidationErrors } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { ModelService } from '../../../../shared/services/model.service';
import { AvaTextboxComponent, AvaTextareaComponent, ButtonComponent, DropdownComponent, DropdownOption, ListComponent, ListItem, PopupComponent } from '@ava/play-comp-library';
import modelText from '../constants/models.json';
import { LucideAngularModule } from 'lucide-angular';
@Component({
  selector: 'app-create-models',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    LucideAngularModule,
    ListComponent,
    AvaTextboxComponent,
    ButtonComponent,
    AvaTextareaComponent,
    DropdownComponent,
    PopupComponent,
  ],
  templateUrl: './create-models.component.html',
  styleUrls: ['./create-models.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class CreateModelsComponent implements OnInit {
  iconName = 'info';
  showSuccessPopup = false;
  submissionSuccess = false;
  popupTitle: string = '';
  popupMessage: string = ''
  modelId: string | null = null;
  isEditMode: boolean = false;
  modelForm: FormGroup;
  aiEngines: ListItem[] = [];
  modelDropdownOptions: DropdownOption[] = [];
  amazonDropdownOptions: DropdownOption[] = [];
  googleDropdownOptions: DropdownOption[] = [];
  daDropdownOptions: DropdownOption[] = [];
  bnyDropdownOptions: DropdownOption[] = [];
  modelConfigurationOptions: DropdownOption[] = [];
  apiVersionOptions: DropdownOption[] = [];
  modelNames: ListItem[] = [];
  selectedAiEngineId: string | null = null;
  selectedModelId: string | null = null;
  modelTypeList: ListItem[] = [];
  selectedModelTypeId: string | null = null;

  public inputVisibility = {
    AzureOpenAI: false,
    AmazonBedrock: false,
    GoogleAI: false,
    DaOpenSourceAI: false,
    BNY: false,
  };
  public mode: 'view' | 'add' = 'add';
  public labels: any = modelText.labels;
  dropdownConfigs: {
    label: string;
    targetKey: keyof CreateModelsComponent;
    mapToListItem?: boolean;
    includeInactive?: boolean;
  }[] = [
      { label: 'AI Engine', targetKey: 'aiEngines', mapToListItem: true },
      { label: 'AzureOpenAI Model', targetKey: 'modelDropdownOptions' },
      { label: 'AmazonBedrock Model', targetKey: 'amazonDropdownOptions' },
      { label: 'GoogleAI Model', targetKey: 'googleDropdownOptions' },
      { label: 'DaOpenSourceAI Model', targetKey: 'daDropdownOptions' },
      { label: 'BNY Model', targetKey: 'bnyDropdownOptions' },

      { label: 'Model Type', targetKey: 'modelTypeList', mapToListItem: true },
      { label: 'Api Version', targetKey: 'apiVersionOptions', includeInactive: true },
    ];
  isLeftCollapsed = false;

  toggleLeftPanel() {
    this.isLeftCollapsed = !this.isLeftCollapsed;
  }
  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private http: HttpClient,
    private modelService: ModelService,
    private cdr: ChangeDetectorRef,
  ) {
    this.modelForm = this.fb.group({
      modelDeploymentName: ['', [Validators.required, this.noSpacesValidator]],
      modelDescription: ['', [Validators.required, this.noLeadingSpaceValidator]],
      description: ['', this.noSpacesValidator],
      modelType: ['', Validators.required],
      model: [''],
      organization: ['', this.noSpacesValidator],
      domain: ['', this.noSpacesValidator],
      project: ['', this.noSpacesValidator],
      team: ['', this.noSpacesValidator],
      baseurl: ['', [Validators.required, this.noSpacesValidator]],
      llmDeploymentName: ['', this.noSpacesValidator],
      apiKey: ['', this.noSpacesValidator],
      apiVersion: ['', this.noSpacesValidator],
      awsAccessKey: ['', this.noSpacesValidator],
      awsSecretKey: ['', this.noSpacesValidator],
      awsRegion: ['', this.noSpacesValidator],
      bedrockModelId: ['', this.noSpacesValidator],
      gcpProjectId: ['', this.noSpacesValidator],
      gcpLocation: ['', this.noSpacesValidator],
      vertexAIEndpoint: ['', this.noSpacesValidator],
      serviceUrl: ['', this.noSpacesValidator],
      apiKeyEncoded: ['', this.noSpacesValidator],
      headerName: ['', this.noSpacesValidator],
      aiEngine: ['', Validators.required],
    });

  }
  noSpacesValidator = (control: AbstractControl): ValidationErrors | null => {
    if (control.value && control.value.toString().includes(' ')) {
      return { containsSpaces: true };
    }
    return null;
  };
  urlValidator(control: AbstractControl): ValidationErrors | null {
    if (control.value && !/^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/.test(control.value)) {
      return { invalidUrl: true };
    }
    return null;
  }
  noLeadingSpaceValidator(control: AbstractControl): ValidationErrors | null {
    const value = control.value;

    if (typeof value === 'string' && value.length > 0 && value.startsWith(' ')) {
      return { leadingSpace: true };
    }
    return null;
  }
  // Lifecycle hook to initialize form and fetch dropdown values
  ngOnInit(): void {
    this.modelId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.modelId;
    this.dropdownConfigs.forEach(({ label, targetKey, mapToListItem, includeInactive }) => {
      this.modelService.getDropdownOptions(label, includeInactive).subscribe({
        next: (options) => {
          const mapped = mapToListItem
            ? options.map((o) => ({ id: o.value, title: o.label }))
            : options.map((o) => ({ name: o.label, value: o.value }));
          (this[targetKey] as any) = mapped;
          this.cdr.detectChanges();
        },
        error: (error) => console.error(`Error fetching ${label}:`, error),
      });
    });
    if (this.isEditMode && this.modelId) {
      this.loadModelData(this.modelId);
      this.modelForm.disable();
    }
  }
  // Load model data by ID and patch form values
  loadModelData(modelId: string): void {
    this.mode = 'view';
    this.modelService.getOneModeById(modelId).subscribe({
      next: (modelData: any) => {
        this.selectedAiEngineId = modelData.aiEngine;
        this.selectedModelId = modelData.model;
        this.selectedModelTypeId = modelData.modelType;
        this.onAiEngineSelected({ id: modelData.aiEngine, title: '' });
        setTimeout(() => {
          this.modelForm.patchValue({
            modelDeploymentName: modelData.modelDeploymentName,
            description: modelData.modelDescription || modelData.description,
            modelType: modelData.modelType,
            modelDescription: modelData.modelDescription,
            aiEngine: modelData.aiEngine,
            model: modelData.model,
            baseurl: modelData.baseurl,
            llmDeploymentName: modelData.llmDeploymentName,
            apiKey: modelData.apiKey,
            apiVersion: modelData.apiVersion,
            awsAccessKey: modelData.awsAccessKey,
            awsSecretKey: modelData.awsSecretKey,
            awsRegion: modelData.awsRegion,
            bedrockModelId: modelData.bedrockModelId,
            gcpProjectId: modelData.gcpProjectId,
            gcpLocation: modelData.gcpLocation,
            vertexAIEndpoint: modelData.vertexAIEndpoint,
            serviceUrl: modelData.serviceUrl,
            apiKeyEncoded: modelData.apiKeyEncoded,
            headerName: modelData.headerName,
            organization: modelData.organization,
            domain: modelData.domain,
            project: modelData.project,
            team: modelData.team,
          });
          this.cdr.detectChanges();
          if (this.mode === 'view') {
            this.modelForm.disable();
          }
        }, 100);
      },
      error: (error: any) => {
        console.error('Error loading model data:', error);
      },
    });
  }
  areSelectionsComplete(): boolean {
    return !!this.selectedAiEngineId && !!this.selectedModelTypeId && !!this.selectedModelId;
  }
  // Handle AI engine selection and update related fields and dropdowns
  onAiEngineSelected(selectedItem: ListItem): void {
    this.selectedAiEngineId = selectedItem.id;
    const selectedEngine = selectedItem.id;
    if (this.mode !== 'view') {
      this.selectedModelId = null;
      this.modelForm.patchValue({
        aiEngine: selectedEngine,
        model: null
      }, { emitEvent: false });
    }
    if (selectedEngine) {
      this.displayInputField(selectedEngine);
      this.updateEngineFieldValidators(selectedEngine);
      if (this.selectedModelTypeId) {
        this.fetchModelsForTypeAndEngine(this.selectedModelTypeId, selectedEngine);
      } else {
        this.modelNames = [];
      }
    }
  }
  // Handle Model Type selection
  onModelTypeSelected(selectedItem: ListItem): void {
    this.selectedModelTypeId = selectedItem.id;
    if (this.mode !== 'view') {
      this.selectedModelId = null;
      this.modelForm.patchValue({
        modelType: selectedItem.id,
        model: null
      }, { emitEvent: false });
    }
    if (this.selectedAiEngineId) {
      this.fetchModelsForTypeAndEngine(selectedItem.id, this.selectedAiEngineId);
    } else {
      this.modelNames = [];
    }
  }
  // Handle Model selection
  onModelSelected(selectedItem: ListItem): void {
    this.selectedModelId = selectedItem.id;
    this.modelForm.patchValue({ model: selectedItem.id }, { emitEvent: false });
  }
  // Fetch models using the API refdata endpoint for the selected engine
  fetchModelsForTypeAndEngine(modelType: string, aiEngine: string): void {
    // Map engine to ref_key
    const engineRefKeyMap: { [key: string]: string } = {
      AzureOpenAI: 'AzureOpenAI Model',
      AmazonBedrock: 'AmazonBedrock Model',
      GoogleAI: 'GoogleAI Model',
      DaOpenSourceAI: 'DaOpenSourceAI Model',
      BNY: 'BNY Model',
      DatabricksAI: 'DatabricksAI Model',
    };
    const refKey = engineRefKeyMap[aiEngine];
    if (!refKey) {
      this.modelNames = [];
      return;
    }
    this.modelService.getDropdownOptions(refKey, false, true).subscribe({
      next: (modelGroups: any[]) => {
        const group = modelGroups.find(g => g.type === modelType);
        this.modelNames = group && group.models
          ? group.models.map((m: any) => ({ id: m.id, title: m.name }))
          : [];
        this.cdr.detectChanges();
      },
      error: (error) => {
        this.modelNames = [];
        console.error('Error fetching models:', error);
      }
    });
  }
  // Get error message for a specific form field
  getFieldError(fieldName: string): string {
    const field = this.modelForm.get(fieldName);
  
    const customLabels: Record<string, string> = {
      modelDeploymentName: 'Model Name',
      modelDescription: 'Description',
      baseurl: 'Base URL',
      llmDeploymentName: 'LLM Deployment Name',
      apiKey: 'API Key Encoded',
      apiVersion: 'API Version',
      awsAccessKey: 'AWS Access Key',
      awsSecretKey: 'AWS Secret Key',
      awsRegion: 'AWS Region',
      bedrockModelId: 'Bedrock Model Id',
      gcpProjectId: 'GCP Project Id',
      gcpLocation: 'GCP Location',
      vertexAIEndpoint: 'VertexAI Endpoint',
      serviceUrl: 'Service URL',
      apiKeyEncoded: 'API Key Encoded',
      headerName: 'Header Name',
      modelType: 'Model Type'
    };
    const formattedFieldName = customLabels[fieldName] || fieldName;
    if (field && field.invalid && (field.touched || field.dirty)) {
      if (field.errors?.['required']) {
        return `${formattedFieldName} is required`;
      }
      if (field.errors?.['minlength']) {
        return `${formattedFieldName} must be at least ${field.errors['minlength'].requiredLength} characters long`;
      }
      if (field.errors?.['invalidUrl']) {
        return `Please enter a valid URL`;
      }
      if (field.errors?.['containsSpaces']) {
        return `${formattedFieldName} cannot contain spaces`;
      }
      if (field.errors?.['leadingSpace']) {
        return `${formattedFieldName} cannot contain spaces`;
      }
    }
    return '';
  }
  
  // Update form control when model type changes
  onModelTypeChange(event: any) {
    const selectedType = event.selectedValue;
    if (selectedType) {
      const selectedOption = this.modelConfigurationOptions.find(option => option.name === selectedType);
      const typeValue = selectedOption ? selectedOption.value : selectedType;
      this.modelForm.patchValue({
        modelType: typeValue
      }, { emitEvent: false });
    }
  }
  // Show or hide engine-specific input fields and reset values
  displayInputField(data: string) {
    // Reset visibility first
    Object.keys(this.inputVisibility).forEach((key) => {
      this.inputVisibility[key as keyof typeof this.inputVisibility] = false;
    });
    // Show only the selected engine's fields
    if (this.inputVisibility.hasOwnProperty(data)) {
      this.inputVisibility[data as keyof typeof this.inputVisibility] = true;
    }
    // Reset form fields specific to other engines to avoid cross-engine data leakage
    const engineFieldsMap: Record<string, string[]> = {
      AzureOpenAI: ['baseurl', 'llmDeploymentName', 'apiKey', 'apiVersion'],
      AmazonBedrock: ['awsAccessKey', 'awsSecretKey', 'awsRegion', 'bedrockModelId'],
      GoogleAI: ['gcpProjectId', 'gcpLocation', 'vertexAIEndpoint'],
      DaOpenSourceAI: ['serviceUrl', 'apiKeyEncoded', 'headerName'],
      BNY: [] // Add fields if needed
    };
    // Clear all engine-specific fields
    Object.values(engineFieldsMap).flat().forEach(field => {
      this.modelForm.get(field)?.reset();
    });
    // Mark required fields as untouched (optional, for cleaner UX)
    this.modelForm.markAsUntouched();
  }

  // Update the model name dropdown based on selected AI engine
  updateModelNameOptions(engine: string): void {
    // No longer needed with new API response, keep empty or remove
    this.modelNames = [];
  }
   iconColor: string = '#28a745'
  // Save model if form is valid and prepare API payload
  onSave(): void {
    if (this.modelForm.valid) {
      const formValues = this.modelForm.value;
      const payload = Object.keys(formValues).reduce((acc, key) => {
        const value = formValues[key];
        if (value !== null && value !== undefined && value !== '') {
          acc[key] = value;
        }
        return acc;
      }, {} as any);
      if (!(this.selectedAiEngineId === 'GoogleAI' && this.selectedModelTypeId === 'Embedding')) {
      delete payload.vertexAIEndpoint;
    }
      if (this.isEditMode && this.modelId) {
      } else {
        this.modelService.saveModel(payload).subscribe({
          next: (info) => {
            this.iconName = "circle-check";
            this.popupMessage = info?.info?.message || info.message;
            this.showSuccessPopup = true;
            this.submissionSuccess = true;
            this.iconColor = "#28a745";
          },
          error: (error) => {
            this.iconName = "info";
            this.popupMessage = error?.error?.message || error.message;
            this.showSuccessPopup = true;
            this.submissionSuccess = false;
            this.iconColor = "#dc3545";
          },
        });
      }
    } else {
      this.markFormGroupTouched();
    }
  }
  // Mark all form fields as touched to trigger validation
  markFormGroupTouched() {
    Object.keys(this.modelForm.controls).forEach((key) => {
      const control = this.modelForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }
  // Reset form and navigate on cancel
  onCancel(): void {
    if (this.mode === 'add') {
      this.modelForm.reset({
        aiEngine: '',
        modelType: 'Generative',
      });
      Object.keys(this.inputVisibility).forEach((key) => {
        this.inputVisibility[key as keyof typeof this.inputVisibility] = false;
      });
    }
    this.router.navigate(['/libraries/models']);
  }
  // Return specific form control by name
  getControl(name: string): FormControl {
    return this.modelForm.get(name) as FormControl;
  }
  // Getter for all form controls
  get formControls() {
    return this.modelForm.controls;
  }
  // Getter for AI engine control
  get aiEngine() {
    return this.formControls['aiEngine'];
  }
  // Check if any input section should be shown based on engine
  get shouldDisplayInput(): boolean {
    return (
      this.aiEngine.value === 'AzureOpenAI' ||
      this.aiEngine.value === 'AmazonBedrock' ||
      this.aiEngine.value === 'GoogleAI' ||
      this.aiEngine.value === 'DaOpenSourceAI' ||
      this.aiEngine.value === 'BNY' ||
      this.mode === 'view'
    );
  }
  // Handle success popup confirmation
  onSuccessConfirm(): void {
    this.closeSuccessPopup();
  }
  // Hide success popup and navigate if successful
  closeSuccessPopup(): void {
    this.showSuccessPopup = false;
    this.popupTitle = '';
    this.popupMessage = '';
    if (this.submissionSuccess) {
      this.router.navigate(['/libraries/models']);
    }
  }
  // Set validators dynamically for selected engine fields
  updateEngineFieldValidators(selectedEngine: string): void {
    const engineFieldMap: Record<string, string[]> = {
      AzureOpenAI: ['baseurl', 'llmDeploymentName', 'apiKey', 'apiVersion'],
      AmazonBedrock: ['awsAccessKey', 'awsSecretKey', 'awsRegion', 'bedrockModelId'],
      GoogleAI: ['gcpProjectId', 'gcpLocation', 'vertexAIEndpoint'],
      DaOpenSourceAI: ['serviceUrl', 'apiKeyEncoded', 'headerName'],
      BNY: []
    };
    const fieldsToUpdate = engineFieldMap[selectedEngine] || [];
    const allEngineFields = Object.values(engineFieldMap).flat();
    // Clear validators for all fields
    allEngineFields.forEach(field => {
      const control = this.modelForm.get(field);
      if (control) {
        control.clearValidators();
        control.updateValueAndValidity();
      }
    });
    // Apply only required validators to selected engine fields
    fieldsToUpdate.forEach(field => {
      const control = this.modelForm.get(field);
      if (control) {
        if (field === 'baseurl' || field === 'serviceUrl') {
          control.setValidators([Validators.required, this.noSpacesValidator, this.urlValidator]);
          control.updateValueAndValidity();
        }
        else {
          control.setValidators([Validators.required, this.noSpacesValidator]);
          control.updateValueAndValidity();
        }
      }
    });
  }
}