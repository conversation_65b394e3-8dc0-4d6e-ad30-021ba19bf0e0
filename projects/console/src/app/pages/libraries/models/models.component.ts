import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { Router } from '@angular/router';
import {
  debounceTime,
  distinctUntilChanged,
  map,
  startWith,
  Subject,
  takeUntil,
} from 'rxjs';
import { CardData, Model } from '../../../shared/models/card.model';
import { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';
import { PaginationService } from '../../../shared/services/pagination.service';
import { ModelService } from '../../../shared/services/model.service';
import {
  AvaTextboxComponent,
  DropdownComponent,
  DropdownOption,
  IconComponent,
  TextCardComponent,
} from '@ava/play-comp-library';
import { LucideAngularModule } from 'lucide-angular';
import { Form<PERSON>uilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { ConsoleCardAction, ConsoleCardComponent } from "../../../shared/components/console-card/console-card.component";
import { TimeAgoPipe } from '../../../shared/pipes/time-ago.pipe';

@Component({
  selector: 'app-models',
  standalone: true,
  imports: [
    CommonModule,
    PageFooterComponent,
    TextCardComponent,
    AvaTextboxComponent,
    DropdownComponent,
    LucideAngularModule,
    IconComponent,
    ReactiveFormsModule,
    ConsoleCardComponent,
    TimeAgoPipe,
  ],
  providers: [DatePipe],
  templateUrl: './models.component.html',
  styleUrl: './models.component.scss',
})
export class ModelsComponent implements OnInit, OnDestroy {
  allModels: CardData[] = [];
  filteredModels: CardData[] = [];
  displayedModels: CardData[] = [];
  isFilterBarVisible: boolean = false;
  isLoading: boolean = false;
  error: string | null = null;
  currentPage: number = 1;
  itemsPerPage: number = 11;
  totalPages: number = 1;
  private destroy$ = new Subject<void>();
  selectedData = null;
  searchForm!: FormGroup;
  search: any;
  modelOptions: DropdownOption[] = [
    { name: 'Option 1', value: '1' },
    { name: 'Option 2', value: '2' },
    { name: 'Option 3', value: '3' },
    { name: 'Option 4', value: '4' },
    { name: 'Option 5', value: '5' },
  ];

  defaultActions: ConsoleCardAction[] = [
    {
      id: 'view',
      icon: 'eye',
      label: 'View Prompts',
      tooltip: 'View',
      isPrimary: true,
    },
  ];
  cardSkeletonPlaceholders = Array(11);

  constructor(
    private paginationService: PaginationService,
    private router: Router,
    private modelService: ModelService,
    private datePipe: DatePipe,
    private fb: FormBuilder,
  ) {
    this.searchForm = this.fb.group({
      search: [''],
    });
  }

  ngOnInit(): void {
    this.searchForm
      .get('search')!
      .valueChanges.pipe(
        startWith(''),
        debounceTime(300),
        distinctUntilChanged(),
        map((value) => value?.toLowerCase() ?? ''),
      )
      .subscribe((searchText) => {
        this.filterModels(searchText);
      });
    this.fetchModels();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private transformModelsToCardData(models: Model[]): CardData[] {
    return models.map((model) => {
      const modelAny = model as any;
      const rawDate = modelAny.date ? new Date(modelAny.date) : new Date();
      const formattedDate =
        this.datePipe.transform(rawDate, 'MM/dd/yyyy') || '';
      return {
        id: String(modelAny.id || ''),
        title:
          modelAny.modelDeploymentName || modelAny.model || 'Unnamed Model',
        description: modelAny.modelDescription || '',
        tags: [
          { label: `Model Name: ${modelAny.model}`, type: 'primary' },
          { label: `Model Type: ${modelAny.modelType}`, type: 'primary' },
          { label: `AI Engine: ${modelAny.aiEngine}`, type: 'primary' },
        ],
        createdDate: formattedDate || new Date().toISOString(),
        updatedDate: formattedDate || new Date().toISOString(),
        userType: modelAny.modelType || '',
        client: modelAny.aiEngine || '',
        department: '',
        role: '',
        project: modelAny.model || '',
        category: modelAny.modelType || '',
        model: modelAny.model,
        modelType: modelAny.modelType,
        aiEngine: modelAny.aiEngine,
        status: 'active',
      } as CardData;
    });
  }

  onSelectionChange(data: any) {
    this.selectedData = data;
    console.log('Selection changed:', data);
  }

  fetchSingleModel(modelId: string): void {
    console.log(`=== FETCHING SINGLE MODEL ===`);
    console.log(`Model ID: ${modelId}`);
    console.log(
      `Service method exists:`,
      typeof this.modelService.getOneModeById === 'function',
    );

    this.modelService
      .getOneModeById(modelId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (model: any) => {
          console.log('=== SINGLE MODEL SUCCESS ===');
          console.log('Single model fetched successfully:', model);

          if (model) {
            console.log('Model details:', model);
            console.log('Model Deployment Name:', model.modelDeploymentName);
            console.log('Model ID:', model.id);
          } else {
            console.log('No model data received');
          }
        },
        error: (error: any) => {
          console.log('=== SINGLE MODEL ERROR ===');
          console.error('Error fetching single model:', error);
          console.error('Error details:', error.message);
        },
      });
  }

  fetchModels(): void {
    this.isLoading = true;
    this.error = null;

    this.modelService
      .getAllModelList()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (models) => {
          console.log('Models fetched successfully:', models);
          this.allModels = this.transformModelsToCardData(models || []);
          this.filteredModels = [...this.allModels];
          this.updateDisplayedModels();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error fetching models:', error);
          this.error = error.message || 'Failed to load models';
          this.isLoading = false;
        },
      });
  }

  retryFetch(): void {
    this.fetchModels();
  }

  updateDisplayedModels(): void {
    this.itemsPerPage = this.currentPage === 1 ? 12 : 11;
    const result = this.paginationService.getPaginatedItems(
      this.filteredModels,
      this.currentPage,
      this.itemsPerPage,
    );

    this.displayedModels = result.displayedItems;
    console.log(this.displayedModels);
    this.totalPages = result.totalPages;
  }

  filterModels(searchText: string): void {
    this.filteredModels = this.allModels.filter((model) => {
      const inTitle = model.title?.toLowerCase().includes(searchText);
      const inDescription = model.description
        ?.toLowerCase()
        .includes(searchText);
      const inTags =
        Array.isArray(model.tags) &&
        model.tags?.some((tag: any) =>
          tag.label?.toLowerCase().includes(searchText),
        );

      return inTitle || inDescription || inTags;
    });

    this.updateDisplayedModels();
  }

  onCreateModel(): void {
    console.log('Create Model clicked');
    this.router.navigate(['/libraries/models/create']);
  }

  onCardClicked(modelId: string): void {
    console.log(`Model card clicked: ${modelId}`);
    this.fetchSingleModel(modelId);
    setTimeout(() => {
      this.router.navigate([`/libraries/models/edit/${modelId}`]);
    }, 1000);
  }

  onActionClick(
    event: { actionId: string; action: ConsoleCardAction },
    modelId: string,
  ): void {
    switch (event.actionId) {
      case 'view':
        this.editModel(modelId);
        break;
      default:
        break;
    }
  }

  deleteModel(modelId: string): void {
    console.log(`Delete model ${modelId}`);
    const model = this.allModels.find((m) => m.id === modelId);
    if (model && confirm(`Are you sure you want to delete "${model.title}"?`)) {
      console.log(`Deleting model: ${modelId}`);
    }
  }

  editModel(modelId: string): void {
    //console.log(`Execute model ${modelId}`);
    this.router.navigate(['/libraries/models/edit', modelId], {
      queryParams: { edit: 'true', returnPage: this.currentPage },
    });
  }

  copyModel(modelId: string): void {
    console.log(`Copy model ${modelId}`);
    const model = this.allModels.find((m) => m.id === modelId);
    if (model) {
      console.log(`Duplicating model: ${model.title}`);
    }
  }

  getHeaderIcons(model: any): { iconName: string; title: string }[] {
    return [
      { iconName: 'box', title: model.toolType || 'Models' },
      { iconName: 'users', title: `${model.userCount || 10}` },
    ];
  }

  getFooterIcons(model: any): { iconName: string; title: string }[] {
    return [
      { iconName: 'user', title: model.owner || 'AAVA' },
      { iconName: 'calendar-days', title: model.createdDate },
    ];
  }

  getFullDescription(model: CardData): string {
    const desc = model.description?.trim() || '';

    const parts: string[] = [];

    if (model.model?.trim()) {
      parts.push(`Model Name: ${model.model}`);
    }
    if (model.modelType?.trim()) {
      parts.push(`Model Type: ${model.modelType}`);
    }
    if (model.aiEngine?.trim()) {
      parts.push(`AI Engine: ${model.aiEngine}`);
    }

    const tagLine = parts.join(' | ');

    return desc ? `${desc}<br>${tagLine}` : tagLine;
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedModels();
  }

  get showCreateCard(): boolean {
    return this.currentPage === 1 && !this.isLoading && !this.error;
  }
}
