// Built-in tools data for <PERSON>e
export const TOOLS_DATA = [
  {
    id: '1',
    title: 'File Reader Tool',
    name: 'FileReaderTool',
    description:
      'Reads and processes various file formats including text, JSON, CSV, and more.',
    category: 'File Operations',
    isBuiltIn: true,
    parameters: [
      {
        name: 'file_path',
        type: 'string',
        description: 'Path to the file to be read',
        required: true,
      },
    ],
  },
  {
    id: '2',
    title: 'File Writer Tool',
    name: 'FileWriterTool',
    description: 'Creates and writes content to files in various formats.',
    category: 'File Operations',
    isBuiltIn: true,
    parameters: [
      {
        name: 'file_path',
        type: 'string',
        description: 'Path where the file should be written',
        required: true,
      },
      {
        name: 'content',
        type: 'string',
        description: 'Content to write to the file',
        required: true,
      },
    ],
  },
  {
    id: '3',
    title: 'Web Search Tool',
    name: 'WebSearchTool',
    description:
      'Performs web searches and retrieves relevant information from search engines.',
    category: 'Web Operations',
    isBuiltIn: true,
    parameters: [
      {
        name: 'query',
        type: 'string',
        description: 'Search query to execute',
        required: true,
      },
      {
        name: 'max_results',
        type: 'number',
        description: 'Maximum number of search results to return',
        required: false,
        default: 10,
      },
    ],
  },
  {
    id: '4',
    title: 'HTTP Request Tool',
    name: 'HttpRequestTool',
    description: 'Makes HTTP requests to APIs and web services.',
    category: 'Web Operations',
    isBuiltIn: true,
    parameters: [
      {
        name: 'url',
        type: 'string',
        description: 'URL to make the HTTP request to',
        required: true,
      },
      {
        name: 'method',
        type: 'string',
        description: 'HTTP method (GET, POST, PUT, DELETE)',
        required: false,
        default: 'GET',
      },
      {
        name: 'headers',
        type: 'object',
        description: 'HTTP headers to include in the request',
        required: false,
      },
    ],
  },
  {
    id: '5',
    title: 'JSON Parser Tool',
    name: 'JsonParserTool',
    description: 'Parses and manipulates JSON data structures.',
    category: 'Data Processing',
    isBuiltIn: true,
    parameters: [
      {
        name: 'json_data',
        type: 'string',
        description: 'JSON string to parse',
        required: true,
      },
    ],
  },
  {
    id: '6',
    title: 'CSV Processor Tool',
    name: 'CsvProcessorTool',
    description: 'Processes and manipulates CSV data files.',
    category: 'Data Processing',
    isBuiltIn: true,
    parameters: [
      {
        name: 'csv_data',
        type: 'string',
        description: 'CSV data to process',
        required: true,
      },
      {
        name: 'operation',
        type: 'string',
        description: 'Operation to perform (read, filter, transform)',
        required: true,
      },
    ],
  },
  {
    id: '7',
    title: 'Email Tool',
    name: 'EmailTool',
    description: 'Sends emails with customizable content and attachments.',
    category: 'Communication',
    isBuiltIn: true,
    parameters: [
      {
        name: 'recipient',
        type: 'string',
        description: 'Email address of the recipient',
        required: true,
      },
      {
        name: 'subject',
        type: 'string',
        description: 'Subject line of the email',
        required: true,
      },
      {
        name: 'body',
        type: 'string',
        description: 'Email body content',
        required: true,
      },
    ],
  },
  {
    id: '8',
    title: 'Database Query Tool',
    name: 'DatabaseQueryTool',
    description: 'Executes database queries and retrieves data.',
    category: 'Database Operations',
    isBuiltIn: true,
    parameters: [
      {
        name: 'connection_string',
        type: 'string',
        description: 'Database connection string',
        required: true,
      },
      {
        name: 'query',
        type: 'string',
        description: 'SQL query to execute',
        required: true,
      },
    ],
  },
];

export enum ToolModes {
  toolCreation = 'TOOL_CREATION',
}


export const TOOL_CREATION_USECASE_IDENTIFIER = 'TOOL_CREATION@ASCENDION@PLATFORM_ENGINEERING@AVA@DIGITAL_ASCENDER';