import { Component } from '@angular/core';
import { FormGroup, FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { AvaTagComponent, AvaTextboxComponent, ButtonComponent, DropdownComponent, DropdownOption, IconComponent, PopupComponent, TextCardComponent } from '@ava/play-comp-library';
import { ConsoleCardAction, ConsoleCardComponent } from 'projects/console/src/app/shared/components/console-card/console-card.component';
import { PaginationService } from 'projects/console/src/app/shared/services';
import { Subject, takeUntil, map, startWith, debounceTime, distinctUntilChanged } from 'rxjs';
import { CommonModule } from '@angular/common';
import { LucideAngularModule } from 'lucide-angular';
import { PageFooterComponent } from 'projects/console/src/app/shared/components/page-footer/page-footer.component';
import { RealmService } from 'projects/console/src/app/shared/services/realm.service';
import { RealmData } from '../../Models/realm-card.model';
import { OrgConfigService } from '../../../../org-config/services/org-config.service';


@Component({
  selector: 'app-view-realms',
  imports: [
    CommonModule,
    PageFooterComponent,
    TextCardComponent,
    AvaTextboxComponent,
    LucideAngularModule,
    IconComponent,
    PopupComponent,
    ReactiveFormsModule,
    ConsoleCardComponent,
    AvaTagComponent,
    ButtonComponent,
    DropdownComponent
  ],
  templateUrl: './view-realms.component.html',
  styleUrl: './view-realms.component.scss'
})
export class ViewRealmsComponent {
 // Popup state for success messages
  showSuccessPopup = false;
  popupMessage = '';
  popupTitle = '';
  iconName = 'info';
  submissionSuccess = false;
  // popup for delete confirmation
  showDeletePopup: boolean = false;
  realmToDelete: RealmData | null = null;

  simpleOptions: DropdownOption[] = [
    { name: 'Option 1', value: '1' },
    { name: 'Option 2', value: '2' },
    { name: 'Option 3', value: '3' },
    { name: 'Option 4', value: '4' },
    { name: 'Option 5', value: '5' },
  ];

  defaultActions: ConsoleCardAction[] = [
    {
      id: 'edit',
      icon: 'pencil',
      label: 'Edit item',
      tooltip: 'Edit',
    },
    {
      id: 'delete',
      icon: 'trash-2',
      label: 'Delete item',
      tooltip: 'Delete',
    }    
  ];
  allRealms: RealmData[] = [];
  filteredRealms: RealmData[] = [];
  displayedRealms: any[] = [];
  searchForm!: FormGroup;
  isLoading = false;
  currentPage: number = 1;
  itemsPerPage: number = 11;
  totalPages: number = 1;
  protected destroy$ = new Subject<void>();
  selectedData: any = null;
  cardSkeletonPlaceholders = Array(11);
  showRealmPopup: boolean = false;
  addRealmForm!: FormGroup;

  isRealmUpdateMode: boolean = false;
  hierarchyData: any[] = [];
  orgOptions: DropdownOption[] = [];
  domainOptions: DropdownOption[] = [];
  projectOptions: DropdownOption[] = [];
  teamOptions: DropdownOption[] = [];

  selectedOrgName: string = '';
  selectedDomainName: string = '';
  selectedProjectName: string = '';
  selectedTeamName: string = '';

  selectedOrg: string = '';
  selectedDomain: string = '';
  selectedProject: string = '';
  selectedTeam: string = '';

  constructor(
    private paginationService: PaginationService,
    private router: Router,
    private route: ActivatedRoute,
    private fb: FormBuilder,
    private realmService: RealmService,
    private orgConfigService: OrgConfigService
  ) {
    this.searchForm = this.fb.group({
      search: [''],
    });
  }

  ngOnInit(): void {
    this.isLoading = true;
    this.addRealmForm = this.getAddRealmForm();
    this.initSearchListener();
    this.getRealmList();
    this.getOrgList();
  }

  getAddRealmForm() {
    return this.fb.group({
      realmId: [null],
      realmName: [null, [Validators.required]],
      orgId: [null, [Validators.required]],
      domainId: [null, [Validators.required]],
      projectId: [null, [Validators.required]],
      teamId: [null, [Validators.required]],
    });
  }

  getRealmList() {
    this.realmService.getAllRealm().pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: any) => {
        this.allRealms = this.transformResponseToCardData(res);
        this.filteredRealms = [...this.allRealms];
        this.updateDisplayedRealms();
        this.setInitialPageFromQueryParam();
      },
      error: e => console.error(e),
      complete: () => this.isLoading = false
    })
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedRealms();
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { page: this.currentPage },
      queryParamsHandling: 'merge',
    });
  }

  onActionClick(
    event: { actionId: string; action: ConsoleCardAction },
    realmId: string,
  ): void {
    switch (event.actionId) {
      case 'delete':
        this.deleteRealm(realmId);
        break;
      case 'edit':
        this.onUpdateRealmPopup(realmId);
        break;
      default:
        break;
    }
  }


  transformResponseToCardData(data: any): RealmData[] {
    return data.map((item: any) => {
      const result: RealmData = {
        id: item.realmId || '',
        title: item.realmName,
        tags: [item.orgName, item.domainName, item.projectName, item.teamName],
        orgId: item.orgId,
        orgName: item.orgName,
        domainId: item.domainId,
        domainName: item.domainName,
        projectId: item.projectId,
        projectName: item.projectName,
        teamId: item.teamId,
        teamName: item.teamName,
      };

      if (item.userSignature) {
        result.owner = item.userSignature;
      }

      return result;
    });
  }

  onUpdateRealmPopup(realmId: string) {
    this.isRealmUpdateMode = true;
    this.onUpdateRealmForm(realmId);
  }

  onUpdateRealmForm(realmId: string) {
    const realm = this.allRealms.find((p: any) => p.id === realmId);
    if (!realm) return;
    this.addRealmForm.patchValue({
      realmId: realm.id,
      realmName: realm.title,
      orgId: realm.orgId,
      domainId: realm.domainId,
      projectId: realm.projectId,
      teamId: realm.teamId
    })

    // Store the IDs for form and the names for dropdown pre-selection
    this.selectedOrg = realm.orgId.toString();
    this.selectedDomain = realm.domainId.toString();
    this.selectedProject = realm.projectId.toString();

    // Store the names for dropdown pre-selection
    this.selectedOrgName = realm.orgName;
    this.selectedDomainName = realm.domainName;
    this.selectedProjectName = realm.projectName;
    this.selectedTeamName = realm.teamName;

    if (this.selectedOrg) {
      this.loadDomains(this.selectedOrg);
      if (this.selectedDomain) {
        this.loadProjects(this.selectedDomain);
        if (this.selectedProject) {
          this.loadTeams(this.selectedProject);
        }
      }
    }
    this.showRealmPopup = true;
  }

  // Step 1: User clicks trash icon → open delete confirmation
  private deleteRealm(realmId: string): void {
    const realm = this.allRealms.find((p: any) => p.id === realmId);
    if (!realm) return;
    this.realmToDelete = realm;
    this.showDeletePopup = true;
  }

  // Step 2: User confirms delete in popup
  onConfirmDelete(): void {
    if (!this.realmToDelete?.id) return;

    const realmId = this.realmToDelete.id;

    this.realmService.deleteRealm(realmId).subscribe({
      next: (res) => {
        if (res) {
          // Update local realms lists
          this.allRealms = this.allRealms.filter((p) => p.id !== realmId);
          this.filteredRealms = this.filteredRealms.filter(
            (p) => p.id !== realmId,
          );
          this.updateDisplayedRealms();

          // Show success popup
          this.iconName = 'check-circle';
          this.popupTitle = 'Success';
          this.popupMessage = 'Realm deleted successfully.';
          this.submissionSuccess = true;
          this.showSuccessPopup = true;
        }

        this.closeDeletePopup();
      },
      error: (err) => {
        console.error('Failed to delete realm:', err);
        this.iconName = 'alert-circle';
        this.popupTitle = 'Error';
        this.popupMessage = 'An unexpected error occurred.';
        this.submissionSuccess = false;
        this.showSuccessPopup = true;
        this.closeDeletePopup();
      },
    });
  }

  // Step 3: User cancels or closes delete popup
  closeDeletePopup(): void {
    this.showDeletePopup = false;
    this.realmToDelete = null;
  }

  // Success popup confirm handler
  onSuccessConfirm(): void {
    this.closeSuccessPopup();
  }

  // Close success popup manually or when user clicks close icon
  closeSuccessPopup(): void {
    this.showSuccessPopup = false;
    this.popupTitle = '';
    this.popupMessage = '';
    this.iconName = 'info';
  }

  private setInitialPageFromQueryParam(): void {
    const pageParam = this.route.snapshot.queryParamMap.get('page');
    if (pageParam) {
      const page = parseInt(pageParam, 10);
      if (!isNaN(page)) this.currentPage = page;
    }
  }

  private initSearchListener(): void {
    this.searchForm
      .get('search')!
      .valueChanges.pipe(
        startWith(''),
        debounceTime(300),
        distinctUntilChanged(),
        map((value) => value?.toLowerCase() ?? ''),
        takeUntil(this.destroy$),
      )
      .subscribe((searchText) => {
        this.filterRealms(searchText);
      });
  }

  private updateDisplayedRealms(): void {
    this.itemsPerPage = this.currentPage === 1 ? 12 : 11;
    const { displayedItems, totalPages } =
      this.paginationService.getPaginatedItems(
        this.filteredRealms,
        this.currentPage,
        this.itemsPerPage,
      );
    this.displayedRealms = displayedItems;
    this.totalPages = totalPages;
  }

  private filterRealms(searchText: string): void {
    this.filteredRealms = this.allRealms.filter((realm: any) => {
      const titleMatch = realm.title?.toLowerCase().includes(searchText);
      const descriptionMatch = realm.description
        ?.toLowerCase()
        .includes(searchText);
      const tagMatch = realm.tags?.some((tag: any) =>
        tag.label?.toLowerCase().includes(searchText),
      );
      return titleMatch || descriptionMatch || tagMatch;
    });
    this.currentPage = 1;
    this.updateDisplayedRealms();
  }

  onCreateRealmPopup(): void {
    this.showRealmPopup = true;
  }

  getOrgList() {
    if(this.hierarchyData.length === 0) {
      this.orgConfigService.getOrganizationHierarchy().subscribe({
        next: (data: any) => {
          this.hierarchyData = data;
          this.loadOrganizations();
        },
        error: e => console.error(e)
      })
    }
  }

  loadOrganizations(): void {
    this.orgOptions = this.hierarchyData.map((org) => ({
      name: org.organizationName,
      value: org.orgId.toString(),
    }));
  }

  onOrgSelect(event: any) {
    const selectedOrgId = event.selectedOptions?.[0]?.value;
    const selectedOrgName = event.selectedOptions?.[0]?.name;
    if (selectedOrgId) {
      this.selectedOrg = selectedOrgId;
      this.selectedOrgName = selectedOrgName;
      this.addRealmForm.patchValue({ orgId: selectedOrgId, domainId: '', projectId: '', teamId: ''  });
      this.loadDomains(selectedOrgId);
      this.selectedDomain = '';
      this.selectedProject = '';
      this.selectedTeam = '';
      this.selectedDomainName = '';
      this.selectedProjectName = '';
      this.selectedTeamName = '';
    }
  }

  loadDomains(orgId: string): void {
    const org = this.hierarchyData.find((o) => o.orgId.toString() === orgId);
    if (org) {
      this.domainOptions = org.domains.map((domain: any) => ({
        name: domain.domainName,
        value: domain.domainId.toString(),
      }));
    } else {
      this.domainOptions = [];
    }
  }

   onDomainSelect(event: any): void {
    const selectedDomainId = event.selectedOptions?.[0]?.value;
    const selectedDomainName = event.selectedOptions?.[0]?.name;
    if (selectedDomainId) {
      this.selectedDomain = selectedDomainId;
      this.selectedDomainName = selectedDomainName;
      this.addRealmForm.patchValue({ domainId: selectedDomainId, projectId: '', teamId: '' });
      this.loadProjects(selectedDomainId);
      this.selectedProject = '';
      this.selectedTeam = '';
      this.selectedProjectName = '';
      this.selectedTeamName = '';
    }
  }

  loadProjects(domainId: string): void {
    const org = this.hierarchyData.find((o) =>
      o.domains.some((d: any) => d.domainId.toString() === domainId),
    );
    if (org) {
      const domain = org.domains.find(
        (d: any) => d.domainId.toString() === domainId,
      );
      if (domain) {
        this.projectOptions = domain.projects.map((project: any) => ({
          name: project.projectName,
          value: project.projectId.toString(),
        }));
      } else {
        this.projectOptions = [];
      }
    } else {
      this.projectOptions = [];
    }
  }

  onProjectSelect(event: any): void {
    const selectedProjectId = event.selectedOptions?.[0]?.value;
    const selectedProjectName = event.selectedOptions?.[0]?.name;
    if (selectedProjectId) {
      this.selectedProject = selectedProjectId;
      this.selectedProjectName = selectedProjectName;
      this.addRealmForm.patchValue({ projectId: selectedProjectId, team: '' });
      this.loadTeams(selectedProjectId);
      this.selectedTeam = '';
      this.selectedTeamName = '';
    }
  }

  loadTeams(projectId: string): void {
    const org = this.hierarchyData.find((o) =>
      o.domains.some((d: any) =>
        d.projects.some((p: any) => p.projectId.toString() === projectId),
      ),
    );
    if (org) {
      const domain = org.domains.find((d: any) =>
        d.projects.some((p: any) => p.projectId.toString() === projectId),
      );
      if (domain) {
        const project = domain.projects.find(
          (p: any) => p.projectId.toString() === projectId,
        );
        if (project) {
          this.teamOptions = project.teams.map((team: any) => ({
            name: team.teamName,
            value: team.teamId.toString(),
          }));
        } else {
          this.teamOptions = [];
        }
      } else {
        this.teamOptions = [];
      }
    } else {
      this.teamOptions = [];
    }
  }

  onTeamSelect(event: any): void {
    const selectedTeamId = event.selectedOptions?.[0]?.value;
    const selectedTeamName = event.selectedOptions?.[0]?.name;
    if (selectedTeamId) {
      this.selectedTeam = selectedTeamId;
      this.selectedTeamName = selectedTeamName;
      this.addRealmForm.patchValue({ teamId: selectedTeamId });
    }
  }

  createRealm() {
    if(this.addRealmForm.valid) {
      const {realmId, realmName, teamId} = this.addRealmForm.value;

      if(this.isRealmUpdateMode) {
        this.realmService.updateRealm(realmName, teamId, realmId).subscribe({
          next: (res: any) => {
            this.allRealms.forEach(r => {
              if(r.id == res.realmId) {
                r.title = res.realmName;
                r.tags = [res.orgName, res.domainName, res.projectName, res.teamName];
                r.orgId = res.orgId;
                r.orgName = res.orgName;
                r.domainId = res.domainId;
                r.domainName = res.domainName;
                r.projectId = res.projectId;
                r.projectName = res.projectName;
                r.teamId = res.teamId;
                r.teamName = res.teamName;
              }
            })
            this.filteredRealms = [...this.allRealms];
            this.updateDisplayedRealms();
  
            // Show success popup
            this.iconName = 'check-circle';
            this.popupTitle = 'Success';
            this.popupMessage = 'Realm updated successfully.';
            this.submissionSuccess = true;
            this.showSuccessPopup = true;
            this.showRealmPopup = false;
          },
          error: e => {
            console.error(e)
            this.iconName = 'alert-circle';
            this.popupTitle = 'Error';
            this.popupMessage = 'An unexpected error occurred.';
            this.submissionSuccess = false;
            this.showSuccessPopup = true;
            this.showRealmPopup = false;
          },
          complete: () => this.closeRealmPopup()
        })
      } else {
        this.realmService.createRealm(realmName, teamId).subscribe({
          next: (res: any) => {
            this.allRealms.push({
              id: res.realmId,
              title: res.realmName,
              tags: [res.orgName, res.domainName, res.projectName, res.teamName],
              orgId: res.orgId,
              orgName: res.orgName,
              domainId: res.domainId,
              domainName: res.domainName,
              projectId: res.projectId,
              projectName: res.projectName,
              teamId: res.teamId,
              teamName: res.teamName,
            })
            this.filteredRealms = [...this.allRealms];
            this.updateDisplayedRealms();
  
            // Show success popup
            this.iconName = 'check-circle';
            this.popupTitle = 'Success';
            this.popupMessage = 'Realm created successfully.';
            this.submissionSuccess = true;
            this.showSuccessPopup = true;
            this.showRealmPopup = false;
          },
          error: e => {
            console.error(e)
            this.iconName = 'alert-circle';
            this.popupTitle = 'Error';
            this.popupMessage = 'An unexpected error occurred.';
            this.submissionSuccess = false;
            this.showSuccessPopup = true;
            this.showRealmPopup = false;
          },
          complete: () => this.closeRealmPopup()
        })
      }
    }
  }

  closeRealmPopup() {
    this.showRealmPopup = false;
    this.addRealmForm.reset();
    this.isRealmUpdateMode = false;
    this.addRealmForm.reset();
    this.selectedOrgName = '';
    this.selectedDomainName = '';
    this.selectedProjectName = '';
    this.selectedTeamName = '';
    this.selectedOrg = '';
    this.selectedDomain = '';
    this.selectedProject = '';
    this.selectedTeam = '';
  }
}
