.drop-zone-canvas-container {
  // position: relative;
  // display: flex;
  // flex-direction: column;
  height: 100%;
  width: 100%;
  min-height: 500px; // Ensure minimum height
  background-color: #f8f9fa;
  background-image: radial-gradient(circle, #d1d5db 1px, transparent 1px);
  background-size: 20px 20px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #d1d3d8;
}

// Central Progress Bar
.central-progress {
  position: absolute;
  top: 50%;
  left: 58%;
  transform: translate(-50%, -50%);
  z-index: 10;
  background-color: white;
  border-radius: 50%;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-ring {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-circle {
  transform: rotate(-90deg);
}

.progress-background {
  opacity: 0.15;
}

.progress-bar {
  transition: stroke-dashoffset 0.3s ease;
}

.progress-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #374151;
}

.progress-percentage {
  font-size: 18px;
  font-weight: 600;
  line-height: 1;
}

.progress-label {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

// Drop Zones
// .drop-zones {
//   position: relative;
//   width: 100%;
//   height: 100%;
//   flex: 1; // Take remaining space
//   // z-index: 2;
// }

.drop-zone {
  // position: absolute;
  border-radius: 8px;
  padding: 20px; // More padding for bigger boxes
  transition: all 0.3s ease; // Smooth transitions for height changes

  // &:hover {
  //   border-color: #3b82f6;
  //   background-color: rgba(255, 255, 255, 0.8);
  // }

  &.has-nodes {
    // Keep original colors, only change border style to solid
    border-style: solid;
  }

  // Collapsed state - ensure minimum drop area
  &.collapsed {
    min-height: 40px; // Ensure enough space for drag & drop

    .zone-header {
      margin-bottom: 0; // Remove bottom margin when collapsed
    }
  }
}

// Zone Positioning
.north-zone {
  // top: 140px;
  // left: 38%;
  // transform: translateX(-50%);
  width: 300px;
  height: auto;
  background-color: #ecf0fa;
  color: #005eb5;
  border: 2px solid #9ab7f6;
  position: relative;
  top: -2.5rem;
  left: -30%;
}

.east-zone {
  // top: 140px;
  // right: 20%;
  // transform: translateX(50%);
  width: 300px;
  height: auto;
  background-color: #f2ebfd;
  border: 2px solid #d6c2f9;
  color: #d6c2f9;
  position: relative;
  top: -2.5rem;
  right: -30%;
}

.south-zone {
  // top: 75%;
  // right: 12%;
  // transform: translateY(-50%);
  width: 300px;
  height: auto;
  background-color: #fbf6f7;
  border: 2px solid #fecaca;
  color: #dc2626 !important;
  position: absolute;
  bottom: -3.5rem;
  right: -30%;
  // Dynamic height for multiple nodes
  &.has-nodes {
    height: auto;
    min-height: 100px;
    max-height: 200px;
    overflow-y: auto;
  }
}

.west-zone {
  // top: 75%;
  // left: 30%;
  // transform: translateY(-50%);
  width: 300px;
  height: auto;
  background-color: #ecf8f4;
  border: 2px solid #a9e1cc;
  color: #25684f;
  position: absolute;
  bottom: -3.5rem;
  left: -30%;

  // Dynamic height for multiple nodes
  &.has-nodes {
    height: auto;
    min-height: 100px;
    max-height: 200px;
    overflow-y: auto;
    // transform: translateY(-50%);
  }
}

// Parent and Box Layout
#parent-box {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  gap: 2rem;
}

#box1 {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 300px;
  position: relative;
  border: 1px solid #bbbec5;
  position: fixed;
  top: 32%;
  left: 40%;
  border-left: none;
}

#box2 {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 300px;
  position: relative;
  border: 1px solid #bbbec5;
  position: fixed;
  top: 32%;
  left: 60%;
  border-right: none;
}

// Zone Headers
.zone-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;

  // &:hover {
  //   background-color: rgba(0, 0, 0, 0.02);
  //   border-radius: 4px;
  //   margin: -4px;
  //   padding: 4px 4px 16px 4px;
  // }

  .header-content {
    display: flex;
    align-items: center;
    gap: 8px;

    .header-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 8px;
      background-color: rgba(59, 130, 246, 0.1);
      color: #3b82f6;
    }

    .zone-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #374151;
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 8px;

    .accordion-toggle {
      background: none;
      border: none;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      color: #6b7280;

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
        color: #374151;
      }

      svg {
        transition: transform 0.3s ease;
      }
    }
  }
}

.required-badge {
  background-color: var(--status-error);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.optional-badge {
  background-color: var(--status-warning);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

// Zone Content
.zone-content {
  min-height: 60px;
  flex: 1; // Allow content to expand
  transition: all 0.3s ease-in-out;
  opacity: 1;
  overflow: hidden;
}

// Special handling for multi-node zones (Knowledge and Guardrails/Tools)
.west-zone,
.south-zone {
  display: flex;
  flex-direction: column;

  .zone-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto; // Enable scrolling for content
    max-height: 200px; // Limit height to force scrolling
  }

  .nodes-list {
    flex: 1;
    overflow-y: auto; // Enable scrolling for the list
  }
}

.empty-state {
  text-align: center;
  color: var(--text-secondary);
  font-size: 12px;
  padding: 20px 0;
  font-style: italic;
}

// Kanban Cards
.nodes-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.kanban-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  .card-title {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: calc(100% - 50px);
  }

  .delete-btn {
    background: none;
    border: none;
    color: #9ca3af;
    font-size: 16px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    margin-left: 8px;
    flex-shrink: 0;

    &:hover {
      background-color: #fef2f2;
      color: #ef4444;
    }
  }
}

// Add styles for guardrail cards with toggle switches
.guardrail-card {
  .card-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .card-title {
    flex: 1;
    margin-right: 0; // Remove margin since we have gap
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: 0; // Allow flex item to shrink below content size
    max-width: calc(
      100% - 80px
    ) !important; // Reserve space for toggle (40px) + gap (8px)
  }

  .guardrail-toggle {
    flex-shrink: 0;
  }
}

// Toggle switch styles
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
  cursor: pointer;

  input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .toggle-slider {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    border-radius: 20px;
    transition: 0.3s;

    &::before {
      position: absolute;
      content: "";
      height: 16px;
      width: 16px;
      left: 2px;
      bottom: 2px;
      background-color: white;
      border-radius: 50%;
      transition: 0.3s;
    }
  }

  input:checked + .toggle-slider {
    background-color: #ef4444;
  }

  input:checked + .toggle-slider::before {
    transform: translateX(20px);
  }

  input:focus + .toggle-slider {
    box-shadow: 0 0 1px #ef4444;
  }
}

// Zone-specific colors
.prompts-zone {
  &.has-nodes {
    border-color: #3b82f6;
  }

  .zone-title {
    color: #005eb5 !important;
  }

  .required-badge {
    background-color: #3b82f6;
  }
}

.models-zone {
  &.has-nodes {
    border-color: #8b5cf6;
  }

  .zone-title {
    color: #673ab7 !important;
  }

  .required-badge {
    background-color: #8b5cf6;
  }
}

.knowledge-zone {
  &.has-nodes {
    border-color: #10b981;
  }

  .zone-title {
    color: #25684f !important;
  }

  .optional-badge {
    background-color: #10b981;
  }
}

.guardrails-zone {
  &.has-nodes {
    border-color: #ef4444;
  }

  .zone-title {
    color: #dc2626 !important;
  }

  .optional-badge {
    background-color: #ef4444;
  }
}

.tools-zone {
  &.has-nodes {
    border-color: #f59e0b;
    // background-color: rgba(245, 158, 11, 0.05);
  }

  .zone-title {
    color: #d97706 !important;
  }

  .optional-badge {
    background-color: #f59e0b;
  }
}

@media (max-width: 1600px) {
  #box1 {
    top: 32%;
    left: 35%;
  }
  #box2 {
    top: 32%;
    left: 60%;
  }
}

// Responsive adjustments
@media (max-width: 1400px) {
  #box1 {
    top: 32%;
    left: 30%;
  }
  #box2 {
    top: 32%;
    left: 60%;
  }

  .connection-system {
    .prompt-connection::before {
      width: 70px;
      left: 155px;
    }
    .prompt-connection::after {
      left: 223px;
    }
    .model-connection::before {
      width: 70px;
      right: 155px;
    }
    .model-connection::after {
      right: 223px;
    }
    .knowledge-connection::before {
      width: calc(26% - 50px);
    }
    .bottom-connection::before {
      width: calc(26% - 50px);
    }
  }
}

@media (max-width: 1200px) {
  .connection-system {
    .prompt-connection::before {
      width: 60px;
      left: 160px;
    }
    .prompt-connection::after {
      left: 218px;
    }
    .model-connection::before {
      width: 60px;
      right: 160px;
    }
    .model-connection::after {
      right: 218px;
    }
    .knowledge-connection::before {
      width: calc(23% - 40px);
    }
    .bottom-connection::before {
      width: calc(23% - 40px);
    }
  }
}

@media (max-width: 768px) {
  .connection-system {
    // Hide connections on mobile for cleaner look
    display: none;
  }
}

// Drag States
.drop-zone {
  &.drag-over {
    border-color: var(--brand-primary);
    background-color: var(--brand-primary-10);
    transform: scale(1.02);
  }

  &.drag-reject {
    border-color: var(--status-error);
    background-color: rgba(239, 68, 68, 0.1);
    animation: shake 0.3s ease-in-out;
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-2px);
  }
  75% {
    transform: translateX(2px);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .drop-zone-canvas-container {
    height: 500px;
  }

  .drop-zone {
    width: 150px;
    height: 100px;
    padding: 12px;
  }

  .north-zone,
  .south-zone {
    width: 150px;
  }

  .east-zone,
  .west-zone {
    width: 150px;
    height: 100px;
  }

  .central-progress {
    .progress-circle {
      width: 100px;
      height: 100px;
    }

    .progress-percentage {
      font-size: 16px;
    }
  }
}
