<!-- 1 -->
<svg width="0" height="0" style="position: absolute">
  <defs>
    <clipPath id="headerClip" clipPathUnits="objectBoundingBox">
      <path d="
        M 0.03,0 
        L 0.97,0 
        L 0.95,0.71 
        Q 0.939,1    0.91,1 
        L 0.09,1 
        Q 0.061,1    0.05,0.69 
        Z" />
    </clipPath>
  </defs>
</svg>
<!-- 2 -->
<!-- <svg width="0" height="0" style="position:absolute;">
  <defs>
    <clipPath id="headerClip" clipPathUnits="objectBoundingBox">
      <path d="
        M 0.03,0 
        L 0.97,0 

        L 0.93,0.71 
        Q 0.91,1 0.89,1 

        L 0.11,1 
        Q 0.09,1 0.07,0.71 

        Z" />
    </clipPath>
  </defs>
</svg> -->
<!-- 3 -->
<!-- <svg width="0" height="0" style="position:absolute;">
  <defs>
    <clipPath id="headerClip" clipPathUnits="objectBoundingBox">
      <path d="
        M 0.03,0 
        L 0.97,0 

        L 0.93,0.71 
        Q 0.91,1 0.87,1 

        L 0.13,1 

        Q 0.09,1 0.07,0.71 

        Z" />
    </clipPath>
  </defs>
</svg> -->

<awe-header theme="light">
  <div left-content>
    <img [src]="logoSrc" class="px-2" alt="Logo" />
  </div>
  <div center-content>
    <div class="header-wrapper">
      <div class="header-shadow"></div>
      <div class="nav-menu">
        <div class="nav-items">
          <app-nav-item *ngFor="let item of navItems; let i = index" [label]="item.label" [route]="item.route"
            [selected]="item.selected" [hasDropdown]="item.hasDropdown" [dropdownOpen]="item.dropdownOpen || false"
            [dropdownItems]="item.dropdownItems || []" [icon]="item.icon" [disabled]="item.disabled || false"
            (toggleDropdownEvent)="toggleDropdown(i)" (navigateEvent)="navigateTo($event)"
            (selectEvent)="selectMenuItem(i)" (dropdownItemSelected)="onDropdownItemSelected($event, i)"
            (dropdownPortalOpen)="onDropdownPortalOpen($event)" class="nav-item-wrapper">
          </app-nav-item>
        </div>
      </div>
    </div>
  </div>
  <div right-content class="user-info-container">
    <div class="org-path-dropdown-container">
      <div class="org-path-trigger" (click)="toggleOrgDialog()" #orgPathTrigger>
        <span class="org-icon">
          <img src="assets/svgs/ascendion-logo/header-ascendion-logo.svg" alt="Ascendion Logo" width="40" height="40" />
        </span>
        <span class="org-label-text">
          <span>{{ orgLabel }}</span>
        </span>
        <span class="org-dropdown-arrow" [class.open]="isOrgDialogOpen">
          <svg width="16" height="16" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M2.5 4L6 7.5L9.5 4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
              stroke-linejoin="round" />
            `
          </svg>
        </span>
      </div>
      <div *ngIf="isOrgDialogOpen" class="org-path-popover" #popover [ngClass]="popoverAlign">
        <form>
          <div class="filter-config-title">Filter Configuration</div>
          <div class="dropdown-row-vertical">
            <label class="filter-label required">Choose Organization</label>
            <ava-dropdown [dropdownTitle]="'Select Organization'" [options]="orgOptions"
              [selectedValue]="selectedOrgName" [disabled]="false" (selectionChange)="onOrgSelect($event)"
              [search]="true" [enableSearch]="true"></ava-dropdown>
            <label class="filter-label required">Choose Domain</label>
            <ava-dropdown [dropdownTitle]="'Select Domain'" [options]="domainOptions"
              [selectedValue]="selectedDomainName" [disabled]="!selectedOrg" (selectionChange)="onDomainSelect($event)"
              [search]="true" [enableSearch]="true"></ava-dropdown>
            <label class="filter-label required">Choose Project</label>
            <ava-dropdown [dropdownTitle]="'Select Project'" [options]="projectOptions"
              [selectedValue]="selectedProjectName" [disabled]="!selectedDomain"
              (selectionChange)="onProjectSelect($event)" [search]="true" [enableSearch]="true"></ava-dropdown>
            <label class="filter-label required">Choose Team</label>
            <ava-dropdown [dropdownTitle]="'Select Team'" [options]="teamOptions" [selectedValue]="selectedTeamName"
              [disabled]="!selectedProject" (selectionChange)="onTeamSelect($event)" [search]="true"
              [enableSearch]="true"></ava-dropdown>
          </div>
          <div class="popover-actions">
            <ava-button label="Cancel" variant="secondary" size="medium" (userClick)="closeOrgDialog()">
            </ava-button>
            <ava-button label="Apply" variant="primary" size="medium" [disabled]="!headerConfigForm.valid"
              (userClick)="saveOrgPathAndClose()">
            </ava-button>
          </div>
        </form>
      </div>
      <div *ngIf="isOrgDialogOpen" class="org-path-backdrop" (click)="closeOrgDialog()"></div>
    </div>

    <div class="d-flex justify-content-center align-items-center cursor-pointer" (click)="toggleDrawer()">
      <img [src]="themeMenuIcon" alt="Theme Menu" loading="lazy" />
    </div>
    <div class="app-drawer-backdrop" *ngIf="drawerOpen" (click)="toggleDrawer()"></div>
    <div class="app-drawer" *ngIf="drawerOpen">
      <div class="app-drawer-content">
        <div class="app-drawer-grid">
          <div class="app-drawer-item" *ngFor="let app of drawerApps" (click)="navigateTo(app.route)">
            <img [src]="app.icon" [alt]="app.label || 'App icon'" [title]="app.label || 'App icon'" class="app-drawer-icon" />
            <span class="app-drawer-label">{{ app.label }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="d-flex justify-content-center align-items-center cursor-pointer" (click)="toggleTheme()">
      <img [src]="themeToggleIcon" alt="Toggle Theme" loading="lazy" />
    </div>
    <div class="profile-container header-right-content">
      <div class="cursor-pointer d-flex justify-content-center align-items-center" [class.active]="profileDropdownOpen"
        (click)="toggleProfileDropdown()">
        <img [src]="userAvatar" alt="User Profile" />
      </div>

      <!-- Profile Dropdown -->
      <div class="profile-dropdown" [class.visible]="profileDropdownOpen">
        <div class="profile-dropdown-content">
          <div class="profile-info">
            <div class="profile-name">{{ userName }}</div>
            <div class="profile-email" *ngIf="userEmail">{{ userEmail }}</div>
          </div>
          <div class="profile-divider"></div>
          <div class="profile-actions">
            <div class="profile-action-item" (click)="logout()">
              <span class="action-label">Logout</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</awe-header>

<!-- Dropdown Portal Container -->
<div *ngIf="dropdownPortal.open && dropdownPortal.rect" class="dropdown-portal-menu"
  [style.top]="dropdownPortal.rect.bottom + 4 + 'px'" [style.left]="dropdownPortal.rect.left + 'px'">
  <app-dropdown-menu [items]="dropdownPortal.items" [visible]="dropdownPortal.open"
    (itemSelected)="onDropdownItemSelected($event, 0); closeDropdownPortal()"></app-dropdown-menu>
</div>