import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DropdownMenuComponent } from '../dropdown-menu/dropdown-menu.component';

interface DropdownItem {
  label: string;
  description: string;
  route: string;
  icon: string;
}

@Component({
  selector: 'app-nav-item',
  standalone: true,
  imports: [CommonModule, DropdownMenuComponent],
  templateUrl: './nav-item.component.html',
  styleUrl: './nav-item.component.scss',
})
export class NavItemComponent {
  @Input() label: string = '';
  @Input() route: string = '';
  @Input() selected: boolean = false;
  @Input() hasDropdown: boolean = false;
  @Input() dropdownOpen: boolean = false;
  @Input() dropdownItems: DropdownItem[] = [];
  @Input() icon: string = '';
  @Input() disabled: boolean = false;

  @Output() toggleDropdownEvent = new EventEmitter<void>();
  @Output() navigateEvent = new EventEmitter<string>();
  @Output() selectEvent = new EventEmitter<void>();
  @Output() dropdownItemSelected = new EventEmitter<{
    route: string;
    label: string;
  }>();
  @Output() dropdownPortalOpen = new EventEmitter<{
    rect: DOMRect;
    items: DropdownItem[];
    parentLabel: string;
    navItemId: string;
  }>();

  toggleDropdown(): void {
    this.toggleDropdownEvent.emit();
  }

  navigate(): void {
    this.navigateEvent.emit(this.route);
  }

  select(): void {
    this.selectEvent.emit();
  }

  onClick(event: MouseEvent): void {
    if (this.disabled) {
      event.preventDefault();
      event.stopPropagation();
      return;
    }

    if (this.hasDropdown) {
      event.stopPropagation();
      // Emit bounding rect and dropdown data for portal rendering, use label as navItemId
      const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
      this.dropdownPortalOpen.emit({ rect, items: this.dropdownItems, parentLabel: this.label, navItemId: this.label });
      this.toggleDropdown();
    } else {
      this.navigate();
      this.select();
    }
  }

  onDropdownItemSelected(event: { route: string; label: string }): void {
    this.dropdownItemSelected.emit(event);
  }
}
