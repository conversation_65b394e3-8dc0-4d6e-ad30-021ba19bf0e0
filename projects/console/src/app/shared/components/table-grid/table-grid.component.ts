import {
  Component,
  computed,
  effect,
  input,
  output,
  signal,
} from '@angular/core';
import { columnDefs } from './model/table-grid.model';
import { CommonModule } from '@angular/common';
import { CellRendererDirective } from './directive/cell-renderer.directive';
import { PaginationComponent } from '../pagination/pagination.component';

@Component({
  selector: 'awe-table-grid',
  imports: [CommonModule, CellRendererDirective, PaginationComponent],
  templateUrl: './table-grid.component.html',
  styleUrl: './table-grid.component.scss',
})
export class TableGridComponent {
  columnDefs = input.required<columnDefs[]>();
  rowData = input<Array<any>>([]);
  height = input<number>(500);
  headerHeight = input<number>(0);
  hoverable = input<boolean>(true);
  emptyStateMessage = input<string>('No data available');
  pagination = input<boolean>(false);
  serverPagination = input<boolean>(false);
  paginationPageSize = input<number>(10);
  visiblePageCount = input<number>(5);
  totalItems = input<number | undefined>(undefined);
  loading = input<boolean>(false);
  skeletonRows = input<number>(5);
  pageChange = output<number>();

  columnFlexValues = signal<string[]>([]);
  currentPage = signal<number>(1);

  totalRowData = computed(() => this.totalItems() ?? this.rowData().length);
  paginatedData = computed(() => {
    const data = this.rowData();
    if (!this.pagination()) {
      return data;
    }

    // If server-side pagination
    if (this.serverPagination()) {
      return data;
    }

    // client-side pagination
    const pageSize = this.paginationPageSize();
    const page = this.currentPage();
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return data.slice(startIndex, endIndex);
  });

  // Create skeleton rows array for loading state
  skeletonRowsArray = computed(() => {
    return Array(this.skeletonRows())
      .fill(0)
      .map((_, index) => index);
  });

  constructor() {
    effect(() => {
      const columns = this.columnDefs();
      if (columns.length > 0) {
        this.initializeColumnFlexValues();
      }
    });
  }

  private initializeColumnFlexValues() {
    const flexValues = this.columnDefs().map((col) => {
      if (col.width) {
        return `0 0 ${col.width}px`;
      }
      const hasFixedWidthColumns = this.columnDefs().some((c) => c.width);
      const flexibleColumns = this.columnDefs().filter((c) => !c.width).length;
      if (hasFixedWidthColumns && flexibleColumns > 0) {
        return `1 1 ${col.minWidth || 150}px`;
      }
      return `1 1 ${col.minWidth || 150}px`;
    });
    this.columnFlexValues.set(flexValues);
  }

  getColumnFlex(index: number): string {
    return this.columnFlexValues()[index] || '1 1 150px';
  }

  getColumnMinWidth(index: number): number {
    return this.columnDefs()[index].minWidth || 50;
  }

  getColumnMaxWidth(index: number): number | null {
    return this.columnDefs()[index].maxWidth || null;
  }

  onPageChange(page: number): void {
    if (!this.pagination()) return;
    if (this.serverPagination()) {
      this.pageChange.emit(page);
    } else {
      this.currentPage.set(page);
    }
  }
}
