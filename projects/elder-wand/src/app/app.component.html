<div
  class="container-fluid"
  [class.launchpad-bg]="isLaunchpadRoute"
  [class.white-bg]="!isLaunchpadRoute"
>
  <shared-app-header
    *ngIf="showHeaderAndNav"
    [config]="headerConfig"
    [themeService]="themeService"
    (navigationEvent)="onNavigation($event)"
    (profileAction)="onProfileAction($event)"
    (themeToggle)="onThemeToggle($event)">
  </shared-app-header>
  <!-- <app-hero *ngIf="showHeroSection"></app-hero> -->
  <router-outlet></router-outlet>
</div>
