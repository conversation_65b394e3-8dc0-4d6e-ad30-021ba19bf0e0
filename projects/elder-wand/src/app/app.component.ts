import { Component, OnInit, ViewChild, ViewContainerRef } from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';

import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { AuthTokenService } from '@shared/auth/services/auth-token.service';
import { AuthConfig } from '@shared/auth/interfaces/auth-config.interface';
import { AuthService } from '@shared/auth/services/auth.service';
import { environment } from '../environments/environment';
import { SharedAppHeaderComponent, HeaderConfig } from '@shared/components/app-header/app-header.component';
import { elderWandHeaderConfig } from './config/header.config';
import { ThemeService } from './shared/services/theme.service';
import { GlobalStoreService } from './shared/service/global-store.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, CommonModule, SharedAppHeaderComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit {
  @ViewChild('dialogHost', { read: ViewContainerRef })
  dialogHost!: ViewContainerRef;
  showHeaderAndNav: boolean = true;
  showHeroSection: boolean = true;
  isLaunchpadRoute: boolean = false;
  
  // Header configuration
  headerConfig: HeaderConfig = elderWandHeaderConfig;

  constructor(
    private authTokenService: AuthTokenService,
    private tokenStorage: TokenStorageService,
    private authService: AuthService,
    private router: Router,
    private globalStoreService: GlobalStoreService,
    public themeService: ThemeService, // Make it public so it can be passed to header
  ) {}

  ngOnInit(): void {
    const authConfig: AuthConfig = {
      apiAuthUrl: environment.elderWandApiAuthUrl,
      redirectUrl: environment.elderWandRedirectUrl,
      postLoginRedirectUrl: '/dashboard',
      appName: 'elder-wand',
    };
    this.authService.setAuthConfig(authConfig);
    this.authTokenService.handleAuthCodeAndToken();
    this.authTokenService.startTokenCheck();

    if (!this.tokenStorage.getCookie('org_path')) {
      this.tokenStorage.setCookie(
        'org_path',
        'ascendion@core@genai@aava::201@202@203@204',
      );
    }

    this.router.events.subscribe(() => {
      if (this.router.url === '/login') {
        this.showHeaderAndNav = false;
        this.showHeroSection = false;
        this.isLaunchpadRoute = false;
      } else if (this.router.url === '/marketplace') {
        // Hide header and nav for marketplace (it has its own layout)
        this.showHeaderAndNav = false;
        this.showHeroSection = false;
        this.isLaunchpadRoute = false;
      } else if (this.router.url === '/' || this.router.url === '') {
        // Hide header and nav for root redirect component
        this.showHeaderAndNav = false;
        this.showHeroSection = false;
        this.isLaunchpadRoute = false;
      } else {
        this.showHeaderAndNav = true;
        // Hide hero section for my-agent-home page, show for all other pages
        this.showHeroSection = this.router.url !== '/my-agent-home';
        // Show background image only for dashboard (launchpad) route
        this.isLaunchpadRoute =
          this.router.url === '/dashboard' || this.router.url === '/';
      }
    });
  }

  ngOnDestroy() {
    this.authTokenService.stopTokenCheck();
  }

  // Header event handlers
  onNavigation(route: string): void {
    console.log('Elder Wand Navigation to:', route);
  }

  onProfileAction(action: string): void {
    if (action === 'logout') {
      this.authService.logout();
    }
  }

  onThemeToggle(theme: 'light' | 'dark'): void {
    console.log('Theme toggled to:', theme);
    // Theme service will handle the actual theme change
  }

  getSelectedUser(user: any) {
    this.globalStoreService.selectedUser.next(user);
  }

  onAscendionOptionSelected(option: string) {
    this.globalStoreService.selectedStudioOption.next(option);
  }
}
