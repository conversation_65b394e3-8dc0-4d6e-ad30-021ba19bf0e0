import { Routes } from '@angular/router';
import { MarketplaceAuthGuard } from './shared/guards/marketplace-auth.guard';
import { LoginComponent } from '@shared/auth/components/login/login.component';
import { CallbackComponent } from '@shared/auth/components/callback/callback.component';
import { LaunchpadHomeComponent } from './pages/launchpad-home/launchpad-home.component';
import { AgentsFilterComponent } from './pages/agents-filter/agents-filter.component';
import { MyAgentHomeComponent } from './shared/components/my-agent-home/my-agent-home.component';
import { AnalyticsComponent } from './shared/components/analytics/analytics.component';
import { DocumentsComponent } from './shared/components/documents/documents.component';

export const routes: Routes = [
  { path: 'login', component: LoginComponent },
  { path: 'callback', component: CallbackComponent },

  {
    path: 'dashboard',
    // canActivate: [MarketplaceAuthGuard],
    component: LaunchpadHomeComponent,
  },
  {
    path: 'agent-list',
    // canActivate: [MarketplaceAuthGuard],
    component: AgentsFilterComponent,
  },
  {
    path: 'my-agent-home',
    // canActivate: [MarketplaceAuthGuard],
    component: MyAgentHomeComponent,
  },
  {
    path: 'analytics',
    // canActivate: [MarketplaceAuthGuard],
    component: AnalyticsComponent,
  },
  {
    path: 'documents',
    // canActivate: [MarketplaceAuthGuard],
    component: DocumentsComponent,
  },
  {
    path: 'build',
    // canActivate: [MarketplaceAuthGuard],
    children: [
      {
        path: 'agents',
        loadComponent: () =>
          import('@shared/pages/agents/agents.component').then(
            (m) => m.AgentsComponent,
          ),
      },
      {
        path: 'agents/:type',
        loadComponent: () =>
          import('@shared/pages/agents/build-agents/build-agents.component').then(
            (m) => m.BuildAgentsComponent,
          ),
      },
      {
        path: 'agents/:type/execute',
        loadComponent: () =>
          import(
            '@shared/pages/agents/agent-execution/agent-execution.component'
          ).then((m) => m.AgentExecutionComponent),
      },
      {
        path: 'workflows',
        loadComponent: () =>
          import('@shared/pages/workflows/workflows.component').then(
            (m) => m.WorkflowsComponent,
          ),
      },
      {
        path: 'workflows/create',
        loadComponent: () =>
          import('@shared/pages/workflows/workflow-editor/workflow-editor.component').then(
            (m) => m.WorkflowEditorComponent,
          ),
      },
      {
        path: 'workflows/edit/:id',
        loadComponent: () =>
          import('@shared/pages/workflows/workflow-editor/workflow-editor.component').then(
            (m) => m.WorkflowEditorComponent,
          ),
      },
      {
        path: 'workflows/execute/:id',
        loadComponent: () =>
          import('@shared/pages/workflows/workflow-execution/workflow-execution.component').then(
            (m) => m.WorkflowExecutionComponent,
          ),
      },
    ],
  },
  {
    path: 'tools',
    redirectTo: '/dashboard',
  },
  {
    path: 'team',
    redirectTo: '/dashboard',
  },
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full',
  },
];
