<div class="marketplace-container">
  <app-hero
    heroType="Marketplace"
    (searchResultsChange)="onSearchResultsChange($event)"
    (searchQueryChange)="onSearchQueryChange($event)"
    (searchLoadingChange)="onSearchLoadingChange($event)"
    (sendClicked)="onSendClicked($event)"
  >
  </app-hero>
  <app-agents
    [agents]="agentsData"
    [showExploreButton]="false"
    [isMarketplace]="true"
    [searchResults]="searchResults"
    [searchQuery]="searchQuery"
    [isSearchLoading]="isSearchLoading"
  >
  </app-agents>
</div>
