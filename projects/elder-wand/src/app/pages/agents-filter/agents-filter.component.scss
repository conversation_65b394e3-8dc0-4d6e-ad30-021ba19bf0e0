// Marketplace layout with sticky hero and tabs
.marketplace-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// Fixed hero section
app-hero {
  position: sticky;
  top: 0;
  z-index: 100;
  flex-shrink: 0;
  background: white; // Ensure hero has background
}

// Agents component with sticky tabs and scrollable cards
app-agents {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; // Prevent overflow, let internal grid handle scrolling
}
