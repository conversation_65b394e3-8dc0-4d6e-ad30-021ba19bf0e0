import { Component, OnInit } from '@angular/core';
import {
  Agent,
  EntityResult,
} from '../../shared/interfaces/agent-list.interface';
import { AgentsComponent } from '../../shared/components/agents/agents.component';
import Hero from '../../shared/components/hero/hero.component';
import { EntityService } from '../../shared/services/entity.service';
import { RevelioSearchResult } from '../../shared/services/search.service';

@Component({
  selector: 'app-agents-filter',
  imports: [AgentsComponent, Hero],
  templateUrl: './agents-filter.component.html',
  styleUrl: './agents-filter.component.scss',
})
export class AgentsFilterComponent implements OnInit {
  agentsData: Agent[] = [];
  searchResults: RevelioSearchResult[] = [];
  searchQuery: string = '';
  isSearchLoading: boolean = false;

  constructor(private entityService: EntityService) {}

  ngOnInit() {
    this.loadAgents();
  }

  /**
   * Load agents and convert entity data to agent format
   */
  private loadAgents(): void {
    this.entityService.getAgents(0, 50).subscribe({
      next: (entityAgents) => {
        // Convert entity agents to the expected agent format
        this.agentsData = entityAgents.map((entityAgent) => ({
          id: entityAgent.id,
          title: entityAgent.name,
          description: entityAgent.description,
          rating: 4.5, // Default rating since it's not in the API
          studio: {
            name: 'Experience Studio', // Default studio
            type: 'Experience Studio',
            backgroundColor: '#FFF4F9',
            textColor: '#DC047B',
          },
          users: Math.floor(Math.random() * 100) + 10, // Random users count for now
        }));
      },
      error: (error) => {
        console.error('Error loading agents:', error);
        // Fallback to empty array
        this.agentsData = [];
      },
    });
  }

  /**
   * Handle search results from the search bar
   */
  onSearchResultsChange(results: RevelioSearchResult[]): void {
    this.searchResults = results;
  }

  /**
   * Handle search query change from the search bar
   */
  onSearchQueryChange(query: string): void {
    this.searchQuery = query;
  }

  /**
   * Handle search loading change from the search bar
   */
  onSearchLoadingChange(isLoading: boolean): void {
    this.isSearchLoading = isLoading;
  }

  /**
   * Handle send clicked from the search bar
   */
  onSendClicked(query: string): void {
    console.log('Search query sent:', query);
    // You can add additional logic here if needed
  }
}
