<div class="agent-details-panel" *ngIf="agent">
  <div class="details-header">
    <button class="close-btn" (click)="onClose()">
      <ava-icon iconName="x" iconSize="36px" iconColor="#52577A"></ava-icon>
    </button>
  </div>

  <div class="details-content">
    <div class="upper-content">
      <div>
        <div class="details-title">
          <h2>{{ getAgentName() }}</h2>
        </div>

        <p class="details-description">
          Effortlessly convert Ruby code to Spring Boot with optimised migration
          <!-- {{ getAgentDescription() }} -->
              </p>

        <!-- Add to List Button -->
        <div class="add-to-list">
          <ava-icon
            iconName="text"
            iconSize="16px"
            iconColor="black"
          ></ava-icon>
          <span>Add to List</span>
        </div>

        <!-- Tags Section -->
        <div class="agent-tags">
          <span class="tag">#1 in Agents</span>
          <span class="tag">Code Migration</span>
          <span class="tag">Development</span>
          <span class="tag">Backend</span>
        </div>

        <!-- Orange Divider -->
        <div class="agent-divider"></div>

        <div class="details-metrics">
          <!-- First Row: Top Labels -->
          <div class="metrics-row">
            <div class="metric">
              <span class="label-1">Category</span>
            </div>
            <div class="metric">
              <span class="label-1">Developed</span>
            </div>
            <div class="metric">
              <span class="label-1">Relevancy</span>
            </div>
            <div class="metric">
              <span class="label-1">Agent</span>
            </div>
          </div>

          <!-- Second Row: Icons/Values -->
          <div class="metrics-row">
            <div class="metric">
              <ava-icon
                iconName="code"
                iconSize="25px"
                iconColor="#616874"
                class="code-icon"
              ></ava-icon>
            </div>
            <div class="metric">
              <ava-icon
                iconName="user"
                iconSize="25px"
                iconColor="#616874"
                class="profile-svg-icon"
              ></ava-icon>
            </div>
            <div class="metric">
              <div class="score">{{ getAgentScore() }}/10</div>
            </div>
            <div class="metric">
              <div class="rating">
                <span class="score">{{ agent.rating || 4.5 }}</span>
                <ava-icon
                  iconName="star"
                  iconSize="18px"
                  iconColor="#616874"
                  class="agent-star-icon"
                ></ava-icon>
              </div>
            </div>
          </div>

          <!-- Third Row: Bottom Labels -->
          <div class="metrics-row">
            <div class="metric">
              <span class="label">{{ agent.entityData?.entityType || 'agent' }}</span>
            </div>
            <div class="metric">
              <span class="label">{{ getCreatedBy() }}</span>
            </div>
            <div class="metric">
              <span class="label">Score</span>
            </div>
            <div class="metric">
              <span class="label">Rating</span>
            </div>
          </div>
        </div>

        <div class="agent-divider"></div>
      </div>

      <div>
        <div class="details-section">
          <h3>What it's for</h3>
          <p>
           {{ getAgentDescription() }}
          </p>
        </div>
      </div>
    </div>

    <div class="action-button mb-3">
      <ava-button
        label="Go to Playground"
        variant="primary"
        size="large"
        width="100%"
        (userClick)="onGoToPlayground()"
        [pill]="false"
      ></ava-button>
    </div>
  </div>
</div>
