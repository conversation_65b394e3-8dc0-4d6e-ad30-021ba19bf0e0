@keyframes slideIn {
  from {
    transform: translateX(100%);
  }

  to {
    transform: translateX(0);
  }
}

:host {
  display: block;
  height: 100%;
  width: 100%;
}

.agent-details-panel {
  width: 100%;
  height: 100%;
  background-color: #fff;
  box-shadow: -4px 0 24px rgba(0, 0, 0, 0.1);
  animation: slideIn 0.3s ease;
  display: flex;
  flex-direction: column;
  position: relative;
  font-family: 'Mulish', sans-serif;

  .details-header {
    padding: 0;
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;

    .close-btn {
      background: none;
      border: none;
      cursor: pointer;
      font-size: 24px;
      color: #666;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 50%;
    }
  }

  .details-content {
    padding: 52px 32px 0 32px;
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow-y: auto;
    min-height: 0;

    .upper-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 30px;
      padding-bottom: 30px;
    }

    .add-to-list {
      display: flex;
      align-items: center;
      border-radius: 24px;
      height: 32px;
      width: fit-content;
      padding: 0 16px;
      gap: 0;
      font-size: 16px;
      font-weight: 500;
      color: #6B7280;
      margin: 0 0 32px 0;
      background: #fff;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        inset: 0;
        padding: 1px;
        background: linear-gradient(180deg, #F06896 0%, #997BCF 100%);
        border-radius: 24px;
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: xor;
        -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
      }

      ava-icon {
        margin-right: 2px;
        display: flex;
        align-items: center;
      }
    }

    .add-to-list button,
    .add-to-list awe-button,
    .add-to-list awe-button button {
      min-width: 0 !important;
    }



    .agent-tags {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
      gap: 12px;
      margin: 12px 0 16px 0;
      padding: 0;
      width: auto;
      justify-content: flex-start;
      margin: 0 0 32px 0;

      .tag {
        display: flex;
        align-items: center;
        text-align: center;
        color: #23262F;
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
        padding: 12px 24px;
        border-radius: 24px;
        height: 38px;
        background-color: #EDEDF3;
        max-width: 170px;
      }
    }

    .details-title {
      h2 {
        color: #4C515B;
        font-family: Mulish;
        font-size: 32px;
        font-style: normal;
        font-weight: 700;
        line-height: 38.4px;
        margin-bottom: 12px;
        margin-top: 0;
      }
    }

    .details-description {
      color: #6B7280;
      font-family: Mulish;
      font-size: 20px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
      margin-bottom: 8px;
      text-align: left;
      margin-top: 0;
      margin-top: 10px;
      margin-bottom: 12px;
    }

    .details-metrics {
      display: flex;
      flex-direction: column;
      margin-bottom: 10px;
      padding: 0;

      .metrics-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .metric {
        flex: 1;
        min-width: auto;
        text-align: center;

        .label-1 {
          color: #858AAD;
          text-align: center;
          font-family: Mulish;
          font-size: 16px;
          font-style: normal;
          font-weight: 800;
          line-height: 110%;
        }

        .label {
          color: #666D99;
          font-family: Mulish;
          font-size: 19px;
          font-style: normal;
          font-weight: 600;
          line-height: 97%;
        }

        .score {
          color: #616874;
          font-family: Mulish;
          font-size: 20px;
          font-style: normal;
          font-weight: 700;
          line-height: 150%;
        }

        .rating {
          color: #616874;
          font-family: Mulish;
          font-size: 20px;
          font-style: normal;
          font-weight: 700;
          line-height: 150%;
          .agent-star-icon{
            padding-bottom: 6px;
            margin-left: 4px;
          }
        }
      }
    }

    .details-section {
      margin-bottom: 15px;
      border-radius: 12px;
      background: linear-gradient(180deg, #FDE9EF 0%,  #F0EBF8 100%);
      padding: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      h3 {
        color: #4C515B;
        font-family: Mulish;
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: 28.8px;
        text-align: left;
        margin-bottom: 12px;
        margin-top: 0;
      }

      p {
        color: #616874;
        font-family: Inter;
        font-size: 1rem;
        font-style: normal;
        font-weight: 400;
        line-height: 150%;
        text-align: left;
      }

      .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-top: 16px;

        .info-item {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .info-label {
            color: #858AAD;
            font-family: Mulish;
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 110%;
          }

          .info-value {
            color: #4C515B;
            font-family: Mulish;
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 120%;
            word-break: break-word;
          }
        }
      }

      .type-badge {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 0.875;
        font-family: 'Mulish', sans-serif;

        .icon {
          font-size: 1rem;
        }
      }
    }
  }

  .action-button {
    width: 100%;
    box-sizing: border-box;
    margin-top: auto;
    flex-shrink: 0;
  }
}

.agent-divider {
  width: 100%;
  height: 1px;
  background: linear-gradient(180deg, #F06896 0%, #997BCF 100%);
  border: none;
  margin: 32px 0;
  border-radius: 1px;
}

// Remove right margin from awe-button globally in this component
:host ::ng-deep awe-button {
  margin-top: 0 !important;
  padding: 0 !important;
}
