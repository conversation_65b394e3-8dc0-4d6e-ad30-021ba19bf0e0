import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { IconComponent, ButtonComponent as <PERSON><PERSON>uttonComponent } from "@ava/play-comp-library";
import { Agent, EntityResult } from '../../interfaces/agent-list.interface';

export interface AgentWithEntityData extends Agent {
  entityData?: EntityResult;
}

@Component({
  selector: 'app-agent-details-panel',
  templateUrl: './agent-details-panel.component.html',
  styleUrls: ['./agent-details-panel.component.scss'],
  imports: [CommonModule, IconComponent, AvaButtonComponent]
})
export class AgentDetailsPanelComponent {
  @Input() agent: AgentWithEntityData | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() goToPlayground = new EventEmitter<AgentWithEntityData>();

  constructor(private router: Router) {}

  /**
   * Closes the agent details panel
   */
  onClose(): void {
    this.close.emit();
  }

  /**
   * Navigates to the playground for the selected agent
   */
  onGoToPlayground(): void {
    if (this.agent) {
      this.goToPlayground.emit(this.agent);
    }
  }

  /**
   * Get the agent name from entity data or fallback to title
   */
  getAgentName(): string {
    return this.agent?.entityData?.name || this.agent?.title || 'Unknown Agent';
  }

  /**
   * Get the agent description from entity data or fallback to description
   */
  getAgentDescription(): string {
    return this.agent?.entityData?.description || this.agent?.description || 'No description available';
  }

  /**
   * Get the agent details from entity data
   */
  getAgentDetails(): string {
    return this.agent?.entityData?.details || 'No details available';
  }

  /**
   * Get the created by information
   */
  getCreatedBy(): string {
    return this.agent?.entityData?.createdBy || 'Unknown';
  }

  /**
   * Get the modified by information
   */
  getModifiedBy(): string {
    return this.agent?.entityData?.modifiedBy || 'Unknown';
  }

  /**
   * Get the agent score
   */
  getAgentScore(): number {
    return this.agent?.entityData?.score || 0;
  }
}
