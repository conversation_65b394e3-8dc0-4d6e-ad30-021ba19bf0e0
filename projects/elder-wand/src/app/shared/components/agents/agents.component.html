<div
  class="agents-container"
  [class.marketplace-container]="isMarketplace"
  [class.launchpad-container]="!isMarketplace"
>
  <!-- Loading State - show in marketplace when loading agents or when search is running -->
  <div
    class="loading-container"
    *ngIf="(loading || isSearchLoading) && isMarketplace"
  >
    <div class="loading-spinner">
      <ava-icon
        iconName="refresh-cw"
        iconSize="24px"
        iconColor="#616874"
        class="spinning"
      ></ava-icon>
      <p>{{ isSearchLoading ? "Searching agents..." : "Loading agents..." }}</p>
    </div>
  </div>
 
  <!-- Content when not loading or when in launchpad (always show content) -->
  <div
    *ngIf="(!loading && !isSearchLoading) || !isMarketplace"
    class="agents-content"
    [class.marketplace-content]="isMarketplace"
    [class.launchpad-content]="!isMarketplace"
  >
    <app-filter-tabs
      *ngIf="!showExploreButton"
      [tabs]="filterTabs"
      [activeTab]="activeFilter"
      (tabChange)="onFilterChange($event)"
    >
    </app-filter-tabs>
    <!-- No agents found message for search results - moved to top -->
    <div
      class="no-agents"
      *ngIf="
        !loading &&
        !isSearchLoading &&
        searchQuery.trim() &&
        (searchResults.length === 0 || getDisplayedAgents().length === 0)
      "
    >
      <ava-icon
        iconName="search"
        iconSize="48px"
        iconColor="#858aad"
      ></ava-icon>
      <h3>No agent present</h3>
      <p>No agents found matching your search query "{{ searchQuery }}"</p>
    </div>
 
    <div
      class="agents-grid-container"
      [class.marketplace-scroll]="isMarketplace"
      [class.launchpad-no-scroll]="!isMarketplace"
      *ngIf="
        !(
          !loading &&
          !isSearchLoading &&
          searchQuery.trim() &&
          (searchResults.length === 0 || getDisplayedAgents().length === 0)
        )
      "
    >
      <div class="agents-grid row">
        <div
          [ngClass]="
            showTwoColumns ? 'col-12 col-md-6' : 'col-12 col-md-6 col-lg-3'
          "
          *ngFor="let agent of getDisplayedAgents()"
        >
          <div
            class="agent-card"
            [class.marketplace-card]="isMarketplace"
            (click)="showAgentDetails(agent)"
          >
            <div class="card-content">
              <div class="card-header truncate-header">
                <h2 class="agent-title-truncate">{{ agent.title }}</h2>
                <div class="rating">
                  <ava-icon
                    iconName="star"
                    iconSize="18px"
                    iconColor="#FFD700"
                    class="agent_star-icon"
                  ></ava-icon>
                  {{ agent.rating }}
                </div>
              </div>
 
              <p class="description">{{ agent.description | truncate: 75 }}</p>
 
              <!-- Footer comments - only show in marketplace -->
              <div class="card-footer" *ngIf="isMarketplace">
                <div class="users">
                  <ava-icon
                    iconName="user"
                    iconSize="16px"
                    iconColor="#858aad"
                    class="profile-svg-icon"
                  ></ava-icon>
                  {{ agent.users }}
                </div>
                <div class="agent-time-ago">3 days ago</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
 
    <!-- No agents found message (general) -->
    <div
      class="no-agents"
      *ngIf="
        !loading &&
        !isSearchLoading &&
        !searchQuery.trim() &&
        agents.length === 0
      "
    >
      <ava-icon
        iconName="search"
        iconSize="48px"
        iconColor="#858aad"
      ></ava-icon>
      <h3>No agents found</h3>
      <p>Try adjusting your filters or search criteria</p>
    </div>
  </div>
</div>