import {
  Component,
  Input,
  Pipe,
  PipeTransform,
  ViewEncapsulation,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';

import { Agent, EntityResult } from '../../interfaces/agent-list.interface';
import {
  FilterTabsComponent,
  FilterTab,
} from '../filter-tabs/filter-tabs.component';
import { IconComponent } from '@ava/play-comp-library';
import { DrawerService } from '../../services/drawer.service';
import { EntityService } from '../../services/entity.service';
import { RevelioSearchResult } from '../../services/search.service';

@Pipe({
  name: 'truncate',
  standalone: true,
})
export class TruncatePipe implements PipeTransform {
  transform(value: string, limit = 75): string {
    if (!value) return '';
    if (value.length <= limit) return value;
    return value.substring(0, limit) + '...';
  }
}

@Component({
  selector: 'app-agents',
  templateUrl: './agents.component.html',
  styleUrls: ['./agents.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FilterTabsComponent,
    TruncatePipe,
    IconComponent,
  ],
  encapsulation: ViewEncapsulation.None,
})
export class AgentsComponent implements OnInit {
  @Input() agents: Agent[] = [];
  @Input() showExploreButton = true;
  @Input() showTwoColumns = false;
  @Input() isMarketplace = false; // New input to detect marketplace context
  @Input() searchResults: RevelioSearchResult[] = []; // Search results from search bar
  @Input() searchQuery: string = ''; // Current search query
  @Input() isSearchLoading: boolean = false; // Search loading state

  private originalAgents: Agent[] = [];
  entityAgents: EntityResult[] = [];
  loading = false;

  activeFilter = 'all';
  filterTabs: FilterTab[] = [
    { id: 'all', label: 'All', priority: 100, disabled: false },
    {
      id: 'experience',
      label: 'Experience Studio',
      icon: 'lightbulb',
      priority: 90,
      disabled: true,
    },
    {
      id: 'product',
      label: 'Product Studio',
      icon: 'box',
      priority: 80,
      disabled: true,
    },
    {
      id: 'data',
      label: 'Data Studio',
      icon: 'database',
      priority: 70,
      disabled: true,
    },
    {
      id: 'finops',
      label: 'FinOps Studio',
      icon: 'dollar-sign',
      priority: 60,
      disabled: true,
    },
  ];
  constructor(
    private readonly router: Router,
    private readonly drawerService: DrawerService,
    private readonly entityService: EntityService,
  ) {}

  ngOnInit() {
    this.originalAgents = [...this.agents];
    this.loadEntityAgents();
  }

  /**
   * Load agents from the entity API
   */
  private loadEntityAgents(): void {
    this.loading = true;
    this.entityService.getAgents(0, 50).subscribe({
      next: (agents: EntityResult[]) => {
        this.entityAgents = agents;
        this.loading = false;
      },
      error: (error: any) => {
        console.error('Error loading agents:', error);
        this.loading = false;
      },
    });
  }

  onFilterChange(filterId: string) {
    this.activeFilter = filterId;
    // Filter agents by studio type if not 'all'
    if (filterId === 'all') {
      this.agents = [...this.originalAgents];
    } else {
      const studioMap: any = {
        experience: 'Experience Studio',
        product: 'Product Studio',
        data: 'Data Studio',
        finops: 'FinOps Studio',
      };
      this.agents = this.originalAgents.filter(
        (agent: Agent) => agent.studio?.type === studioMap[filterId],
      );
    }
  }

  navigate() {
    this.router.navigateByUrl('/agent-list');
  }

  /**
   * Shows the agent details panel for the selected agent
   * @param agent The agent to display details for
   */
  showAgentDetails(agent: Agent): void {
    // Find the corresponding entity data
    const entityAgent = this.entityAgents.find((ea) => ea.id === agent.id);

    console.log('Showing agent details for:', agent, entityAgent);

    this.drawerService.open({
      data: { ...agent, entityData: entityAgent },
      width: '650px',
      position: 'right',
      onGoToPlayground: (selectedAgent: Agent) =>
        this.goToPlayground(selectedAgent),
    });
  }

  /**
   * Navigates to the playground for the selected agent
   */
  goToPlayground(agent: Agent): void {
    if (agent) {
      // You can replace this with the actual navigation logic
      this.router.navigate(['/playground', agent.id]);
      // Close the drawer after navigation
      this.drawerService.close();
    }
  }

  /**
   * Gets the agents to display based on the showTwoColumns setting and search results
   * Returns only 6 agents (3 rows x 2 columns) when showTwoColumns is true
   */
  getDisplayedAgents(): Agent[] {
    let agentsToDisplay = this.agents;

    // If there's a search query, handle search results
    if (this.searchQuery.trim()) {
      // If no search results, return empty array
      if (this.searchResults.length === 0) {
        return [];
      }

      // If there are search results, filter agents to show only those that match
      const searchResultIds = this.searchResults.map((result) => result.id);

      // Check if we have all the search result agents in our current list
      const missingAgentIds = searchResultIds.filter(
        (id) => !this.agents.some((agent) => agent.id.toString() === id),
      );

      if (missingAgentIds.length > 0) {
        console.log('Missing agent IDs from search results:', missingAgentIds);
        // Create agent objects directly from search results
        const searchResultAgents = this.searchResults.map((revelioResult) => ({
          id: parseInt(revelioResult.id) || 0, // Convert string id to number
          title: revelioResult.metadata.name || 'Unknown Agent',
          description:
            revelioResult.metadata.description ||
            revelioResult.metadata.details ||
            '',
          rating: 4.5, // Default rating
          studio: {
            name: 'Experience Studio', // Default studio
            type: 'Experience Studio',
            backgroundColor: '#FFF4F9',
            textColor: '#E91E63',
          },
          developer: revelioResult.metadata.createdBy || 'Unknown Developer',
          users: Math.floor(Math.random() * 100) + 10, // Random users count
        }));

        // Filter by active tab if not 'all' and we're in marketplace
        if (this.activeFilter !== 'all' && this.isMarketplace) {
          const studioMap: any = {
            experience: 'Experience Studio',
            product: 'Product Studio',
            data: 'Data Studio',
            finops: 'FinOps Studio',
          };
          const targetStudioType = studioMap[this.activeFilter];
          agentsToDisplay = searchResultAgents.filter(
            (agent) => agent.studio?.type === targetStudioType,
          );
        } else {
          agentsToDisplay = searchResultAgents;
        }
      } else {
        // All search results are in our current list, filter normally
        let filteredAgents = this.agents.filter((agent) => {
          const isMatch = searchResultIds.includes(agent.id.toString());
          console.log(`Agent ${agent.id} (${agent.title}) - Match: ${isMatch}`);
          return isMatch;
        });

        // Further filter by active tab if not 'all' and we're in marketplace
        if (this.activeFilter !== 'all' && this.isMarketplace) {
          const studioMap: any = {
            experience: 'Experience Studio',
            product: 'Product Studio',
            data: 'Data Studio',
            finops: 'FinOps Studio',
          };
          const targetStudioType = studioMap[this.activeFilter];
          agentsToDisplay = filteredAgents.filter(
            (agent) => agent.studio?.type === targetStudioType,
          );
        } else {
          agentsToDisplay = filteredAgents;
        }
      }
    }

    if (this.showTwoColumns) {
      return agentsToDisplay.slice(0, 6); // Show only 6 agents for 3 rows x 2 columns
    }
    return agentsToDisplay; // Show all agents in normal mode
  }

  /**
   * Check if we should show the search results message (only in marketplace)
   */
  get shouldShowSearchResults(): boolean {
    return (
      this.searchResults.length > 0 &&
      this.searchQuery.trim() !== '' &&
      this.isMarketplace
    );
  }

  /**
   * Get the search results count
   */
  get searchResultsCount(): number {
    return this.searchResults.length;
  }
}
