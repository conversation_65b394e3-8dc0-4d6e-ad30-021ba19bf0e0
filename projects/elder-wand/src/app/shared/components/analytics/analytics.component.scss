.analytics {
  padding: 0 32px 40px 32px;
  margin: 40px 0;
  position: relative;

  // Coming Soon Overlay Styles
  .coming-soon-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 16px;
    animation: fadeInOverlay 0.8s ease-out;
  }

  .coming-soon-content {
    text-align: center;
    padding: 40px;
    animation: slideInUp 1s ease-out 0.3s both;
  }

  .coming-soon-icon {
    position: relative;
    display: inline-block;
    margin-bottom: 32px;
    
    svg {
      width: 64px;
      height: 64px;
      color: #667eea;
      position: relative;
      z-index: 3;
      animation: sparkle 2s ease-in-out infinite;
    }

    .pulse-ring {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100px;
      height: 100px;
      border: 2px solid #667eea;
      border-radius: 50%;
      opacity: 0;
      animation: pulseRing 2s ease-out infinite;
    }

    .pulse-ring-2 {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 120px;
      height: 120px;
      border: 1px solid #8b5cf6;
      border-radius: 50%;
      opacity: 0;
      animation: pulseRing 2s ease-out infinite 0.5s;
    }
  }

  .coming-soon-title {
    font-family: 'Mulish', sans-serif;
    font-size: 36px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0 0 16px 0;
    animation: titleGlow 3s ease-in-out infinite alternate;
  }

  .coming-soon-subtitle {
    font-family: 'Mulish', sans-serif;
    font-size: 18px;
    font-weight: 400;
    color: #6b7280;
    margin: 0 0 32px 0;
    opacity: 0;
    animation: fadeInText 1s ease-out 0.8s both;
  }

  .coming-soon-dots {
    display: flex;
    justify-content: center;
    gap: 8px;
    
    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #667eea;
      opacity: 0.3;
      animation: dotPulse 1.5s ease-in-out infinite;

      &:nth-child(1) {
        animation-delay: 0s;
      }
      
      &:nth-child(2) {
        animation-delay: 0.2s;
      }
      
      &:nth-child(3) {
        animation-delay: 0.4s;
      }
    }
  }

  // Animations
  @keyframes fadeInOverlay {
    from {
      opacity: 0;
      backdrop-filter: blur(0px);
    }
    to {
      opacity: 1;
      backdrop-filter: blur(3px);
    }
  }

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes sparkle {
    0%, 100% {
      transform: scale(1) rotate(0deg);
    }
    25% {
      transform: scale(1.1) rotate(90deg);
    }
    50% {
      transform: scale(1) rotate(180deg);
    }
    75% {
      transform: scale(1.1) rotate(270deg);
    }
  }

  @keyframes pulseRing {
    0% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.5);
    }
    50% {
      opacity: 0.3;
    }
    100% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(1.2);
    }
  }

  @keyframes titleGlow {
    0% {
      filter: brightness(1);
    }
    100% {
      filter: brightness(1.2);
    }
  }

  @keyframes fadeInText {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes dotPulse {
    0%, 100% {
      opacity: 0.3;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.2);
    }
  }

  .analytics-header {
    text-align: center;

    .title-wrapper {
      display: inline-flex;
      align-items: center;
      gap: 12px;
      margin: 0;

      .sparkle {
        font-size: 28px;
      }

      h1 {
        color: #14161F;
        text-align: center;
        font-family: Mulish;
        font-size: 48px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        letter-spacing: -0.912px;
        margin: 0 0 24px 0;
      }
    }
  }

  .stats-row {
    .stat-card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      margin-bottom: 24px;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      height: 235px;



      .stat-icon {
        width: 40px;
        height: 40px;
        border-radius: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        margin-bottom: 16px;

        &.blue {
          background: #eef2ff;
          color: #6366f1;
        }

        &.orange {
          background: #fff7ed;
          color: #f97316;
        }

        &.pink {
          background: #fdf2f8;
          color: #ec4899;
        }
      }

      .stat-content {
        position: relative;
        z-index: 2;

        h3 {
          font-size: 14px;
          color: #6b7280;
          margin: 0 0 8px;
          font-weight: 500;
        }

        .stat-value {
          font-size: 28px;
          font-weight: 600;
          color: #111827;
          margin-bottom: 8px;
        }
      }

      .stat-change {
        font-size: 14px !important;

        &.positive {
          color: #22c55e !important;
          display: flex;
          align-items: center;
          gap: 4px;

          fa-icon {
            font-size: 12px !important;
          }
        }
      }

      .stat-chart {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 60%;
        // height: 70px;
        opacity: 0.7;
      }

      &:first-child {
        background: linear-gradient(to bottom right, white, #fafafa);

        &::after {
          content: "";
          position: absolute;
          bottom: 0;
          right: 0;
          width: 60%;
          // height: 70px;
          background: linear-gradient(90deg,
              rgba(99, 102, 241, 0.05) 0%,
              rgba(99, 102, 241, 0.15) 100%);
          border-radius: 100px 0 0 0;
          opacity: 0.8;
        }
      }
    }
  }


  .map-card {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    height: 100% !important;

    .map-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h3 {
        font-size: 16px;
        color: #6b7280;
        margin: 0;
        font-weight: 500;
      }

      .success-rate {
        color: #22c55e;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .user-count {
      font-size: 36px;
      font-weight: 600;
      color: #111827;
      margin-bottom: 24px;
    }

    .world-map {
      height: 240px;
      background: #f8fafc;
      border-radius: 12px;
      position: relative;
      overflow: hidden;

      &::after {
        content: "";
        position: absolute;
        inset: 0;
        background-image: radial-gradient(circle, #e2e8f0 1px, transparent 1px);
        background-size: 16px 16px;
        opacity: 0.3;
      }
    }
  }

  .latencies-card {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    height: 100% !important;

    h3 {
      font-size: 16px;
      color: #111827;
      margin: 0 0 20px;
      font-weight: 600;
    }

    table {
      width: 100%;
      border-collapse: collapse;

      th {
        font-weight: 500;
        color: #6b7280;
        padding: 12px 16px;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
        font-size: 14px;

        &:first-child {
          padding-left: 0;
        }
      }

      td {
        padding: 12px 16px;
        border-bottom: 1px solid #e5e7eb;
        color: #111827;
        font-size: 14px;

        &:first-child {
          padding-left: 0;
        }

        &:nth-child(2) {
          color: #6b7280;
        }
      }

      .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        display: inline-block;
        line-height: 1.5;

        &.good {
          background: #f0fdf4;
          color: #22c55e;
        }

        &.medium {
          background: #fff7ed;
          color: #f97316;
        }
      }
    }
  }

  .code-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .code-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      h3 {
        font-size: 16px;
        color: #111827;
        margin: 0;
        font-weight: 600;
      }

      .efficiency {
        color: #22c55e;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .code-stats {
      display: flex;
      gap: 2rem;
      margin-bottom: 1rem;

      .total-lines {
        color: #bd93f9;
        font-size: 1.5rem;
        font-weight: 600;
      }

      .monthly-lines {
        color: #6272a4;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
      }
    }

    .code-preview {
      background: #1e1e2e;
      border-radius: 8px;
      padding: 1rem;
      margin-top: 1rem;
      overflow: auto;
      max-height: 750px;

      pre {
        margin: 0;

        code {
          font-family: "JetBrains Mono", monospace;
          font-size: 14px;
          line-height: 1.6;

          .line {
            display: flex;
            gap: 1rem;
            padding: 0.125rem 0;

            &:hover {
              background: rgba(255, 255, 255, 0.05);
            }

            .line-number {
              color: #6272a4;
              opacity: 0.5;
              min-width: 24px;
              text-align: right;
              user-select: none;
            }

            .code-content {
              color: #f8f8f2;
              display: flex;
              white-space: nowrap;

              .keyword {
                color: #ff79c6;
              }

              .module {
                color: #50fa7b;
              }

              .string {
                color: #f1fa8c;
              }

              .punctuation {
                color: #bd93f9;
              }
            }
          }
        }
      }
    }
  }
}