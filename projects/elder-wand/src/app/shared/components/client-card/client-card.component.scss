.client-card {
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  margin: 0 auto;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid #f1f5f9;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }
}

// Company Logo Styles
.client-logo {
  margin-bottom: 16px; // 16px gap between header content and footer

  .logo-image {
    height: 32px;
    width: auto;
    object-fit: contain;
    max-width: 120px;
  }
}

// Testimonial Styles (Content)
.client-testimonial {
  margin-bottom: 16px;
  flex: 1;
  min-height: 96px;
  display: flex; /* Use flexbox for better control */
  flex-direction: column; /* Stack children vertically */
}

.testimonial-text {
  width: 280px;
  color: var(--<PERSON><PERSON><PERSON><PERSON><PERSON>, #5D6180);
  font-family: Mulish;
  font-size: 16px;
  font-style: italic;
  font-weight: 400;
  line-height: 150%;
  margin: 0;
  overflow: hidden; /* Hide overflow */
  display: -webkit-box;
  -webkit-line-clamp: 4; /* Limit to 4 lines */
  -webkit-box-orient: vertical; /* Stack lines vertically */
}


// Author Styles (Footer)
.client-author {
  display: flex;
  align-items: center;
  gap: 12px;

  .author-avatar {
    flex-shrink: 0;

    .avatar-image {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid #e2e8f0;
    }
  }

  .author-info {
    flex: 1;

    .author-name {
      color: var(--Greyscale-Dark-Grey, #060C3C);
      font-variant-numeric: ordinal;
      font-family: Mulish;
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      line-height: 24px;
      margin: 0 0 2px 0;
      max-height: 24px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .author-role {
      color: var(--Purple-Purple, #6241D4);
      font-family: Mulish;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 26px;
      margin: 0;
      max-height: 28px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}


// Variant Styles
.client-compact {
  padding: 20px;
  max-width: 280px;

  .client-logo .logo-image {
    height: 28px;
    max-width: 100px;
  }

  .client-testimonial .testimonial-text {
    font-size: 13px;
  }

  .client-author {
    gap: 10px;

    .author-avatar .avatar-image {
      width: 36px;
      height: 36px;
    }

    .author-info {
      .author-name {
        font-size: 13px;
      }

      .author-role {
        font-size: 11px;
      }
    }
  }
}

.client-featured {
  padding: 32px;
  max-width: 400px;

  .client-logo .logo-image {
    height: 40px;
    max-width: 150px;
  }

  .client-testimonial .testimonial-text {
    font-size: 16px;
  }

  .client-author {
    gap: 16px;

    .author-avatar .avatar-image {
      width: 48px;
      height: 48px;
    }

    .author-info {
      .author-name {
        font-size: 16px;
      }

      .author-role {
        font-size: 14px;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .client-card {
    padding: 20px;
    max-width: 100%;
    margin: 0;

    .client-logo .logo-image {
      height: 28px;
      max-width: 100px;
    }

    .client-testimonial .testimonial-text {
      font-size: 13px;
    }

    .client-author {
      gap: 10px;

      .author-avatar .avatar-image {
        width: 36px;
        height: 36px;
      }

      .author-info {
        .author-name {
          font-size: 13px;
        }

        .author-role {
          font-size: 11px;
        }
      }
    }
  }

  .client-featured {
    padding: 24px;

    .client-logo .logo-image {
      height: 32px;
      max-width: 120px;
    }

    .client-testimonial .testimonial-text {
      font-size: 14px;
    }
  }
}

@media (max-width: 480px) {
  .client-card {
    padding: 16px;

    .client-logo .logo-image {
      height: 24px;
      max-width: 80px;
    }

    .client-testimonial .testimonial-text {
      font-size: 12px;
    }

    .client-author {
      gap: 8px;

      .author-avatar .avatar-image {
        width: 32px;
        height: 32px;
      }

      .author-info {
        .author-name {
          font-size: 12px;
        }

        .author-role {
          font-size: 10px;
        }
      }
    }
  }
}

// // Dark theme support
// @media (prefers-color-scheme: dark) {
//   .client-card {
//     background: #1a202c;
//     color: #e2e8f0;
//     border-color: #2d3748;

//     .client-testimonial .testimonial-text {
//       color: #cbd5e0;
//     }

//     .client-author {
//       .author-avatar .avatar-image {
//         border-color: #4a5568;
//       }

//       .author-info {
//         .author-name {
//           color: #f7fafc;
//         }

//         .author-role {
//           color: #a0aec0;
//         }
//       }
//     }
//   }
// }

// // Accessibility
// @media (prefers-reduced-motion: reduce) {
//   .client-card {
//     transition: none;
//   }

//   .client-card:hover {
//     transform: none;
//   }
// }

// // High contrast mode
// @media (prefers-contrast: high) {
//   .client-card {
//     border: 2px solid #000;
//     box-shadow: none;

//     .client-author .author-info .author-role {
//       color: #000;
//     }
//   }
// } 