<div class="filter-tabs">
  <div class="tabs-wrapper">
    <!-- Visible Tabs Container -->
    <div class="tabs-container">
      <button
        *ngFor="let tab of visibleTabs"
        class="tab-item"
        [class.active]="isActiveTab(tab.id)"
        [class.disabled]="tab.disabled"
        [disabled]="tab.disabled"
        (click)="onTabClick(tab.id)"
      >
        <ava-icon
          *ngIf="tab.icon"
          [iconName]="tab.icon"
          iconSize="24px"
          [iconColor]="tab.iconColor || '#000'"
        ></ava-icon>
        <span>{{ tab.label }}</span>
      </button>
    </div>
  </div>
</div>
