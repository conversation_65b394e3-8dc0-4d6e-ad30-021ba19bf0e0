<section class="hero-banner">
  <div class="hero-container" [ngClass]="'layout-' + layout">
    <!-- Text Content -->
    <div class="hero-content">
      <div class="hero-text">
        <h1 class="hero-title">
          <span [innerHTML]="title"></span>
          <br *ngIf="brandText" />
          <span class="brand-text" *ngIf="brandText">{{ brandText }}</span>
        </h1>

        <p class="hero-subtitle" *ngIf="subtitle">{{ subtitle }}</p>

        <p class="hero-description" *ngIf="description">{{ description }}</p>

        <!-- Actions -->
        <div class="hero-actions" *ngIf="actions.length > 0">
          <ava-button
            *ngFor="let action of actions"
            [label]="action.label"
            [variant]="action.variant"
            [size]="action.size"
            (userClick)="onActionClick(action)"
            iconName="move-right"
            iconPosition="right"
            iconColor="white"
          >
            <lucide-icon name="arrow-right" class="button-icon"></lucide-icon>
          </ava-button>
        </div>

        <!-- Status Message -->
        <div class="hero-status" *ngIf="showStatus && statusMessage">
          <p class="status-message">{{ statusMessage }}</p>
        </div>
      </div>
    </div>

    <!-- Hero Image -->
    <div class="hero-visual" *ngIf="imageSrc">
      <div class="hero-image-container">
        <img
          [src]="imageSrc"
          [alt]="imageAlt"
          class="hero-image"
          loading="lazy"
        />
        <div class="image-glow"></div>
      </div>
    </div>
  </div>
</section>
