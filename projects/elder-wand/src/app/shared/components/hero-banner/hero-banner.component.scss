.hero-banner {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: 3rem;
  position: relative;
  overflow: hidden;

  // Background animation
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    z-index: -1;
  }
}

.hero-container {
  margin: 0 auto;
  width: 100%;
  display: grid;
  align-items: center;
  gap: 6rem;

  // Layout variants
  &.layout-default,
  &.layout-image-right {
    grid-template-columns: 1.2fr 0.8fr;

    .hero-visual {
      order: 2;
    }
  }

  &.layout-image-left {
    grid-template-columns: 0.8fr 1.2fr; // Adjusted for image-left layout

    .hero-visual {
      order: 1;
    }

    .hero-content {
      order: 2;
    }
  }

  &.layout-centered {
    grid-template-columns: 1fr;
    text-align: center;
    max-width: 800px;

    .hero-visual {
      order: 2;
      justify-self: center;
    }
  }

  @media (max-width: 1024px) {
    padding: 0 40px; // Reduced padding for tablets
    gap: 4rem;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 0 20px; // Further reduced for mobile
    text-align: left; // Keep text left-aligned on mobile

    .hero-visual {
      order: 2 !important;
    }

    .hero-content {
      order: 1 !important;
    }
  }
}

.hero-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start; // Ensure content starts from the left
  text-align: left; // Explicitly set text alignment
}

.hero-text {
  .hero-title {
    // Layout styles
    align-self: stretch;

    // Typography
    color: var(--Global-colors-Neutral-n---800, #3b3f46);
    font-family: Mulish;
    font-size: 56px;
    font-style: normal;
    font-weight: 700;
    line-height: 64px; /* 114.286% */
    letter-spacing: -1.8px;
    margin-bottom: 1.5rem;

    .brand-text {
      // Gradient styles
      background: var(
        --Gradient-Gradient-Medium,
        linear-gradient(
          180deg,
          var(--Brand-Primary-300, #f06896) 0%,
          var(--Brand-Tertiary-300, #997bcf) 100%
        )
      );
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-family: Mulish;
      font-size: 56px;
      font-style: normal;
      font-weight: 700;
      line-height: 64px;
      letter-spacing: -1.8px;
      display: block; // Changed from inline-block to block to ensure new line
      margin-left: 0; // Removed left margin since it's on a new line
    }

    @media (max-width: 768px) {
      font-size: 42px;
      line-height: 48px;
      letter-spacing: -1.35px;

      .brand-text {
        font-size: 42px;
        line-height: 48px;
        letter-spacing: -1.35px;
      }
    }

    @media (max-width: 480px) {
      font-size: 32px;
      line-height: 36px;
      letter-spacing: -1px;

      .brand-text {
        font-size: 32px;
        line-height: 36px;
        letter-spacing: -1px;
      }
    }
  }

  .hero-subtitle {
    // Layout
    align-self: stretch;

    // Styles
    color: #6b6b6b;
    font-family: Mulish;
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: var(--Global-Typography-line-height-H3, 28.8px); /* 144% */
    opacity: 0.8;
    margin-bottom: 1rem;

    @media (max-width: 768px) {
      font-size: 18px;
      line-height: 25.2px;
    }

    @media (max-width: 480px) {
      font-size: 16px;
      line-height: 22.4px;
    }
  }

  .hero-description {
    // Layout
    width: 100%; // Changed to 100% to use available space
    max-width: 600px; // Set a reasonable max-width instead of fixed width

    // Styles
    color: #161c2d;
    font-family: Mulish;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 32px; /* 177.778% */
    letter-spacing: -0.2px;
    opacity: 0.7;
    margin-bottom: 2rem;

    @media (max-width: 768px) {
      max-width: 100%;
      font-size: 16px;
      line-height: 28px;
      letter-spacing: -0.16px;
    }

    @media (max-width: 480px) {
      font-size: 14px;
      line-height: 24px;
      letter-spacing: -0.14px;
    }
  }

  ava-button {
    color: white;
  }

  .hero-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;

    // Button icon styles
    ::ng-deep .button-icon {
      margin-left: 8px;
      width: 16px;
      height: 16px;
    }

    @media (max-width: 480px) {
      flex-direction: column;
      align-items: center;
    }
  }

  .hero-status {
    .status-message {
      padding: 1rem;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      font-size: 0.9rem;
      opacity: 0.9;
      animation: fadeInUp 0.5s ease-in-out;
      margin: 0;
    }
  }
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.hero-image-container {
  position: relative;
  max-width: 100%;
  animation: floatAnimation 6s ease-in-out infinite;

  .hero-image {
    width: 100%;
    height: auto;
    max-width: 700px; // Increased to make image bigger as requested
    // min-width: 500px; // Added minimum width to ensure image stays prominent
    border-radius: 20px;
    // box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    transition:
      transform 0.3s ease,
      box-shadow 0.3s ease;

    // &:hover {
    //   transform: scale(1.02); // Reduced from 1.05 to prevent too much movement
    //   box-shadow: 0 30px 80px rgba(0, 0, 0, 0.3);
    // }

    @media (max-width: 1024px) {
      max-width: 600px;
      // min-width: 400px;
    }

    @media (max-width: 768px) {
      max-width: 500px;
      // min-width: 300px;
    }

    @media (max-width: 480px) {
      max-width: 350px;
      // min-width: 250px;
    }
  }

  .image-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120%;
    height: 120%;
    background: radial-gradient(
      circle,
      rgba(102, 126, 234, 0.3) 0%,
      rgba(118, 75, 162, 0.2) 50%,
      transparent 70%
    );
    border-radius: 50%;
    z-index: -1;
    animation: pulseGlow 3s ease-in-out infinite alternate;
  }
}

// Animations
@keyframes floatAnimation {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px); // Reduced from -20px for subtler movement
  }
}

@keyframes pulseGlow {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.05); // Reduced from 1.1
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 0.9;
    transform: translateY(0);
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  // .hero-banner {
  //   .hero-text {
  //     .hero-status {
  //       .status-message {
  //         background: rgba(0, 0, 0, 0.3);
  //         border-color: rgba(255, 255, 255, 0.1);
  //       }
  //     }
  //   }

  // .hero-image-container {
  //   .hero-image {
  //     box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);

  //     &:hover {
  //       box-shadow: 0 30px 80px rgba(0, 0, 0, 0.5);
  //     }
  //   }
  // }
}
// }

// High contrast mode support
@media (prefers-contrast: high) {
  .hero-banner {
    .hero-text {
      .brand-text {
        background: #ffff00;
        background-clip: unset;
        -webkit-background-clip: unset;
        -webkit-text-fill-color: unset;
        color: #000;
        padding: 0.2rem 0.4rem;
        border-radius: 4px;
      }
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .hero-image-container,
  .image-glow,
  .status-message {
    animation: none;
  }

  .hero-image {
    transition: none;
  }
}
