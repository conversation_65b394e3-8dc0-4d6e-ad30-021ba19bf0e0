import { Component, Input, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from '@ava/play-comp-library';
import { LucideAngularModule } from 'lucide-angular';

export interface HeroAction {
  label: string;
  variant: 'primary' | 'secondary' | 'default';
  size: 'small' | 'medium' | 'large';
  action: () => void;
}

@Component({
  selector: 'app-hero-banner',
  standalone: true,
  imports: [CommonModule, ButtonComponent, LucideAngularModule],
  templateUrl: './hero-banner.component.html',
  styleUrl: './hero-banner.component.scss',
  encapsulation: ViewEncapsulation.None,
})
export class HeroBannerComponent {
  @Input() title: string = '';
  @Input() subtitle: string = '';
  @Input() description: string = '';
  @Input() imageSrc: string = '';
  @Input() imageAlt: string = 'Hero Image';
  @Input() actions: HeroAction[] = [];
  @Input() statusMessage: string = '';
  @Input() showStatus: boolean = false;
  @Input() brandText: string = '';
  @Input() backgroundColor: string =
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
  @Input() textColor: string = 'white';
  @Input() layout: 'default' | 'centered' | 'image-left' | 'image-right' =
    'default';

  onActionClick(action: HeroAction): void {
    action.action();
  }
}
