<div
  class="hero-container"
  [class.marketplace-hero]="heroType === 'Marketplace'"
>
  <div class="hero-content">
    <div class="greeting-section">
      <div class="greeting-header">
        <ava-icon
          iconName="Sparkles"
          iconSize="26px"
          [iconColor]="heroType === 'Marketplace' ? '#E91E63' : '#FFD700'"
          class="stars-icon"
        ></ava-icon>
        <h1 class="greeting-title">
          {{ heroContent.greeting
          }}<span class="name" *ngIf="!heroType">, {{ userName }}</span>
        </h1>
      </div>
      <p class="greeting-message">
        {{ heroContent.message }}
      </p>
    </div>
    <div class="search-bar-background">
      <div class="search-bar-container transparent-bg">
        <app-search-bar
          (searchResultsChange)="onSearchResultsChange($event)"
          (searchQueryChange)="onSearchQueryChange($event)"
          (searchLoadingChange)="onSearchLoadingChange($event)"
          (sendClicked)="onSendClicked($event)"
        >
        </app-search-bar>
      </div>
    </div>
  </div>
</div>
