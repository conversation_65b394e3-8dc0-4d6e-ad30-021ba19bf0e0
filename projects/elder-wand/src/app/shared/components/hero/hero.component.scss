.hero-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-top: 2rem;
}

.hero-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.greeting-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  margin: 0;
  margin-bottom: 24px;
}

.greeting-header {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.greeting-title {
  background: linear-gradient(90deg, #6f1c7d 0%, #f06896 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: center;
  font-family: Mulish;
  font-size: 48px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  letter-spacing: -0.912px;

  margin: 0;
}

.name {
  background: linear-gradient(90deg, #6f1c7d 0%, #f06896 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-family: Mulish;
  font-size: 48px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  letter-spacing: -0.912px;
  white-space: nowrap;
}

.greeting-message {
  width: 913px;
  color: #616874;
  text-align: center;
  font-family: Mulish;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px; /* 120% */
  margin: 0;
}

.search-bar-background {
  position: relative;
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.transparent-bg {
  width: 100%;
  background: transparent !important;
  box-shadow: none !important;
}

.stars-icon {
  height: 35px;
  width: 27px;
  margin-right: 16px;
  animation: blink 3s ease-in-out infinite;
  vertical-align: middle;
  margin-top: 18px;
}

// Marketplace specific styles
.marketplace-hero {
  .greeting-title {
    background: none !important; // Remove gradient for marketplace
    -webkit-background-clip: initial !important;
    -webkit-text-fill-color: initial !important;
    background-clip: initial !important;
    color: #000 !important; // Black color for marketplace greeting
  }
}

/* Animations */
@keyframes blink {
  0%,
  100% {
    opacity: 0;
  }

  50% {
    opacity: 0.8;
    /* Slightly reduce opacity for smoother blink */
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .greeting-title,
  .name {
    font-size: 28px;
  }
  .greeting-message {
    font-size: 16px;
  }

}
