import {
  Component,
  ViewEncapsulation,
  Input,
  OnInit,
  Output,
  EventEmitter,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import SearchBar from '../search-bar/search-bar.component';
import { GlobalStoreService } from '../../service/global-store.service';
import { IconComponent } from '@ava/play-comp-library';
import { TokenStorageService } from '@shared/index';
import { EntityResult } from '../../interfaces/agent-list.interface';
import { RevelioSearchResult } from '../../services/search.service';

interface HeroContent {
  greeting: string;
  message: string;
}

const HERO_TEXT_LOOKUP: Record<string, HeroContent> = {
  Sales: {
    greeting: 'Heya',
    message:
      "I see you have a demo with Dover CPQ in 5 minutes. I've kept your space organized and ready for you!",
  },
  'Project Team': {
    greeting: 'Welcome back',
    message:
      "It's been a busy week, hasn't it? I've organized your space with the tasks you use most often, so you don't have to keep searching!",
  },
  Marketplace: {
    greeting: 'Just for You and Your Team',
    message:
      'A curated list for you - browse or add more Agents & Flows to maximize value!',
  },
};

@Component({
  selector: 'app-hero',
  templateUrl: './hero.component.html',
  styleUrls: ['./hero.component.scss'],
  standalone: true,
  imports: [CommonModule, SearchBar, IconComponent],
  encapsulation: ViewEncapsulation.None,
})
export default class Hero implements OnInit {
  @Input() heroType?: string; // Allow parent to override hero type
  @Output() searchResultsChange = new EventEmitter<RevelioSearchResult[]>();
  @Output() searchQueryChange = new EventEmitter<string>();
  @Output() searchLoadingChange = new EventEmitter<boolean>();
  @Output() sendClicked = new EventEmitter<string>();

  selectedUser: any;
  heroContent: HeroContent = HERO_TEXT_LOOKUP['Sales'];

  constructor(
    private readonly globalStoreService: GlobalStoreService,
    private tokenStorageService: TokenStorageService,
  ) {}

  ngOnInit() {
    // If heroType is provided as input, use it directly
    if (this.heroType) {
      this.heroContent = HERO_TEXT_LOOKUP[this.heroType] || this.heroContent;
    } else {
      // Otherwise, use the user type from global store
      this.globalStoreService.selectedUser.subscribe((user) => {
        this.selectedUser = user;
        if (user && user.type) {
          this.heroContent = HERO_TEXT_LOOKUP[user.type] || this.heroContent;
        }
      });
    }
  }

  get userName(): string {
    const userName = this.tokenStorageService.getDaName() || 'User';
    return userName.split(' ')[0];
  }

  /**
   * Handle search results from search bar
   */
  onSearchResultsChange(results: RevelioSearchResult[]): void {
    this.searchResultsChange.emit(results);
  }

  /**
   * Handle search query change from search bar
   */
  onSearchQueryChange(query: string): void {
    this.searchQueryChange.emit(query);
  }

  /**
   * Handle search loading change from search bar
   */
  onSearchLoadingChange(isLoading: boolean): void {
    this.searchLoadingChange.emit(isLoading);
  }

  /**
   * Handle send clicked from search bar
   */
  onSendClicked(query: string): void {
    this.sendClicked.emit(query);
  }
}
