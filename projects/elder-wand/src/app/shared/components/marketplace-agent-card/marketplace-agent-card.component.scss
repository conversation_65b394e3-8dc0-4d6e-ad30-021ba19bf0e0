.marketplace-agent-card {
  // Card layout as specified
  display: flex;
  width: 561px;
  padding: 24px;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #e5e7eb;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: default;
  height: 100%;
  min-height: 200px;

  &.clickable {
    cursor: pointer;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    }
  }

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}

// Card Header Styles
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px; // 16px gap between header and content
  align-self: stretch;

  .card-title {
    // Header layout
    flex: 1 0 0;
    // Header styles
    color: var(--Global-colors-Neutral-n---700, #4C515B);
    font-family: Mulish;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 120%; /* 24px */
    margin: 0;
    padding:0;
  }

  .card-rating {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 0 5px;   
    flex-shrink: 0;
    background: white;
    margin-left: auto; // Push to the right end
    border-radius: 5px;

    .rating-stars {
      display: flex;
      align-items: center;
      gap: 2px;
    }

    .rating-value {
      font-family: 'Mulish', sans-serif;
      font-size: 14px;
      font-weight: 500;
      color: #374151;
    }
  }
}

// Card Description Styles
.card-description {
  flex: 1;
  margin:0;
  padding:0;

  .description-text {
    color: var(--Global-colors-Neutral-n---700, #4C515B);
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 120%; 
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    padding:0;
  }
}

// Card Footer Styles
.card-footer {
  // Footer layout
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  margin-top: auto;

  .user-count {
    display: flex;
    align-items: center;
    gap: 6px;

    .count-text {
      font-family: 'Mulish', sans-serif;
      font-size: 14px;
      font-weight: 500;
      color: #6b7280;
    }
  }

  .studio-badge {
    font-family: 'Mulish', sans-serif;
    font-size: 12px;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 12px;
    border: 1px solid;
    white-space: nowrap;
    text-transform: capitalize;
  }
}

// Variant Styles
// .card-compact {
//   padding: 20px;
//   min-height: 180px;

//   .card-header {
//     margin-bottom: 12px;

//     .card-title {
//       font-size: 16px;
//     }

//     .card-rating .rating-value {
//       font-size: 13px;
//     }
//   }

//   .card-description {
//     margin-bottom: 16px;

//     .description-text {
//       font-size: 13px;
//       -webkit-line-clamp: 2;
//     }
//   }

//   .card-footer {
//     .user-count .count-text {
//       font-size: 13px;
//     }

//     .studio-badge {
//       font-size: 11px;
//       padding: 4px 10px;
//     }
//   }
// }

// .card-featured {
//   padding: 32px;
//   min-height: 240px;

//   .card-header {
//     margin-bottom: 20px;

//     .card-title {
//       font-size: 20px;
//     }

//     .card-rating .rating-value {
//       font-size: 15px;
//     }
//   }

  // .card-description {
  //   margin-bottom: 24px;

  //   .description-text {
  //     font-size: 15px;
  //     -webkit-line-clamp: 4;
  //   }
  // }

  // .card-footer {
  //   .user-count .count-text {
  //     font-size: 15px;
  //   }

  //   .studio-badge {
  //     font-size: 13px;
  //     padding: 8px 16px;
  //   }
  // }
// }

// Responsive Design
@media (max-width: 768px) {
  .marketplace-agent-card {
    width: 400px; // Smaller width for tablets
    padding: 20px;
    min-height: 180px;

    .card-header {
      .card-title {
        font-size: 18px; // Slightly smaller but maintain hierarchy
        font-weight: 700;
        line-height: 120%;
      }
    }

    .card-description {
      .description-text {
        font-size: 14px;
        font-weight: 400;
        line-height: 120%;
        -webkit-line-clamp: 2;
      }
    }

    .card-footer {
      .user-count .count-text {
        font-size: 13px;
      }

      .studio-badge {
        font-size: 11px;
        padding: 4px 10px;
      }
    }
  }

  .card-featured {
    padding: 24px;

    .card-header .card-title {
      font-size: 18px;
    }

    .card-description .description-text {
      font-size: 14px;
    }
  }
}

@media (max-width: 480px) {
  .marketplace-agent-card {
    width: 320px; // Mobile width
    padding: 16px;
    min-height: 160px;

    .card-header {
      margin-bottom: 12px;

      .card-title {
        font-size: 16px; // Maintain readability on mobile
        font-weight: 700;
        line-height: 120%;
      }

      .card-rating .rating-value {
        font-size: 12px;
      }
    }

    .card-description {
      margin-bottom: 14px;

      .description-text {
        font-size: 13px; // Maintain readability
        font-weight: 400;
        line-height: 120%;
      }
    }

    .card-footer {
      .user-count .count-text {
        font-size: 12px;
      }

      .studio-badge {
        font-size: 10px;
        padding: 3px 8px;
      }
    }
  }
}

// Dark theme support - keep default colors and background (no overrides needed)

// Accessibility
@media (prefers-reduced-motion: reduce) {
  .marketplace-agent-card {
    transition: none;
  }

  .marketplace-agent-card.clickable:hover {
    transform: none;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .marketplace-agent-card {
    border: 2px solid #000;
    box-shadow: none;

    .card-header .card-title {
      color: #000;
    }

    .card-description .description-text {
      color: #000;
    }

    .card-footer .user-count .count-text {
      color: #000;
    }

    .studio-badge {
      border: 2px solid !important;
    }
  }
} 