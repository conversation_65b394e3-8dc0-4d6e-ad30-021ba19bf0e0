import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '@ava/play-comp-library';

export interface MarketplaceAgent {
  id?: string | number;
  title: string;
  description: string;
  rating: number;
  userCount: number;
  studioType: 'Experience Studio' | 'Platform Studio' | 'Data Studio' | 'QE Studio' | 'Product Studio';
  backgroundColor?: string;
  onClick?: () => void;
}

@Component({
  selector: 'app-marketplace-agent-card',
  standalone: true,
  imports: [CommonModule, IconComponent],
  templateUrl: './marketplace-agent-card.component.html',
  styleUrl: './marketplace-agent-card.component.scss',
})
export class MarketplaceAgentCardComponent {
  @Input() agent!: MarketplaceAgent;
  @Input() variant: 'default' | 'compact' | 'featured' = 'default';

  // Individual inputs for backward compatibility
  @Input() title: string = '';
  @Input() description: string = '';
  @Input() rating: number = 0;
  @Input() userCount: number = 0;
  @Input() studioType: MarketplaceAgent['studioType'] = 'Experience Studio';
  @Input() backgroundColor: string = '';

  get displayAgent(): MarketplaceAgent {
    // Use agent object if provided, otherwise use individual inputs
    if (this.agent) {
      return this.agent;
    }
    
    return {
      title: this.title,
      description: this.description,
      rating: this.rating,
      userCount: this.userCount,
      studioType: this.studioType,
      backgroundColor: this.backgroundColor,
    };
  }

  get studioConfig() {
    const configs = {
      'Experience Studio': {
       color: '#ECF8F4', // Cyan
        backgroundColor: '#43BD90',
        textColor: '#FFFFFF'
      },
      'Platform Studio': {
        color: '#F2EBFD', // Purple
        backgroundColor: '#997BCF',
        textColor: '#FFFFFF'
      },
      'Data Studio': {
        color: '#FFF5E2', // Yellow/Gold
        backgroundColor: '#B4A02D',
        textColor: '#FFFFFF'
      },
      'QE Studio': {
       color: '#cceaf0ff', // Cyan
        backgroundColor: '#0891b2',
        textColor: '#FFFFFF'
      },
      'Product Studio': {
        color: '#FDE9EF', // Rose/Pink
        backgroundColor: '#F06896',
        textColor: '#FFFFFF'
      }
    };

    return configs[this.displayAgent.studioType] || configs['Experience Studio'];
  }

  get cardBackgroundColor(): string {
    if (this.displayAgent.backgroundColor) {
      return this.displayAgent.backgroundColor;
    }

    const backgroundColors = {
      'Experience Studio': '#ECF8F4', // Light pink
      'Platform Studio': '#F2EBFD', // Light purple
      'Data Studio': '#FFF5E2', // Light yellow
      'QE Studio': '#cceaf0ff', // Light blue
      'Product Studio': '#FDE9EF' // Light pink
    };

    return backgroundColors[this.displayAgent.studioType] || '#ffffff';
  }
  onCardClick(): void {
    if (this.displayAgent.onClick) {
      this.displayAgent.onClick();
    }
  }

  getRatingStars(): boolean[] {
    // Show only one star as requested
    return [true];
  }
} 