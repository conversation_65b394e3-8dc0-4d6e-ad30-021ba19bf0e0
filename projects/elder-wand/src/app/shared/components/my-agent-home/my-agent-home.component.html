<div class="my-agent-home-container">
  <div class="header-section">
    <!-- Left side - User Profile -->
    <div class="user-profile-section">
      <div class="user-avatar">
        <img [src]="userAvatar" [alt]="userName" class="avatar-image" />
      </div>
      <div class="user-details">
        <h2 class="user-name">{{ userName }}</h2>
        <p class="user-email">{{ userEmail }}</p>
        <p class="user-role">{{ userRole }}</p>
      </div>
    </div>

    <!-- Right side - Action Buttons -->
    <div class="action-buttons">
        <ava-button
        class="create-agent-btn"
          label="Create Agent"
          iconName="plus"
          iconPosition="right"
          variant="primary"
          iconColor="#fff"
          width="270px"
          height="48px"
          (userClick)="onCreateAgent()"
        ></ava-button>
              <ava-button
          label="View Analytics"
          iconName="chart-spline"
          iconPosition="right"
          variant="secondary"
          iconColor="#E91E63"
          width="270px"
          height="48px"
          (userClick)="onViewAnalytics()"
        ></ava-button>
    </div>
  </div>

  <!-- Statistics Cards Section -->
  <div class="stats-section">
    <div class="row">
      <div class="col-12 col-sm-6 col-lg-3" *ngFor="let card of statCards">
        <div class="stat-card">
          <div class="stat-header">
            <div class="stat-icon-container">
              <ava-icon [iconName]="card.icon" [iconColor]="card.iconColor" iconSize="28px" class="stat-icon"></ava-icon>
            </div>
            <div class="stat-content">
              <h3 class="stat-title">{{ card.title }}</h3>
              <div class="stat-value-container">
                <ava-icon
                  *ngIf="card.title === 'Average Agents Rating'"
                  iconName="star"
                  iconColor="#FFD700"
                  iconSize="24px"
                  class="rating-star">
                </ava-icon>
                <p class="stat-value">{{ card.value }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- My Agents Section with Tabs -->
  <div class="my-agents-section">
    <h2 class="section-title">My Agents</h2>
    <app-filter-tabs
      [tabs]="agentTabs"
      [activeTab]="activeTab"
      (tabChange)="onTabChange($event)">
    </app-filter-tabs>

    <!-- Agent Cards Grid -->
    <div class="agent-cards-grid">
      <div class="row">
        <div class="col-12 col-md-6 col-lg-4" *ngFor="let agent of filteredAgentCards">
          <div class="agent-card">
            <div class="card-layout">
              <!-- Agent Icon -->
              <div class="agent-icon">
                <ava-icon iconName="code" iconSize="20px" iconColor="#fff"></ava-icon>
              </div>

              <!-- Card Content -->
              <div class="card-content">
                <!-- Card Header with Title and Status -->
                <div class="card-header">
                  <h3 class="agent-title">{{ agent.title }}</h3>
                  <div class="status-badge" [ngClass]="getStatusClass(agent.status)">
                    {{ agent.status }}
                  </div>
                </div>

                <!-- Description and Meta -->
                <p class="agent-description">{{ agent.description }}</p>
                <div class="agent-meta">
                  <span class="created-date">Created on - {{ agent.createdDate }}</span>
                </div>

                <!-- Card Actions -->
                <div class="card-actions">
              <ava-button
              label="Edit"
                iconName="pencil"
                iconPosition="right"
                height="40px"
                width="218px"
                variant="secondary"
                iconColor="#E91E63"
                (click)="onEditAgent(agent)">
                
              </ava-button>
              <ava-button
                label="View"
                iconName="eye"
                iconPosition="right"
                height="40px"
                width="218px"
                variant="secondary"
                iconColor="#E91E63"
                (click)="onViewAgent(agent)">                
              </ava-button>
              <ava-button
                iconName="trash-2"
                iconPosition="only"
                height="40px"
                width="40px"
                variant="secondary"
                iconColor="#E91E63"
                (click)="onDeleteAgent(agent)">
              </ava-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>