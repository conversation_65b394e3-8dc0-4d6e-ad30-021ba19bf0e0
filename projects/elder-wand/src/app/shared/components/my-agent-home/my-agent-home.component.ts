import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { GlobalStoreService } from '../../service/global-store.service';
import { IconComponent, ButtonComponent } from "@ava/play-comp-library";
import { FilterTabsComponent, FilterTab } from '../filter-tabs/filter-tabs.component';

interface User {
  name: string;
  role: string;
  type: string;
}

interface StatCard {
  icon: string;
  title: string;
  value: string;
  iconColor: string;
}

interface AgentCard {
  id: number;
  title: string;
  description: string;
  status: 'Approved' | 'Pending' | 'Denied' | 'Draft';
  createdDate: string;
}

@Component({
  selector: 'app-my-agent-home',
  imports: [CommonModule, IconComponent, FilterTabsComponent, ButtonComponent],
  templateUrl: './my-agent-home.component.html',
  styleUrl: './my-agent-home.component.scss',
  encapsulation: ViewEncapsulation.None
})
export class MyAgentHomeComponent implements OnInit {
  userName: string = '';
  userEmail: string = '';
  userRole: string = 'Lead Backend Engineer';
  userAvatar: string = 'assets/icons/user-avatar.svg';
  selectedUser: User | null = null;

  // Statistics cards data
  statCards: StatCard[] = [
    {
      icon: 'bot',
      title: 'Total Agents created',
      value: '32',
      iconColor: '#000'
    },
    {
      icon: 'circle-check',
      title: 'Approved Agents',
      value: '126',
      iconColor: '#000'
    },
    {
      icon: 'star',
      title: 'Average Agents Rating',
      value: '4.8',
      iconColor: '#000'
    },
    {
      icon: 'chart-spline',
      title: 'Average Accuracy',
      value: '97%',
      iconColor: '#000'
    }
  ];

  // Tabs data for My Agents section
  activeTab = 'all';
  agentTabs: FilterTab[] = [
    { id: 'all', label: 'All', priority: 100 },
    { id: 'approved', label: 'Approved (126)', icon: 'circle-check', iconColor: '#059669', priority: 90 },
    { id: 'pending', label: 'Pending (10)', icon: 'clock', iconColor: '#D97706', priority: 80 },
    { id: 'denied', label: 'Denied (4)', icon: 'x', iconColor: '#DC2626', priority: 70 },
    { id: 'draft', label: 'Draft (1)', icon: 'pencil', priority: 60 }
  ];

  // Agent cards data
  allAgentCards: AgentCard[] = [
    {
      id: 1,
      title: 'Java to Ruby Migration',
      description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',
      status: 'Approved',
      createdDate: '12/06/2025'
    },
    {
      id: 2,
      title: 'Java to Ruby Migration',
      description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',
      status: 'Pending',
      createdDate: '10/06/2025'
    },
    {
      id: 3,
      title: 'Java to Ruby Migration',
      description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',
      status: 'Approved',
      createdDate: '11/06/2025'
    },
    {
      id: 4,
      title: 'Java to Ruby Migration',
      description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',
      status: 'Approved',
      createdDate: '08/06/2025'
    },
    {
      id: 5,
      title: 'Java to Ruby Migration',
      description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',
      status: 'Approved',
      createdDate: '05/06/2025'
    },
    {
      id: 6,
      title: 'Java to Ruby Migration',
      description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',
      status: 'Pending',
      createdDate: '01/06/2025'
    }
  ];

  // Filtered agent cards based on active tab
  get filteredAgentCards(): AgentCard[] {
    if (this.activeTab === 'all') {
      return this.allAgentCards;
    }

    const statusMap: { [key: string]: string } = {
      'approved': 'Approved',
      'pending': 'Pending',
      'denied': 'Denied',
      'draft': 'Draft'
    };

    const targetStatus = statusMap[this.activeTab];
    return this.allAgentCards.filter(card => card.status === targetStatus);
  }

  constructor(
    private tokenStorageService: TokenStorageService,
    private globalStoreService: GlobalStoreService
  ) {}

  ngOnInit(): void {
    this.loadUserData();
    this.loadSelectedUser();
  }

  private loadUserData(): void {
    this.userName = this.tokenStorageService.getDaName() || 'Akash Kumar';
    this.userEmail = this.tokenStorageService.getDaUsername() || '<EMAIL>';
  }

  private loadSelectedUser(): void {
    this.globalStoreService.selectedUser.subscribe((user) => {
      this.selectedUser = user;
      if (user && user.role) {
        this.userRole = user.role;
      }
    });
  }

  onCreateAgent(): void {
    console.log('Create Agent clicked');
    // TODO: Implement navigation to agent creation interface
  }

  onViewAnalytics(): void {
    console.log('View Analytics clicked');
    // TODO: Implement navigation to analytics page
  }

  onTabChange(tabId: string): void {
    this.activeTab = tabId;
  }

  onEditAgent(agent: AgentCard): void {
    console.log('Edit agent:', agent);
    // TODO: Implement agent editing functionality
  }

  onViewAgent(agent: AgentCard): void {
    console.log('View agent:', agent);
    // TODO: Implement agent viewing functionality
  }

  onDeleteAgent(agent: AgentCard): void {
    console.log('Delete agent:', agent);
    // TODO: Implement agent deletion functionality
  }

  getStatusClass(status: string): string {
    const statusClasses: { [key: string]: string } = {
      'Approved': 'status-approved',
      'Pending': 'status-pending',
      'Denied': 'status-denied',
      'Draft': 'status-draft'
    };
    return statusClasses[status] || 'status-default';
  }
}
