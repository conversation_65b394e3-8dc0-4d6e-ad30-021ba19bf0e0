<awe-header theme="light" class="custom-header-container">
  <div left-content class="header-left-logo">
    <p class="logo-text cursor-pointer" (click)="onLogoClick()">
      Ascendion AAVA
    </p>
    <div class="ascendion-dropdown"></div>
  </div>
  <div right-content class="header-right-content">
    <!-- Show marketplace and Ascendion buttons only on launchpad route -->
    <ava-button
      *ngIf="isLaunchpadRoute"
      class="button"
      label="Go to Marketplace"
      variant="primary"
      width="auto"
      height="40px"
      (userClick)="navigate()"
      [pill]="true"
    ></ava-button>
    <div class="ascendion-container" *ngIf="isLaunchpadRoute">
      <ava-button
        [label]="selectedAscendionOption"
        variant="secondary"
        iconName="chevron-down"
        iconPosition="right"
        [pill]="true"
        iconColor="#E91E63"
        width="auto"
        height="40px"
        (userClick)="toggleAscendionDropdown()"
      ></ava-button>

      <!-- Ascendion Dropdown -->
      <div class="ascendion-dropdown" [class.visible]="ascendionDropdownOpen">
        <div class="ascendion-dropdown-content">
          <div class="ascendion-actions">
            <div
              class="ascendion-action-item"
              (click)="selectAscendionOption('EE')"
            >
              <span class="action-label">Experience Engineering</span>
            </div>
            <div
              class="ascendion-action-item"
              (click)="selectAscendionOption('PE')"
            >
              <span class="action-label">Product Engineering</span>
            </div>
            <div
              class="ascendion-action-item"
              (click)="selectAscendionOption('QE')"
            >
              <span class="action-label">Quality Engineering</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ava-icon
      iconName="languages"
      iconColor="#666D99"
      iconSize="24px"
      class="nav-icon"
    ></ava-icon>
    <ava-icon
      iconName="sun"
      iconColor="#666D99"
      iconSize="24px"
      class="nav-icon"
    ></ava-icon>
    <div class="profile-container header-right-content">
      <div
        class="cursor-pointer d-flex justify-content-center align-items-center"
        [class.active]="profileDropdownOpen"
        (click)="toggleProfileDropdown()"
      >
        <img
          src="assets/icons/user-avatar.svg"
          class="custom-avatar"
          alt="User avatar"
        />
      </div>

      <!-- Profile Dropdown -->
      <div class="profile-dropdown" [class.visible]="profileDropdownOpen">
        <div class="profile-dropdown-content">
          <div class="profile-info">
            <div class="profile-name">{{ userName }}</div>
            <div class="profile-email" *ngIf="userEmail">{{ userEmail }}</div>
          </div>
          <div class="profile-divider"></div>
          <div class="profile-actions">
            <div class="profile-action-item" (click)="onLogout()">
              <span class="action-label">Logout</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</awe-header>
