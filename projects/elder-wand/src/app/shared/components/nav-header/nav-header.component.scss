.outer-box.light {
  background-color: transparent !important;
  box-shadow: none !important;
}

.container {
  background-color: transparent !important;
}

.custom-header-container {
  width: 100%;
  box-sizing: border-box;
}

.header-left-logo {
  display: flex;
  align-items: center;
  gap: 24px;
  width: auto;
  height: 36px;
  flex: 1;

  .logo-text {
    color: #000;
    font-family: Mulish;
    font-size: 32px;
    font-weight: 700;
    margin: 0;
  }
}

.header-right-content {
  display: flex;
  align-items: center;
  gap: 32px;
}

.button {
  .ava-button .button-label {
    color: #fff;
  }
}

.custom-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(20, 70, 146, 0.08);
}

// Only apply size constraints to nav icons, not buttons
.nav-icon {
  width: 40px !important;
  height: 40px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box;
}

// Profile dropdown container
.profile-container {
  position: relative;
  display: flex;
  align-items: center;

  // Profile icon wrapper with nav-item-like styling
  .cursor-pointer {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
      background-color: var(--nav-hover);
      transform: scale(1.05);

      &:after {
        opacity: 0.1;
      }
    }

    // When profile dropdown is open, apply selected state styling
    &.active {
      background-color: var(--nav-item-active-bg, #e6e4fb);
      box-shadow: 0 2px 4px var(--card-shadow, rgba(0, 0, 0, 0.1));

      &:after {
        opacity: 0.1;
      }
    }

    // Add the gradient overlay effect
    &:after {
      content: "";
      position: absolute;
      inset: 0;
      background: var(--gradient-primary);
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: -1;
    }

    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }
  }
}

// Ascendion dropdown container
.ascendion-container {
  position: relative;
  display: flex;
  align-items: center;
}

// Profile dropdown styles (matching navbar dropdown)
.profile-dropdown {
  position: absolute;
  top: 110px;
  right: 50px;
  min-width: 200px;
  background-color: var(--dropdown-bg);
  border-radius: 8px;
  box-shadow: 0 4px 16px var(--dropdown-shadow);
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  z-index: 15; // Reduced from 1000 to prevent hiding content below
  margin-top: 8px;
  padding: 8px;

  &.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

.profile-dropdown-content {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.profile-info {
  padding: 12px;
  border-radius: 6px;
}

.profile-name {
  font-weight: 500;
  font-size: 16px;
  color: var(--dropdown-text);
  margin-bottom: 4px;
}

.profile-email {
  font-size: 14px;
  color: var(--text-secondary);
}

.profile-divider {
  height: 1px;
  background-color: var(--border-color, #e5e7eb);
  margin: 8px 0;
}

.profile-actions {
  display: flex;
  flex-direction: column;
}

.profile-action-item {
  color: var(--nav-text, #666d99);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    background-color: var(--nav-hover);
    color: var(--nav-item-active-color);

    &:after {
      opacity: 0.1;
    }
  }

  // Add the gradient overlay effect
  &:after {
    content: "";
    position: absolute;
    inset: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
  }

  .action-label {
    font-weight: 500;
    font-size: 1rem;
    color: var(--dropdown-text);
  }
}

// Ascendion dropdown styles (matching profile dropdown)
.ascendion-dropdown {
  position: absolute;
  top: 48px;
  right: 0;
  min-width: 160px;
  background-color: var(--dropdown-bg);
  border-radius: 8px;
  box-shadow: 0 4px 16px var(--dropdown-shadow);
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  z-index: 15;
  padding: 8px;

  &.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

.ascendion-dropdown-content {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.ascendion-actions {
  display: flex;
  flex-direction: column;
}

.ascendion-action-item {
  color: var(--nav-text, #666d99);
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    background-color: var(--nav-hover);
    color: var(--nav-item-active-color);

    &:after {
      opacity: 0.1;
    }
  }

  // Add the gradient overlay effect
  &:after {
    content: "";
    position: absolute;
    inset: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
  }

  .action-label {
    font-weight: 500;
    font-size: 1rem;
    color: var(--dropdown-text);
    text-align: left;
    width: 100%;
  }
}
