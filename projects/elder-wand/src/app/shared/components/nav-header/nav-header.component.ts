import {
  Component,
  OnInit,
  HostListener,
  Output,
  EventEmitter,
  Input,
  ViewEncapsulation,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from '@awe/play-comp-library';

import { IconComponent, ButtonComponent } from '@ava/play-comp-library';
import { AuthService, TokenStorageService } from '@shared/index';
import { Router } from '@angular/router';
import { Location } from '@angular/common';

interface User {
  name: string;
  role: string;
  type: string;
  projects?: string[];
}

@Component({
  selector: 'app-nav-header',
  standalone: true,
  imports: [CommonModule, HeaderComponent, IconComponent, ButtonComponent],
  templateUrl: './nav-header.component.html',
  styleUrl: './nav-header.component.scss',
  encapsulation: ViewEncapsulation.None,
})
export class NavHeaderComponent implements OnInit {
  @Input() isLaunchpadRoute: boolean = false;
  public redirectUrl = '';
  profileDropdownOpen: boolean = false;
  ascendionDropdownOpen: boolean = false;
  selectedAscendionOption: string = 'Experience Engineering'; // Default label

  userName: string = '';
  userEmail: string = '';

  @Output() userSelected = new EventEmitter<User>();
  @Output() ascendionOptionSelected = new EventEmitter<string>();

  users: User[] = [
    {
      name: 'Akshay',
      role: 'Project Team',
      type: 'Project Team',
    },
  ];
  constructor(
    private authService: AuthService,
    private tokenStorageService: TokenStorageService,
    private router: Router,
    private location: Location,
  ) {}

  ngOnInit(): void {
    const authConfig = this.authService.getAuthConfig();
    this.redirectUrl = authConfig?.redirectUrl || window.location.origin;
    this.loadUserData();
    this.selectUser(this.users[0]);
  }

  // Handle logout
  onLogout() {
    if (this.tokenStorageService.getLoginType() === 'basic') {
      this.authService.basicLogout().subscribe({
        next: () => {
          // Use Angular router instead of window.location
          this.router.navigate(['/login']);
          this.tokenStorageService.deleteCookie('org_path');
        },
        error: (error) => {
          console.error('Basic logout failed:', error);
          // Still try to navigate to login even if logout fails
          this.router.navigate(['/login']);
        },
      });
    } else {
      this.authService.logout(this.redirectUrl).subscribe({
        next: () => {
          this.tokenStorageService.deleteCookie('org_path');
        },
        error: (error) => {
          console.error('SSO logout failed:', error);
        },
      });
    }
  }
  onLogin(): void {
    try {
      // this.logger.info('Triggering login...');
      const currentUrl = window.location.origin;
      this.authService.loginSSO(currentUrl).subscribe({
        // next: () => this.logger.debug('Login successful'),
        // error: (error) => this.logger.error('Login failed:', error),
      });
    } catch (error) {
      // this.logger.error('Error during login:', error);
      // this.toastService.error('Login failed');
    }
  }

  // Load user data from cookies
  loadUserData(): void {
    this.userName = this.tokenStorageService.getDaName() || 'User';
    this.userEmail =
      this.tokenStorageService.getDaUsername() || '<EMAIL>';
  }

  getInitials(name: string): string {
    if (!name) return '';
    const parts = name.split(' ');
    if (parts.length === 1) return parts[0].charAt(0).toUpperCase();
    return `${parts[0].charAt(0)}${parts[parts.length - 1].charAt(0)}`.toUpperCase();
  }

  // Toggle profile dropdown
  toggleProfileDropdown(): void {
    this.profileDropdownOpen = !this.profileDropdownOpen;
    // Close nav dropdowns when profile dropdown opens
    // if (this.profileDropdownOpen) {
    //   this.closeAllDropdowns();
    // }
  }

  // Toggle Ascendion dropdown
  toggleAscendionDropdown(): void {
    this.ascendionDropdownOpen = !this.ascendionDropdownOpen;
  }

  selectUser(user: User): void {
    this.userSelected.emit(user);
  }

  selectAscendionOption(option: string): void {
    const selectedLabel =
      option === 'EE'
        ? 'Experience Engineering'
        : option === 'PE'
          ? 'Product Engineering'
          : option === 'QE'
            ? 'Quality Engineering'
            : option;

    // Update the button label
    this.selectedAscendionOption = selectedLabel;

    console.log('Selected label:', selectedLabel);

    // Emit the selected option to parent components
    this.ascendionOptionSelected.emit(option);

    this.ascendionDropdownOpen = false; // Close dropdown after selection
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    const clickedInsideProfile =
      !!target.closest('.profile-container') ||
      !!target.closest('.profile-dropdown');
    const clickedInsideAscendion =
      !!target.closest('.ascendion-container') ||
      !!target.closest('.ascendion-dropdown');

    if (!clickedInsideProfile) {
      this.profileDropdownOpen = false;
    }

    if (!clickedInsideAscendion) {
      this.ascendionDropdownOpen = false;
    }
  }

  navigate() {
    this.router.navigateByUrl('/agent-list');
  }

  // Navigate back to previously opened page
  onLogoClick(): void {
    this.location.back();
  }
}
