<section class="news-blogs">
  <div class="container">
    <div class="header">
      <h2>
        What's <span class="gradient-text">New?</span>
      </h2>
    </div>
    <div class="blog-grid">
      <article class="blog-card" *ngFor="let post of blogPosts">
        <div class="blog-image">
          <img [src]="post.image" [alt]="post.title" />
        </div>
        <div class="blog-content">
          <div class="blog-main-content">
            <h3>{{ post.title }}</h3>
            <p class="description">
              {{ post.description }}
            </p>
          </div>
          <div class="blog-bottom-section">
            <a href="#" class="read-more" (click)="readMore(post.id); $event.preventDefault()">Read More</a>
            <hr class="divider">
            <div class="blog-footer">
              <div class="author">
                <img [src]="post.author.avatar" [alt]="post.author.name" class="author-avatar" />
                <span class="author-name">{{ post.author.name }}</span>
              </div>
              <div class="views">
                <ava-icon iconName="eye" iconColor="#666D99" iconSize="14"></ava-icon>
                <span>{{ post.views }}</span>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</section>