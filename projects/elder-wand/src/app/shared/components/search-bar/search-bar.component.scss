.search-container {
  display: flex;
  width: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 10px;
  border-radius: 16px;
  box-sizing: border-box;
  margin: 0 auto;
  position: relative;
  z-index: 2;
  overflow: visible;
}

.search-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  z-index: 2;
}

.search-icon {
  position: absolute;
  left: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.search-svg-icon {
  width: 24px;
  height: 24px;
  display: block;
}

.search-input {
  width: 100%;
  padding: 16px 60px 16px 48px;
  border-radius: 16px;
  background-color: #fff;
  color: #666D99;
  font-family: Mulish;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  outline: none;
  border: double 1px transparent;
  background-image: linear-gradient(white, white),
    linear-gradient(180deg, var(--Brand-Primary-300, #F06896) 0%, var(--Brand-Tertiary-300, #997BCF) 100%);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  transition: all 0.3s ease;

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }

  &:focus {
    box-shadow: 0 0 0 3px rgba(240, 104, 150, 0.1);
  }
}

.animated-placeholder-wrapper {
  position: absolute;
  left: 48px;
  top: 50%;
  transform: translateY(-50%);
  overflow: hidden;
  height: 24px;
  display: flex;
  align-items: center;
  pointer-events: none;
  width: calc(100% - 48px - 16px);
}

.animated-placeholder {
  display: flex;
  flex-direction: column;
  height: 100%;
  animation: slide-up-down 10s ease-in-out infinite;
  line-height: 1.2;
}

.animated-placeholder span {
  color: #666D99;
  font-family: 'Mulish', -apple-system, 'Roboto', 'Helvetica', sans-serif;
  font-size: 20px;
  font-weight: 400;
  white-space: nowrap;
  height: 100%;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  flex-grow: 0;
}

.loading-indicator {
  position: absolute;
  right: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;

  .spinning {
    animation: spin 1s linear infinite;
  }
}

.clear-button {
  position: absolute;
  right: 80px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: all 0.3s ease;
  z-index: 3;

  &:hover {
    background-color: rgba(102, 109, 153, 0.1);
  }

  &:active {
    transform: scale(0.95);
  }
}

.send-button {
  position: absolute;
  right: 16px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: all 0.3s ease;
  z-index: 3;

  &:hover:not(:disabled) {
    background-color: rgba(0, 0, 0, 0.1);
  }

  &:active:not(:disabled) {
    transform: scale(0.95);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

@keyframes slide-up-down {
  0%, 45% {
    transform: translateY(0);
  }
  50%, 95% {
    transform: translateY(-24px);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Media queries */
@media (max-width: 1200px) {
  .search-container {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .search-wrapper {
    max-width: 100%;
  }

  .search-container {
    height: auto;
  }

  .search-input {
    font-size: 18px;
  }

  .animated-placeholder span {
    font-size: 18px;
  }

  .animated-placeholder-wrapper {
    height: 22px;
  }
}
