import { Component, Output, EventEmitter, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconComponent } from '@ava/play-comp-library';
import {
  SearchService,
  RevelioSearchResponse,
  RevelioSearchResult,
} from '../../services/search.service';
import { EntityResult } from '../../interfaces/agent-list.interface';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-search-bar',
  templateUrl: './search-bar.component.html',
  styleUrls: ['./search-bar.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, IconComponent],
})
export default class SearchBar implements OnDestroy {
  placeholderTexts: string[] = [
    'What you want to do today',
    'How can I make your day productive?',
  ];
  searchValue: string = '';
  searchResults: RevelioSearchResult[] = [];
  isSearching: boolean = false;

  @Output() sendClicked = new EventEmitter<string>();
  @Output() searchResultsChange = new EventEmitter<RevelioSearchResult[]>();
  @Output() searchQueryChange = new EventEmitter<string>();
  @Output() searchLoadingChange = new EventEmitter<boolean>();

  private destroy$ = new Subject<void>();

  constructor(private searchService: SearchService) {}

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onEnterPressed() {
    if (this.searchValue.trim()) {
      this.searchQueryChange.emit(this.searchValue.trim());
      this.performSearch(this.searchValue.trim());
      this.sendClicked.emit(this.searchValue.trim());
    }
  }

  onSend() {
    if (this.searchValue.trim()) {
      this.searchQueryChange.emit(this.searchValue.trim());
      this.performSearch(this.searchValue.trim());
      this.sendClicked.emit(this.searchValue.trim());
    }
  }

  /**
   * Perform search using the unified search API
   */
  // private performSearch(query: string) {
  //   if (!query.trim()) {
  //     this.searchResults = [];
  //     this.searchResultsChange.emit([]);
  //     return;
  //   }
  //   this.searchService.searchAgents(query, 10).subscribe({
  //     next: (results: EntityResult[]) => {
  //       this.searchResults = results;
  //       this.searchResultsChange.emit(results);
  //     },
  //     error: (error: any) => {
  //       this.searchResults = [];
  //       this.searchResultsChange.emit([]);
  //     }
  //   });
  // }

  /**
   * Perform search using the Revelio search API
   */
  private performSearch(query: string) {
    if (!query.trim()) {
      this.searchResults = [];
      this.searchResultsChange.emit([]);
      this.isSearching = false;
      this.searchLoadingChange.emit(false);
      return;
    }

    this.isSearching = true;
    this.searchLoadingChange.emit(true);

    this.searchService
      .revelioSearch({
        query: query,
        limit: 10,
        threshold: 0,
      })
      .subscribe({
        next: (response: RevelioSearchResponse) => {
          // Extract results from the Revelio response
          const results: RevelioSearchResult[] = response.results || [];
          console.log('Revelio search results:', results);
          this.searchResults = results;
          this.searchResultsChange.emit(results);
          this.isSearching = false;
          this.searchLoadingChange.emit(false);
        },
        error: (error: any) => {
          console.error('Revelio search error:', error);
          this.searchResults = [];
          this.searchResultsChange.emit([]);
          this.isSearching = false;
          this.searchLoadingChange.emit(false);
        },
      });
  }

  /**
   * Clear search
   */
  clearSearch() {
    this.searchValue = '';
    this.searchResults = [];
    this.searchResultsChange.emit([]);
    this.searchQueryChange.emit('');
    this.isSearching = false;
    this.searchLoadingChange.emit(false);
  }
}
