import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { IconComponent } from '@ava/play-comp-library';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, IconComponent],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss'
})
export class SidebarComponent {

  constructor(private router: Router) {}
  
  sidebarItems = [
    {
      iconName: 'bot',
      label: 'Agents',
      isActive: false,
      route: '/build/agents/individual'
    },
    {
      iconName: 'wrench',
      label: 'Tools',
      isActive: false,
      route: '/tools'
    },
    {
      iconName: 'workflow',
      label: 'Workflows',
      isActive: false,
      route: '/build/workflows'
    },
    {
      iconName: 'shield-check',
      label: 'Team',
      isActive: false,
      route: '/team'
    },
    {
      iconName: 'book-text',
      label: 'Documents',
      isActive: false,
      route: '/documents'
    }
  ];

  onItemClick(item: any) {
    // Reset all items to inactive
    this.sidebarItems.forEach(sidebarItem => sidebarItem.isActive = false);
    // Set clicked item as active
    item.isActive = true;

    // Add navigation logic here
    console.log(`Navigating to: ${item.label} - Route: ${item.route}`);

    // Navigate to the selected route
    this.router.navigate([item.route]);
  }

  onAccountClick() {
    console.log('Account icon clicked - navigating to my-agent-home');
    this.router.navigate(['/my-agent-home']);
  }

  onLogoClick() {
    console.log('Logo clicked - navigate to home');
  }
}
