<div class="studios-container">
  <div class="studios-grid">
    <div
      class="studio-card"
      *ngFor="let studio of displayedStudios"
      [routerLink]="studio.link"
    >
      <div class="card-content">
        <div class="card-header">
          <h2>{{ studio.title }}</h2>
          <p class="description">{{ studio.description }}</p>
        </div>

        <div class="card-body">
          <div class="card-visual">
            <img [src]="studio.image" [alt]="studio.title" />
          </div>

          <div class="card-footer">
            <div class="arrow-button">
              <ava-icon
                iconName="ArrowUpRight"
                iconSize="20px"
                iconColor="#A0A0A0"
              ></ava-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
