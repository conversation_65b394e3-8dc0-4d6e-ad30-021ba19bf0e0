.studios-container {
  padding: 0px 16px;



  .studios-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
    margin: 0;

    .studio-card {
      background: white;
      border-radius: 20px;
      overflow: hidden;
      cursor: pointer;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      // box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      // border: 1px solid rgba(0, 0, 0, 0.05);
      height: auto;

      .card-content {
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 24px 24px 0 24px;
        position: relative;

        .card-header {
          margin-bottom: 24px;

          h2 {
            color: #1D1D1D;
            font-family: Mulish;
            font-size: 28px;
            font-weight: 700;
            margin: 0 0 12px 0;
          }

          .description {
            color: #6B7280;
            font-family: Mulish;
            font-size: 16px;
            font-weight: 500;
            text-align: left;
            margin: 0;
          }
        }

        .card-body {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 20px;
        }

        .card-visual {
          display: flex;
          align-items: center;
          justify-content: flex-start;

          img {
            width: 223px;
            height: 195px;
            object-fit: contain;
          }
        }

        .card-footer {
          display: flex;
          align-items: baseline;
          justify-content: center;

          .arrow-button {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: baseline !important;
            justify-content: center;
            border: 1px solid #E5E7EB;
            background: #fff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .studios-grid {
    grid-template-columns: 1fr !important;
  }
}