# Testimonial Card Component

A reusable, customizable testimonial card component for displaying user testimonials, reviews, and feedback.

## Features

- 🎨 **Multiple Variants**: Default, compact, and detailed layouts
- 📱 **Responsive Design**: Mobile-first approach with breakpoints
- ⭐ **Star Ratings**: Optional 5-star rating display
- 🌙 **Dark Theme Support**: Automatic dark mode adaptation
- ♿ **Accessibility**: Screen reader friendly with proper ARIA labels
- 🎭 **Customizable**: Extensive styling options via inputs

## Basic Usage

### Using Testimonial Object (Recommended)

```typescript
import { TestimonialCardComponent, Testimonial } from '@shared/components/testimonial-card/testimonial-card.component';

export class MyComponent {
  testimonial: Testimonial = {
    id: 1,
    avatar: 'assets/images/user-avatar.jpg',
    quote: 'Must have book for all, who want to be Product Designer or Interaction Designer.',
    authorName: '<PERSON>',
    authorRole: 'Designer',
    rating: 5,
    company: 'Tech Corp'
  };
}
```

```html
<app-testimonial-card [testimonial]="testimonial"></app-testimonial-card>
```

### Using Individual Inputs

```html
<app-testimonial-card
  avatar="assets/images/user-avatar.jpg"
  quote="Amazing product! Highly recommended."
  authorName="John Doe"
  authorRole="Senior Developer"
  [rating]="5"
  company="InnovateTech">
</app-testimonial-card>
```

## Component Inputs

### Core Properties

| Input | Type | Default | Description |
|-------|------|---------|-------------|
| `testimonial` | `Testimonial` | - | Complete testimonial object (recommended) |
| `avatar` | `string` | `''` | User avatar image URL |
| `quote` | `string` | `''` | Testimonial text/quote |
| `authorName` | `string` | `''` | Author's full name |
| `authorRole` | `string` | `''` | Author's job title/role |
| `rating` | `number` | `0` | Star rating (1-5) |
| `company` | `string` | `''` | Author's company name |

### Customization Options

| Input | Type | Default | Description |
|-------|------|---------|-------------|
| `variant` | `'default' \| 'compact' \| 'detailed'` | `'default'` | Card size variant |
| `showQuotes` | `boolean` | `true` | Show decorative quote marks |
| `backgroundColor` | `string` | `'#ffffff'` | Card background color |
| `textColor` | `string` | `'#333333'` | Primary text color |
| `borderRadius` | `string` | `'16px'` | Card border radius |
| `padding` | `string` | `'32px'` | Internal padding |
| `shadow` | `string` | `'0 4px 20px rgba(0, 0, 0, 0.08)'` | Box shadow |
| `maxWidth` | `string` | `'400px'` | Maximum card width |

## Testimonial Interface

```typescript
interface Testimonial {
  id?: string | number;
  avatar: string;
  quote: string;
  authorName: string;
  authorRole: string;
  rating?: number;
  company?: string;
}
```

## Variants

### Default
Standard size with balanced proportions.

```html
<app-testimonial-card [testimonial]="testimonial" variant="default"></app-testimonial-card>
```

### Compact
Smaller size for tight layouts or mobile views.

```html
<app-testimonial-card [testimonial]="testimonial" variant="compact"></app-testimonial-card>
```

### Detailed
Larger size with more prominent elements.

```html
<app-testimonial-card [testimonial]="testimonial" variant="detailed"></app-testimonial-card>
```

## Custom Styling

```html
<app-testimonial-card
  [testimonial]="testimonial"
  backgroundColor="#f8f9fa"
  textColor="#2d3748"
  borderRadius="12px"
  padding="24px"
  shadow="0 8px 32px rgba(0, 0, 0, 0.12)"
  maxWidth="500px">
</app-testimonial-card>
```

## Grid Layout Example

```html
<div class="testimonials-grid">
  <app-testimonial-card
    *ngFor="let testimonial of testimonials; trackBy: trackByTestimonial"
    [testimonial]="testimonial">
  </app-testimonial-card>
</div>
```

```scss
.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}
```

## Accessibility Features

- Semantic HTML structure
- Alt text for avatar images
- Proper heading hierarchy
- High contrast mode support
- Reduced motion respect
- Screen reader friendly

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Dependencies

- Angular 17+
- Angular Common Module

## Related Components

- `TestimonialsSectionComponent` - Section wrapper with header
- `HeroBannerComponent` - For hero sections with testimonials 