<div class="testimonial-card" [ngStyle]="cardStyles" [ngClass]="'testimonial-' + variant">
  <!-- Header with Ava<PERSON> on right -->
  <div class="testimonial-header">
    <!-- Avatar -->
    <div class="testimonial-avatar">
      <img
        [src]="displayTestimonial.avatar"
        [alt]="displayTestimonial.authorName + ' avatar'"
        class="avatar-image"
        loading="lazy"
      />
    </div>
  </div>

  <!-- Quote Text -->
  <div class="testimonial-quote">
    <span *ngIf="showQuotes" class="quote-mark opening">"</span>
    <p class="quote-text">{{ displayTestimonial.quote }}</p>
    <span *ngIf="showQuotes" class="quote-mark closing">"</span>
  </div>

  <!-- Footer with Author Information -->
  <div class="testimonial-footer">
    <div class="author-info">
      <h4 class="author-name">{{ displayTestimonial.authorName }}</h4>
      <p class="author-role">
        {{ displayTestimonial.authorRole }}
        <span *ngIf="displayTestimonial.company" class="company">
          at {{ displayTestimonial.company }}
        </span>
      </p>
    </div>
  </div>
</div>