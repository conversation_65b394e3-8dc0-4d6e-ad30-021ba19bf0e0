import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface Testimonial {
  id?: string | number;
  avatar: string;
  quote: string;
  authorName: string;
  authorRole: string;
  rating?: number;
  company?: string;
}

@Component({
  selector: 'app-testimonial-card',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './testimonial-card.component.html',
  styleUrl: './testimonial-card.component.scss',
})
export class TestimonialCardComponent {
  @Input() testimonial!: Testimonial;
  @Input() showQuotes: boolean = true;
  @Input() variant: 'default' | 'compact' | 'detailed' = 'default';
  @Input() backgroundColor: string = '#ffffff';
  @Input() textColor: string = '#333333';
  @Input() borderRadius: string = '16px';
  @Input() padding: string = '32px';
  @Input() shadow: string = '0 4px 20px rgba(0, 0, 0, 0.08)';
  @Input() maxWidth: string = '400px';

  // Individual inputs for backward compatibility
  @Input() avatar: string = '';
  @Input() quote: string = '';
  @Input() authorName: string = '';
  @Input() authorRole: string = '';
  @Input() rating: number = 0;
  @Input() company: string = '';

  get displayTestimonial(): Testimonial {
    // Use testimonial object if provided, otherwise use individual inputs
    if (this.testimonial) {
      return this.testimonial;
    }
    
    return {
      avatar: this.avatar,
      quote: this.quote,
      authorName: this.authorName,
      authorRole: this.authorRole,
      rating: this.rating,
      company: this.company,
    };
  }

  get cardStyles() {
    return {
      'background-color': this.backgroundColor,
      'color': this.textColor,
      'border-radius': this.borderRadius,
      'padding': this.padding,
      'box-shadow': this.shadow,
      'max-width': this.maxWidth,
    };
  }
} 