.testimonials-section {
  padding: 40px 0;
  background: #ffffff;

  .container {
    width: 100%;
    // max-width: 1200px;
    // margin: 0 auto;
    padding: 0 32px;
  }

  .section-header {
    text-align: center;
    margin-bottom: 40px;

    .section-title {
      font-family: 'Mulish', sans-serif;
      font-size: 48px;
      font-weight: 700;
      color: var(--Global-colors-Neutral-n---800, #3B3F46);
      margin: 0;
      line-height: normal;
      letter-spacing: -0.912px;

      .highlight {
        background: linear-gradient(90deg, #6566CD 36.04%, #F96CAB 110.55%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-family: Mulish;
        font-size: 48px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        letter-spacing: -0.912px;
      }

      @media (max-width: 768px) {
        font-size: 36px;

        .highlight {
          font-size: 36px;
        }
      }

      @media (max-width: 480px) {
        font-size: 28px;

        .highlight {
          font-size: 28px;
        }
      }
    }

    .section-subtitle {
      color: #161C2D;
      font-family: Mulish;
      font-size: 24px;
      font-style: normal;
      font-weight: 500;
      line-height: 32px; /* 133.333% */
      letter-spacing: -0.2px;
      margin: 0;
      max-width: 600px;
      margin: 0 auto;

      @media (max-width: 768px) {
        font-size: 20px;
        line-height: 28px;
      }
    }
  }

  .testimonials-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    align-items: stretch;
    width: 100%;
    margin: 0 auto;

    @media (max-width: 1024px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }
}

// Override testimonial card styles for this section
.testimonials-section {
  ::ng-deep app-testimonial-card {
    .testimonial-card {
      background: #ffffff;
      border: 1px solid #e2e8f0;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
      border-radius: 8px;
      padding: 20px;
      max-width: none;
      width: 100%;
      height: 100%;
      margin: 0;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      display: flex;
      flex-direction: column;
      gap: 16px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }

    .testimonial-header {
      display: flex;
      justify-content: flex-start;
    }

    .testimonial-avatar {
      .avatar-image {
        width: 56px;
        height: 56px;
        border: 2px solid #f8f9fa;
      }
    }

    .testimonial-quote {
      flex: 1;

      .quote-text {
        color: var(--Colors-Text-secondary, #616874);
        font-family: Mulish;
        font-size: 21px;
        font-style: normal;
        font-weight: 400;
        line-height: 32px; /* 152.381% */
        letter-spacing: -0.5px;
        margin: 0;
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .testimonial-footer {
      .author-info {
        flex: 1 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .author-name {
          color: var(--Colors-Text-secondary, #616874);
          font-family: Mulish;
          font-size: 17px;
          font-style: normal;
          font-weight: 700;
          line-height: 29px; /* 170.588% */
          letter-spacing: -0.2px;
          margin: 0;
        }

        .author-role {
          color: #161C2D;
          font-family: Mulish;
          font-size: 17px;
          font-style: normal;
          font-weight: 400;
          line-height: 29px; /* 170.588% */
          letter-spacing: -0.2px;
          margin: 0;
        }
      }
    }
  }
}

