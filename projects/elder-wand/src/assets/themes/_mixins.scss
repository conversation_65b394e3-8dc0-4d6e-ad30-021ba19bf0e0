// =============================================================================
// Elder Wand Theme Mixins
// =============================================================================

// Glass effect mixin
@mixin glass-effect($blur: var(--glass-backdrop-blur, 12px)) {
  backdrop-filter: blur($blur);
  -webkit-backdrop-filter: blur($blur);
  background: var(--glass-background-color, rgba(255, 255, 255, 0.25));
  border: var(--glass-border-width, 1px) solid var(--glass-border-color, rgba(255, 255, 255, 0.3));
  box-shadow: var(--glass-elevation, var(--global-elevation-02));
}

// Fallback for browsers that don't support backdrop-filter
@mixin glass-effect-fallback {
  background: var(--glass-fallback-bg, rgba(255, 255, 255, 0.65)) !important;
}

// Theme transition mixin
@mixin theme-transition {
  transition-property: background-color, background-image, color, border-color, box-shadow, opacity;
  transition-duration: var(--transition-base, 300ms);
  transition-timing-function: ease;
}

// Button mixin
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2, 0.5rem) var(--spacing-4, 1rem);
  border-radius: var(--border-radius-lg, 0.5rem);
  font-family: var(--font-family);
  font-weight: var(--font-weight-medium, 500);
  font-size: var(--font-size-sm, 0.875rem);
  line-height: 1.5;
  text-decoration: none;
  cursor: pointer;
  border: 1px solid transparent;
  @include theme-transition;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// Primary button mixin
@mixin button-primary {
  @include button-base;
  background-color: var(--color-brand-primary, var(--global-color-blue-500));
  color: var(--color-text-on-primary, var(--global-color-white));
  border-color: var(--color-brand-primary, var(--global-color-blue-500));
  
  &:hover:not(:disabled) {
    background-color: var(--color-brand-primary-hover, var(--global-color-blue-700));
    border-color: var(--color-brand-primary-hover, var(--global-color-blue-700));
  }
  
  &:active:not(:disabled) {
    background-color: var(--color-brand-primary-active, var(--global-color-blue-900));
    border-color: var(--color-brand-primary-active, var(--global-color-blue-900));
  }
}

// Secondary button mixin
@mixin button-secondary {
  @include button-base;
  background-color: var(--color-brand-secondary, var(--global-color-aqua-500));
  color: var(--color-text-on-secondary, var(--global-color-white));
  border-color: var(--color-brand-secondary, var(--global-color-aqua-500));
  
  &:hover:not(:disabled) {
    background-color: var(--color-brand-secondary-hover, var(--global-color-aqua-700));
    border-color: var(--color-brand-secondary-hover, var(--global-color-aqua-700));
  }
  
  &:active:not(:disabled) {
    background-color: var(--color-brand-secondary-active, var(--global-color-aqua-900));
    border-color: var(--color-brand-secondary-active, var(--global-color-aqua-900));
  }
}

// Card mixin
@mixin card-base {
  background: var(--card-bg, white);
  border: 1px solid var(--card-border, rgba(255, 255, 255, 0.2));
  border-radius: var(--card-border-radius, 12px);
  box-shadow: var(--card-box-shadow, 0 4px 16px rgba(0, 0, 0, 0.05));
  @include theme-transition;
  
  &:hover {
    box-shadow: var(--card-hover-shadow, rgba(0, 0, 0, 0.08));
    border-color: var(--card-hover-border, rgba(37, 99, 235, 0.3));
  }
}

// Input mixin
@mixin input-base {
  width: 100%;
  padding: var(--spacing-3, 0.75rem);
  border: 1px solid var(--color-border-default, var(--global-color-gray-300));
  border-radius: var(--border-radius-lg, 0.5rem);
  font-family: var(--font-family);
  font-size: var(--font-size-base, 1rem);
  line-height: 1.5;
  background-color: var(--color-background-primary, var(--global-color-white));
  color: var(--color-text-primary, var(--global-color-gray-700));
  @include theme-transition;
  
  &::placeholder {
    color: var(--color-text-placeholder, var(--global-color-gray-400));
  }
  
  &:focus {
    outline: none;
    border-color: var(--color-border-focus, var(--global-color-blue-500));
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }
  
  &:disabled {
    background-color: var(--color-background-disabled, var(--global-color-gray-100));
    color: var(--color-text-disabled, var(--global-color-gray-400));
    cursor: not-allowed;
  }
}

// Responsive breakpoints
@mixin mobile {
  @media (max-width: 767px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 768px) and (max-width: 1023px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1024px) {
    @content;
  }
}

// Flexbox utilities
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

@mixin flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

// Text utilities
@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-multiline-truncate($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
} 