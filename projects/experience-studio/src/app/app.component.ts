import {
  Component,
  OnInit,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';

import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { AuthTokenService } from '@shared/auth/services/auth-token.service';
import { AuthConfig } from '@shared/auth/interfaces/auth-config.interface';
import { AuthService } from '@shared/auth/services/auth.service';
import { environment } from '../environments/environment';
import { SharedAppHeaderComponent, HeaderConfig } from '@shared/components/app-header/app-header.component';
import { experienceStudioHeaderConfig } from './config/header.config';
import { ThemeService } from './shared/services/theme-service/theme.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    RouterOutlet,
    CommonModule,
    SharedAppHeaderComponent
],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit {
  @ViewChild('dialogHost', { read: ViewContainerRef })
  dialogHost!: ViewContainerRef;
  showHeaderAndNav: boolean = true;
  
  // Header configuration
  headerConfig: HeaderConfig = experienceStudioHeaderConfig;

  constructor(
    private authTokenService: AuthTokenService,
    private tokenStorage: TokenStorageService,
    private authService: AuthService,
    private router: Router,
    public themeService: ThemeService, // Make it public so it can be passed to header
  ) {}

  ngOnInit(): void {
    const authConfig: AuthConfig = {
      apiAuthUrl: environment.experianceApiAuthUrl,
      redirectUrl: environment.experianceRedirectUrl,
      postLoginRedirectUrl: '/dashboard',
      appName: 'experience-studio',
    };

    this.authService.setAuthConfig(authConfig);
    this.authTokenService.startTokenCheck();

    // Check initial authentication
    const accessToken = this.tokenStorage.getAccessToken();
    const refreshToken = this.tokenStorage.getRefreshToken();

    if (!accessToken && !refreshToken) {
      this.router.navigate(['/login']);
    }
  }

  ngOnDestroy() {
    this.authTokenService.stopTokenCheck();
  }

  // Header event handlers
  onNavigation(route: string): void {
    console.log('Experience Studio Navigation to:', route);
  }

  onProfileAction(action: string): void {
    if (action === 'logout') {
      this.authService.logout();
    }
  }

  onThemeToggle(theme: 'light' | 'dark'): void {
    console.log('Theme toggled to:', theme);
    // Theme service will handle the actual theme change
  }
}
