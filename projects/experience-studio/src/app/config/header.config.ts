import { HeaderConfig, SharedNavItem } from '@shared/components/app-header/app-header.component';

// Experience Studio specific navigation items
const experienceStudioNavItems: SharedNavItem[] = [
  {
    label: 'Dashboard',
    route: '/',
    selected: true,
    hasDropdown: false,
    icon: `assets/icons/awe_dashboard.svg`,
  },
  {
    label: 'Image to Code',
    route: '/experience/generate-application',
    selected: false,
    hasDropdown: false,
    icon: `assets/icons/awe_angular.svg`,
  },
  {
    label: 'Prompt to Code',
    route: '/experience/generate-application',
    selected: false,
    hasDropdown: false,
    icon: `assets/icons/awe_arrow_up.svg`,
  },
  {
    label: 'Generate Wireframes',
    route: 'experience/generate-ui-design',
    selected: false,
    hasDropdown: false,
    icon: `assets/icons/awe_analytics.svg`,
  },
];

// Available studio apps for the app drawer
const availableStudioApps = [
  {
    name: 'Experience Studio',
    route: '/dashboard',
    icon: 'assets/icons/awe_analytics.svg',
    description: 'AI-powered design analysis and code generation',
  },
  {
    name: 'Product Studio',
    route: '/product-studio',
    icon: 'assets/icons/awe_analytics.svg',
    description: 'Product strategy and business model canvas',
  },
  {
    name: 'Console',
    route: '/console',
    icon: 'assets/icons/awe_dashboard.svg',
    description: 'Agent management and workflow automation',
  },
  {
    name: 'Elder Wand',
    route: '/elder-wand',
    icon: 'assets/icons/awe_arrow_up.svg',
    description: 'Advanced AI development tools',
  },
];

// Available languages for the language switcher
const availableLanguages = [
  { code: 'en', name: 'English' },
  { code: 'es', name: 'Español' },
  { code: 'fr', name: 'Français' },
];

// Experience Studio header configuration
export const experienceStudioHeaderConfig: HeaderConfig = {
  logoSrc: 'assets/svgs/ascendion-logo-light.svg',
  navItems: experienceStudioNavItems,
  showOrgSelector: false,
  showThemeToggle: false,
  showAppDrawer: true,
  showProfileDropdown: true,
  showThemeToggleInProfile: true,
  showLanguageSwitcher: true,
  availableLanguages: [
    { code: 'en', name: 'English' },
    { code: 'fil', name: 'Filipino' },
    { code: 'es', name: 'Español' },
  ],
  currentApp: 'Experience Studio',
  availableApps: [
    {
      name: 'Console',
      route: '/console',
      icon: 'assets/svgs/ascendion-logo-light.svg',
      description: 'Agent & Workflow Management',
    },
    {
      name: 'Experience Studio',
      route: '/experience-studio',
      icon: 'assets/svgs/ascendion-logo-light.svg',
      description: 'UI/UX Design & Prototyping',
    },
    {
      name: 'Product Studio',
      route: '/product-studio',
      icon: 'assets/svgs/ascendion-logo-light.svg',
      description: 'Product Development',
    },
    {
      name: 'Launchpad',
      route: '/launchpad',
      icon: 'assets/svgs/ascendion-logo-light.svg',
      description: 'Project Launch Hub',
    },
  ],
   projectName: 'Experience Studio',
  redirectUrl: '/',
  // Logo animation configuration
  enableLogoAnimation: true,
  logoAnimationInterval: 3500, // 3.5 seconds between transitions
  logoAnimationStyle: 'fade-rotate', // Use 2D rotation with fade
  studioLogos: ['ascendionAAVA-logo-light.svg', 'AAVA_logo.svg', 'ES_LOGO.svg'],
  studioNames: ['Experience Studio', 'Design Innovation', 'Code Generation'],
};
