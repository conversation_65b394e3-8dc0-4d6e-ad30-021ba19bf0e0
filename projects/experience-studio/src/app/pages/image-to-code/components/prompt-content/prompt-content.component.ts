import { CommonModule } from '@angular/common';
import { Component, Input, On<PERSON><PERSON>roy, OnInit, ChangeDetectorRef, ChangeDetectionStrategy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import {
  ButtonComponent,
  PromptBarComponent,
} from '@awe/play-comp-library';
import { IconPillComponent } from 'projects/experience-studio/src/app/shared/components/icon-pill/icon-pill.component';
import { IconsComponent } from '../../../../shared/components/icons/icons.component';
import { Subscription } from 'rxjs';
import { FileAttachPillComponent,FileAttachOption } from '../../../../shared/components/file-attach-pill/file-attach-pill.component';
import { HeroSectionHeaderComponent } from '../../../../shared/components/hero-section-header/hero-section-header.component';
import { CardDataService } from '../../../../shared/services/data-services/card-data.service';
import { CodeGenerationService } from '../../../../shared/services/code-generation.service';
import {
  CompleteSelections,
  PromptBarService,
} from '../../../../shared/services/prompt-bar-services/prompt-bar.service';

// SSE Integration - Angular 19+ modern patterns
import { EnhancedSSEService } from '../../../../shared/services/enhanced-sse.service';
import { AppStateService } from '../../../../shared/services/app-state.service';
import { SelectedFile, IconStatus, IconOption, Buttons } from '../../models/image-to-code.model';
import {
  buttonLabels,
  designOptions,
  fileOptions,
  imageToApplicationFileOptions,
  promptToApplicationFileOptions,
  generateWireframesFileOptions,
  promptContentConstants,
} from '../../constants/image-to-code.constant';
import { UserService } from '../../../../shared/services/user/user.service';
import { UserSignatureService } from '../../../../shared/services/user-signature.service';
import { PromptSubmissionService } from '../../../../shared/services/prompt-submission.service';
import { ToastService } from '../../../../shared/services/toast.service';
import { CardSelectionService } from '../../../../shared/services/card-selection.service';
import { TypewriterService } from '../../../../shared/services/typewriter.service';
import { GenerationStateService } from '../../../../shared/services/generation-state.service';
import { TypewriterPlaceholderDirective } from '../../../../shared/directives/typewriter-placeholder.directive';
import { ServiceFactoryService } from '../../../../shared/services/service-factory.service';
import { GenerateUIDesignService, UIDesignData } from '../../../../shared/services/generate-ui-design.service';
import { UI_DESIGN_CONSTANTS, applicationTargetOptions } from '../../constants/ui-design.constant';
import { environment } from '../../../../../environments/environment';
import { createThemeManager } from '../../../../shared/utils/theme-manager.util';

@Component({
  selector: 'app-prompt-content',
  standalone: true,
  imports: [
    PromptBarComponent,
    ButtonComponent,
    CommonModule,
    HeroSectionHeaderComponent,
    FileAttachPillComponent,
    IconPillComponent,
    IconsComponent,
    TypewriterPlaceholderDirective,
  ],
  templateUrl: './prompt-content.component.html',
  styleUrl: './prompt-content.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PromptContentComponent implements OnInit, OnDestroy {
  currentPrompt = '';
  selectedCardTitle = 'Generate Application';

  // Modern theme management using functional approach
  private readonly themeManager = createThemeManager();
  get theme(): 'light' | 'dark' { return this.themeManager.theme(); }
  animatedTexts = promptContentConstants.animatedTexts;
  selectedFiles: SelectedFile[] = [];
  selectedFile: File | null = null;
  selectedFileName = '';
  previewFile: SelectedFile | null = null;
  readonly maxAllowedFiles = 1;
  isFileAttachDisabled = false;
  fileError = '';
  isEnhancing = false;
  isGenerating = false;

  // Dual file support for wireframes
  selectedImageFile: File | null = null;
  selectedTextFile: File | null = null;
  selectedImageFileName = '';
  selectedTextFileName = '';
  textFileContent = '';
  readonly maxWireframeFiles = 2; // 1 image + 1 text file

  // Prompt-to-application text file content (separate from image data)
  promptToAppTextContent = '';
  showPreview = false;
  isEnhancedPromptValid: boolean = true;
  isPromptEnhanced: boolean = false;
  enhanceClickCount = 0;
  maxEnhanceClicks = 2;
  isPolling = false;

  // SSE Integration - Modern Angular 19+ patterns (polling removed)
  private sseSubscription: Subscription | null = null;

  techOptions: IconOption[] = promptContentConstants.techOptions;
  selectedTech: IconOption = this.techOptions[0];
  designOptions: IconOption[] = designOptions;
  selectedDesign: IconOption = this.designOptions[0];

  // UI Design specific properties
  isUIDesignMode = false;
  cardType: string = '';
  applicationTargetOptions: IconOption[] = applicationTargetOptions;
  selectedApplicationTarget: IconOption = this.applicationTargetOptions[0]; // Mobile is first (default)
  uiDesignHeroSection = UI_DESIGN_CONSTANTS.heroSection;

  // Flow type detection properties
  isImageToApplicationFlow = false;
  isPromptToApplicationFlow = false;

  // UI Design generation state tracking
  isUIDesignGenerating = false;
  leftIcons: { name: string; status: IconStatus; color: string }[] = [];
  rightIcons: { name: string; status: IconStatus; color: string }[] = [];
  fileOptions: FileAttachOption[] = fileOptions;
  @Input() buttons: Buttons[] = buttonLabels;
  jobId: string | null = null;
  projectId: string | undefined = undefined;
  submissionData = {
    prompt: this.currentPrompt,
    timestamp: new Date().toISOString(),
    imageFile: this.selectedFile,
    imageUrl: this.selectedFile ? URL.createObjectURL(this.selectedFile) : null,
    imageDataUri: null as string | null,
    fileName: this.selectedFileName || null,
    textContent: null as string | null, // Add text content property
  };
  private promptSubscription!: Subscription;
  private cardTitleSubscription: Subscription = new Subscription();
  private textareaObserver: MutationObserver | null = null;
  private boundHandlePasteEvent!: (event: ClipboardEvent) => void;

  constructor(
    public readonly promptService: PromptBarService,
    private readonly cardDataService: CardDataService,
    private readonly codeGenerationService: CodeGenerationService,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    // SSE Integration - Angular 19+ inject pattern (polling service removed)
    private readonly enhancedSSEService: EnhancedSSEService,
    private readonly appStateService: AppStateService,
    private readonly cdr: ChangeDetectorRef,
    private readonly userService: UserService,
    private readonly userSignatureService: UserSignatureService,
    private readonly promptSubmissionService: PromptSubmissionService,
    private readonly toastService: ToastService,
    private readonly cardSelectionService: CardSelectionService,
    public readonly typewriterService: TypewriterService,
    private readonly serviceFactory: ServiceFactoryService,
    private readonly generateUIDesignService: GenerateUIDesignService,
    private readonly generationStateService: GenerationStateService
  ) {}

  ngOnInit(): void {
    this.resetComponentState();
    this.initRouteData();
    this.initThemeManager();
    this.initPromptData();
    this.initCardTitleSync();
    this.fetchUserIdOnLogin();
    this.promptSubmissionService.resetSubmissionState();
    this.initTypewriterEffect();

    // Initialize generation state service with current selections
    this.updateGenerationSelections();

    // Verify that a card was selected from the landing page
    if (!this.cardSelectionService.hasCardBeenSelected()) {
      this.router.navigate(['/experience/main']);
    }
  }

  /**
   * Initialize route data to determine UI Design mode and flow type
   * Automatically disables file attach pill when UI Design card is clicked
   */
  private initRouteData(): void {
    this.route.data.subscribe(data => {
      this.cardType = data['cardType'] || '';
      this.isUIDesignMode = this.cardType === 'Generate Wireframes';

      if (this.isUIDesignMode) {
        this.animatedTexts = UI_DESIGN_CONSTANTS.animatedTexts;
        this.selectedCardTitle = 'Generate Wireframes';
        this.buttons = UI_DESIGN_CONSTANTS.uiDesignButtonLabels;

        // 🎯 AUTOMATICALLY DISABLE FILE ATTACH PILL when UI Design card is clicked
        this.isUIDesignGenerating = true;

      } else {
        this.animatedTexts = promptContentConstants.animatedTexts;
        this.buttons = buttonLabels;

        // Re-enable file attach pill for other modes
        this.isUIDesignGenerating = false;

      }
    });

    // Detect flow type based on selected card title
    this.cardDataService.selectedCardTitle$.subscribe(cardTitle => {
      this.isImageToApplicationFlow = cardTitle === 'Image to Application';
      this.isPromptToApplicationFlow = cardTitle === 'Prompt to Application';

      // Configure file options based on flow type
      this.configureFileOptions();
    });
  }

  /**
   * Configure file options based on the current flow type
   */
  private configureFileOptions(): void {
    if (this.isImageToApplicationFlow) {
      // Image to Application flow - mandatory image upload
      this.fileOptions = imageToApplicationFileOptions;
    } else if (this.isPromptToApplicationFlow) {
      // Prompt to Application flow - optional document upload
      this.fileOptions = promptToApplicationFileOptions;
    } else if (this.isUIDesignMode) {
      // Generate Wireframes flow - accepts both images and .txt files
      this.fileOptions = generateWireframesFileOptions;
    } else {
      // Default flow - use original file options
      this.fileOptions = fileOptions;
    }
  }

  /**
   * Get the file attach text based on the current flow type
   */
  getFileAttachText(): string {
    if (this.isImageToApplicationFlow) {
      return 'Attach Ref Image';
    } else if (this.isPromptToApplicationFlow) {
      return 'Attach Files';
    } else if (this.isUIDesignMode) {
      return 'Attach';
    } else {
      return 'Attach Ref Image';
    }
  }

  /**
   * Get the file type based on the current flow type
   */
  getFileType(): 'image' | 'document' | 'mixed' {
    if (this.isImageToApplicationFlow) {
      return 'image';
    } else if (this.isPromptToApplicationFlow) {
      return 'document';
    } else if (this.isUIDesignMode) {
      return 'mixed';
    } else {
      return 'image';
    }
  }

  /**
   * Get the maximum allowed files based on the current flow type
   */
  getMaxAllowedFiles(): number {
    if (this.isUIDesignMode) {
      return this.maxWireframeFiles; // 2 files for wireframes (1 image + 1 text)
    } else {
      return this.maxAllowedFiles; // 1 file for other flows
    }
  }

  /**
   * Check if a file type is an image
   */
  isImageFile(fileType: string): boolean {
    return promptContentConstants.acceptedImageTypes.includes(fileType);
  }

  /**
   * Handle files selected from the file attach pill component
   */
  onFilesSelected(files: File[]): void {
    if (!files || files.length === 0) return;

    const file = files[0]; // Only handle the first file

    // Validate file based on current flow
    if (!this.validateFileForCurrentFlow(file)) {
      return;
    }

    // Process the file based on flow type
    if (this.isPromptToApplicationFlow) {
      this.handleTextFileSelection(file);
    } else if (this.isUIDesignMode) {
      this.handleWireframeFileSelection(file);
    } else {
      this.handleImageFileSelection(file);
    }
  }

  /**
   * Validate file based on current flow type
   */
  private validateFileForCurrentFlow(file: File): boolean {
    if (this.isPromptToApplicationFlow) {
      // Strict validation for .txt files only
      const isValidExtension = file.name.toLowerCase().endsWith('.txt');
      const isValidMimeType = file.type === 'text/plain' || file.type === '';

      if (!isValidExtension) {
        this.fileError = 'Only .txt files are allowed for Prompt to Application flow';
        this.toastService.error(this.fileError);
        return false;
      }

      // Additional MIME type check (some browsers may not set correct MIME type)
      if (file.type && !promptContentConstants.acceptedDocumentTypes.includes(file.type) && !isValidMimeType) {
        this.fileError = 'Only .txt files are allowed for Prompt to Application flow';
        this.toastService.error(this.fileError);
        return false;
      }
    } else if (this.isImageToApplicationFlow) {
      // Validate image files
      if (!promptContentConstants.acceptedImageTypes.includes(file.type)) {
        this.fileError = 'Only image files (JPEG, PNG, GIF, WEBP, SVG) are allowed for Image to Application flow';
        this.toastService.error(this.fileError);
        return false;
      }
    } else if (this.isUIDesignMode) {
      // Wireframe flow accepts both images and .txt files
      const isValidImage = promptContentConstants.acceptedImageTypes.includes(file.type);
      const isValidDocument = promptContentConstants.acceptedDocumentTypes.includes(file.type) || file.name.toLowerCase().endsWith('.txt');

      if (!isValidImage && !isValidDocument) {
        this.fileError = 'Only image files (JPEG, PNG, GIF, WEBP, SVG) or .txt files are allowed for Generate Wireframes flow';
        this.toastService.error(this.fileError);
        return false;
      }
    }

    // Check file size (5MB limit)
    const maxFileSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxFileSize) {
      this.fileError = 'File size must be less than 5MB';
      this.toastService.error(this.fileError);
      return false;
    }

    this.fileError = '';
    return true;
  }

  /**
   * Handle text file selection for Prompt to Application flow
   */
  private handleTextFileSelection(file: File): void {
    // Clear any existing files first to prevent duplication
    this.selectedFiles = [];
    this.selectedFile = null;
    this.selectedFileName = '';
    this.promptToAppTextContent = '';

    // Create file URL for display
    const fileUrl = URL.createObjectURL(file);
    const newFile: SelectedFile = {
      id: Math.random().toString(36).substring(2, 11),
      name: file.name,
      url: fileUrl,
      type: file.type || 'text/plain',
    };

    // Update component state
    this.selectedFile = file;
    this.selectedFileName = file.name;
    this.selectedFiles = [newFile];

    // Update UI state
    this.updateFileAttachPillStatus();
    this.updateIconDisabledState();
    this.updateSendButtonState();

    // Read file content for potential use in prompt processing
    const reader = new FileReader();
    reader.onload = (e) => {
      const textContent = e.target?.result as string;

      // Store text content separately (NOT in imageDataUri)
      this.promptToAppTextContent = textContent;

      // Update submission data without storing text in image-related fields
      this.submissionData = {
        ...this.submissionData,
        imageFile: null, // No image file for text uploads
        fileName: file.name,
        imageUrl: null, // No URL for text files
        imageDataUri: null, // No image data for text files
        textContent: textContent, // Store text content in dedicated field
      };
    };
    reader.readAsText(file);

    this.toastService.success('Text file attached successfully');
    this.cdr.detectChanges();
  }

  /**
   * Handle image file selection for Image to Application flow
   */
  private handleImageFileSelection(file: File): void {
    // Use existing image processing logic
    this.processFileImmediately(file);
  }

  /**
   * Handle file selection for Generate Wireframes flow (accepts both images and .txt files)
   */
  private handleWireframeFileSelection(file: File): void {
    const isTextFile = file.name.toLowerCase().endsWith('.txt') || file.type === 'text/plain';
    const isImageFile = promptContentConstants.acceptedImageTypes.includes(file.type);

    if (isTextFile) {
      // Check if text file already exists
      if (this.selectedTextFile) {
        this.fileError = 'Only one .txt file is allowed. Please remove the existing text file first.';
        this.toastService.error(this.fileError);
        return;
      }
      this.handleWireframeTextFile(file);
    } else if (isImageFile) {
      // Check if image file already exists
      if (this.selectedImageFile) {
        this.fileError = 'Only one image file is allowed. Please remove the existing image file first.';
        this.toastService.error(this.fileError);
        return;
      }
      this.handleWireframeImageFile(file);
    } else {
      this.fileError = 'Invalid file type. Only image files (JPEG, PNG, GIF, WEBP, SVG) or .txt files are allowed.';
      this.toastService.error(this.fileError);
    }
  }

  /**
   * Handle text file selection specifically for Generate Wireframes flow
   */
  private handleWireframeTextFile(file: File): void {
    // Create file URL for display
    const fileUrl = URL.createObjectURL(file);
    const newFile: SelectedFile = {
      id: `text-${Math.random().toString(36).substring(2, 11)}`,
      name: file.name,
      url: fileUrl,
      type: file.type || 'text/plain',
    };

    // Update wireframe-specific state
    this.selectedTextFile = file;
    this.selectedTextFileName = file.name;

    // Add to selectedFiles array (don't replace, add to existing)
    const existingFiles = this.selectedFiles.filter(f => !f.id.startsWith('text-'));
    this.selectedFiles = [...existingFiles, newFile];

    // Read file content and integrate with user prompt
    const reader = new FileReader();
    reader.onload = (e) => {
      const textContent = e.target?.result as string;
      this.textFileContent = textContent;

      // Log the text content to console as required
      console.log('Text file content for Generate Wireframes:', textContent);

      // Note: Text content is NOT combined with prompt - it will be passed separately as docsContent
    };
    reader.readAsText(file);

    // Update UI state
    this.updateWireframeFileAttachStatus();
    this.updateIconDisabledState();
    this.updateSendButtonState();

    this.toastService.success('Text file attached successfully for wireframe generation');
    this.cdr.detectChanges();
  }

  /**
   * Handle image file selection specifically for Generate Wireframes flow
   */
  private handleWireframeImageFile(file: File): void {
    // Create file URL for display
    const fileUrl = URL.createObjectURL(file);
    const newFile: SelectedFile = {
      id: `image-${Math.random().toString(36).substring(2, 11)}`,
      name: file.name,
      url: fileUrl,
      type: file.type,
    };

    // Update wireframe-specific state
    this.selectedImageFile = file;
    this.selectedImageFileName = file.name;

    // Add to selectedFiles array (don't replace, add to existing)
    const existingFiles = this.selectedFiles.filter(f => !f.id.startsWith('image-'));
    this.selectedFiles = [...existingFiles, newFile];

    // Process image file for data URI
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageDataUri = e.target?.result as string;
      this.submissionData = {
        ...this.submissionData,
        imageFile: file,
        imageUrl: fileUrl,
        imageDataUri: imageDataUri,
        fileName: file.name,
      };
    };
    reader.readAsDataURL(file);

    // Update UI state
    this.updateWireframeFileAttachStatus();
    this.updateIconDisabledState();
    this.updateSendButtonState();

    this.toastService.success('Image file attached successfully for wireframe generation');
    this.cdr.detectChanges();
  }





  /**
   * Update file attach status for wireframe flow (supports dual files)
   */
  private updateWireframeFileAttachStatus(): void {
    // For wireframes, disable file attach only when both files are present
    const hasMaxFiles = this.selectedFiles.length >= this.maxWireframeFiles;
    const uploadIcon = this.leftIcons.find(icon => icon.name === 'awe_enhanced_alternate');

    if (uploadIcon) {
      uploadIcon.status = hasMaxFiles ? 'disable' : 'default';
    }

    this.isFileAttachDisabled = hasMaxFiles;
  }

  /**
   * Initialize the typewriter effect for the prompt bar placeholder
   */
  private initTypewriterEffect(): void {
    // Determine static text based on mode
    const staticText = this.isUIDesignMode ? 'Ask Studio to design' : 'Ask Studio to create';

    // Start the typewriter animation with the animated texts
    this.typewriterService.startTypewriter(
      this.animatedTexts.map(text => text.replace(/"/g, '')), // Remove quotes from the texts
      staticText, // Static text based on mode
      40, // Typing speed
      30, // Erasing speed
      400, // Pause before erasing
      200 // Pause before typing
    );
  }

  ngAfterViewInit(): void {
    this.boundHandlePasteEvent = this.handlePasteEvent.bind(this);
    document.addEventListener('paste', this.boundHandlePasteEvent);
    this.setupTextareaDisableObserver();

    // Expose to window for debugging in development
    if (!environment.production) {
      (window as any).promptComponent = this;
    }
  }

  ngOnDestroy(): void {
    [this.promptSubscription, this.cardTitleSubscription].forEach(sub =>
      sub?.unsubscribe()
    );

    // SSE Integration - Modern cleanup with Angular 19+ patterns
    this.cleanupStatusMonitoring();

    this.cleanupDomListeners();

    // Stop the typewriter animation
    this.typewriterService.stopTypewriter();
  }

  /**
   * Clean up status monitoring (SSE only)
   * Uses Angular 19+ patterns with proper cleanup
   */
  private cleanupStatusMonitoring(): void {

    if (this.enhancedSSEService.isMonitoring()) {

      this.enhancedSSEService.stopMonitoring();
    }

    if (this.sseSubscription) {
      this.sseSubscription.unsubscribe();
      this.sseSubscription = null;
    }

    // Reset polling flag (for compatibility)
    this.isPolling = false;
  }

  handleEnterPressed(): void {
    if (this.isProcessing() || !this.currentPrompt?.trim()) {
      return;
    }
    this.handleEnhancedSend();
  }

  handleEnhancedSend(): void {
    if (this.isEnhancing || !this.currentPrompt?.trim()) return;
    if (this.isPromptEnhanced && !this.isEnhancedPromptValid) {
      return;
    }

    // Validate mandatory image upload for Image to Application flow
    if (this.isImageToApplicationFlow && !this.selectedFile) {
      this.toastService.error('Please attach an image to proceed with Image to Application generation.');
      return;
    }

    const prompt = this.currentPrompt.trim();
    const timestamp = new Date().toISOString();

    // Handle different file scenarios based on flow type
    let file: File | null = null;
    let imageDataUri: string | null = null;
    let imageUrl: string | null = null;
    let fileName: string | null = null;
    let textContent: string | null = null;

    if (this.isUIDesignMode) {
      // Wireframe flow - handle dual files
      file = this.selectedImageFile || this.selectedTextFile;
      if (this.selectedImageFile) {
        imageDataUri = this.submissionData.imageDataUri;
        imageUrl = URL.createObjectURL(this.selectedImageFile);
        fileName = this.selectedImageFileName;
      }
      if (this.selectedTextFile) {
        textContent = this.textFileContent;
        // If no image file, use text file as primary file
        if (!this.selectedImageFile) {
          fileName = this.selectedTextFileName;
        }
      }
    } else {
      // Other flows - single file
      file = this.selectedFile;
      imageDataUri = this.submissionData.imageDataUri;
      imageUrl = file ? URL.createObjectURL(file) : null;
      fileName = this.selectedFileName || null;

      // For prompt-to-application flow, check if it's a text file
      if (this.isPromptToApplicationFlow && file && file.name.toLowerCase().endsWith('.txt')) {
        textContent = this.promptToAppTextContent; // Use dedicated text content property
      }
    }
    this.promptService.setPrompt(prompt);
    this.promptService.setSelectedCardTitle(this.selectedCardTitle);
    this.isGenerating = true;
    this.disablePromptBarElements(true);
    this.submissionData = {
      prompt,
      timestamp,
      imageFile: file,
      imageDataUri,
      imageUrl,
      fileName,
      textContent, // Add text content for API submission
    };
    const initialData: CompleteSelections = {
      type: this.selectedCardTitle,
      prompt,
      application: 'web',
      technology: this.selectedTech?.value || 'angular',
      designLibrary: this.selectedDesign?.value || 'tailwind',
      imageUrl,
      imageDataUri,
      textContent, // Include text content for API submission
    };
    this.appStateService.setCompleteSelections(initialData);
    this.promptService.setCompleteSelections(initialData);
    this.promptSubmissionService.setPromptSubmitted(true);
    // Determine the route based on the card title
    let targetRoute = '';
    if (this.selectedCardTitle === 'Generate Wireframes') {
      targetRoute = '/generate-ui-design';
    } else if (this.selectedCardTitle === 'Generate Application') {
      targetRoute = '/generate-application';
    } else {
      // Fallback to legacy route
      targetRoute = '/code-preview';
    }

    // Reset the card selection state since we're now moving to code-preview
    this.cardSelectionService.resetSelectionState();

    this.router.navigateByUrl(targetRoute);

    // Different success messages based on mode
    if (this.isUIDesignMode) {
      this.toastService.success('Processing your request. Starting UI design generation...');
    } else {
      this.toastService.success('Processing your request. Starting code generation...');
    }

    this.handleSubmit();
  }

  handlePromptChange(newValue: string): void {
    this.currentPrompt = newValue;

    // Real-time enhanced state clearing when prompt is manually changed
    if (this.isPromptEnhanced) {
      // Clear enhanced state immediately when user manually changes the prompt
      this.isPromptEnhanced = false;
      this.isEnhancedPromptValid = true;
      this.enhanceClickCount = 0;
      // Clear enhanced state from service in real-time
      this.promptService.resetEnhancedPromptState();
    }

    // Update the service with the current prompt
    this.promptService.setPrompt(this.currentPrompt);

    // Check if the prompt is now empty and we have an image uploaded
    if (
      (!this.currentPrompt || this.currentPrompt.trim() === '') &&
      this.selectedFiles.length > 0
    ) {
      // Update to image-specific prompts based on mode
      if (this.isUIDesignMode) {
        this.animatedTexts = UI_DESIGN_CONSTANTS.imageUploadAnimatedTexts;
      } else {
        this.animatedTexts = promptContentConstants.imageUploadAnimatedTexts;
      }
      this.initTypewriterEffect();
    } else if (
      (!this.currentPrompt || this.currentPrompt.trim() === '') &&
      this.selectedFiles.length === 0
    ) {
      // Update to default prompts if no image and empty prompt based on mode
      if (this.isUIDesignMode) {
        this.animatedTexts = UI_DESIGN_CONSTANTS.animatedTexts;
      } else {
        this.animatedTexts = promptContentConstants.animatedTexts;
      }
      this.initTypewriterEffect();
    }

    this.adjustTextareaHeight();
    this.updateIconDisabledState();
    this.updateSendButtonState();
  }

  handleIconClick(event: { name: string; side: string; index: number }): void {
    const normalizedIconName = event.name.toLowerCase();
    switch (normalizedIconName) {
      case 'awe_enhanced_alternate':
        this.handleEnhancedAlternate();
        break;
      case 'awe_enhance':
        this.handleEnhanceText();
        break;
      case 'awe_enhanced_send':
        this.handleEnhancedSend();
        break;
      default:

    }
  }

  onFileRemoved(fileId: string): void {
    this.removeFile(fileId);
    this.promptService.resetEnhancedPromptState();
    this.promptService.setImage(null);
    this.isPromptEnhanced = false;
    this.isEnhancedPromptValid = true;
    this.enhanceClickCount = 0;
    // Don't clear the prompt when removing file - keep user's text
    this.updateSendButtonState();

    // Reset the animated text back to the default prompts only if prompt is empty
    if (!this.currentPrompt || this.currentPrompt.trim() === '') {
      if (this.isUIDesignMode) {
        this.animatedTexts = UI_DESIGN_CONSTANTS.animatedTexts;
      } else {
        this.animatedTexts = promptContentConstants.animatedTexts;
      }
      // Restart the typewriter effect with the original texts
      this.initTypewriterEffect();
    }

    this.toastService.info('Image removed. You can upload a new image or enter a prompt.');
  }

  closePreview(): void {
    this.previewFile = null;
    this.showPreview = false;
  }

  showFilePreview(file: SelectedFile): void {
    this.previewFile = file;
    this.showPreview = true;
  }

  truncateFileName(filename: string): string {
    const maxLength = 13;
    const dotIndex = filename.lastIndexOf('.');
    if (filename.length <= maxLength || dotIndex === -1) return filename;
    const extension = filename.slice(dotIndex);
    const nameWithoutExt = filename.slice(0, dotIndex);
    const truncatedName = nameWithoutExt.slice(0, maxLength - extension.length - 3);
    return `${truncatedName}...${extension}`;
  }

  removeFile(fileId: string): void {
    // Find the file to be removed
    const fileToRemove = this.selectedFiles.find(file => file.id === fileId);

    // Remove from selectedFiles array
    this.selectedFiles = this.selectedFiles.filter(file => file.id !== fileId);

    if (this.isUIDesignMode && fileToRemove) {
      // Handle wireframe dual file removal
      if (fileToRemove.id.startsWith('image-')) {
        // Removing image file
        this.selectedImageFile = null;
        this.selectedImageFileName = '';
        this.submissionData = {
          ...this.submissionData,
          imageFile: null,
          imageUrl: null,
          imageDataUri: null,
          fileName: null,
        };
      } else if (fileToRemove.id.startsWith('text-')) {
        // Removing text file
        this.selectedTextFile = null;
        this.selectedTextFileName = '';
        this.textFileContent = '';

        // Note: No need to restore prompt since text content was never combined with it
      }

      this.updateWireframeFileAttachStatus();
    } else {
      // Handle single file removal for other flows
      this.selectedFile = null;
      this.selectedFileName = '';

      // Clear prompt-to-application text content if it was a text file
      if (this.isPromptToApplicationFlow) {
        this.promptToAppTextContent = '';
      }

      this.submissionData = {
        ...this.submissionData,
        imageFile: null,
        imageUrl: null,
        imageDataUri: null,
        fileName: null,
        textContent: null,
      };

      this.updateFileAttachPillStatus();
    }

    // Clear file from prompt service
    this.promptService.setImage(null);
  }

  onFileOptionSelected(option: FileAttachOption): void {
    // Enhanced disabled state checking for UI Design mode - only block during generation
    if (this.isUIDesignMode && this.isUIDesignGenerating && this.isGenerating) {

      this.toastService.warning('UI Design generation in progress...');
      return;
    }

    if (this.isProcessing() || this.isFileAttachDisabled) {
      const fileTypeText = this.isPromptToApplicationFlow ? 'document' : 'image';
      this.fileError = `Only 1 ${fileTypeText} can be uploaded at a time`;
      this.toastService.warning(this.fileError);
      return;
    }
    if (option.value === 'computer') {
      // Don't call handleEnhancedAlternate() here because the file attach pill component
      // already handles the file input trigger. This prevents duplicate file inputs.
      // The actual file selection will be handled by onFilesSelected() when the user selects a file.
      return;
    }
    if (option.value === 'url') {
      if (this.isEnhancing || this.isGenerating) return;
      const url = prompt('Enter the URL of the file:');
      if (!url) return;
      const mockFile: SelectedFile = {
        id: Math.random().toString(36).substring(2, 11),
        name: url.split('/').pop() || 'file.jpg',
        url,
        type: 'image/jpeg',
      };
      this.selectedFiles = [...this.selectedFiles, mockFile];
      this.updateFileAttachPillStatus();
      this.toastService.success('Image added successfully. You can now enhance your prompt.');
    }
  }

  onTechSelected(option: IconOption): void {
    if (option.disabled || this.isProcessing()) {
      return;
    }
    this.selectedTech = option;
    this.toastService.info(`Selected ${option.name} as the framework for code generation.`);

    // Update generation state service with new selections
    this.updateGenerationSelections();
  }

  onDesignSelected(option: IconOption): void {
    if (option.disabled) return;
    this.selectedDesign = option;
    this.toastService.info(`Selected ${option.name} as the design library for code generation.`);

    // Update generation state service with new selections
    this.updateGenerationSelections();
  }

  onApplicationTargetSelected(option: IconOption): void {
    if (option.disabled || this.isProcessing()) {
      return;
    }
    this.selectedApplicationTarget = option;
    this.generateUIDesignService.setApplicationTarget(option.value as 'mobile' | 'web');
    this.toastService.info(`Selected ${option.name} for UI design generation.`);
  }

  handleEnhanceText(): void {
    if (this.enhanceClickCount >= this.maxEnhanceClicks || !this.currentPrompt?.trim()) return;
    this.isEnhancing = true;
    this.rightIcons[0].status = 'disable';
    this.disablePromptBarElements(true);
    const imageDataUris =
      this.selectedFiles.length && this.submissionData.imageDataUri
        ? [this.submissionData.imageDataUri]
        : [];

    // Determine the correct project type based on current mode
    const projectType = this.isUIDesignMode ? 'Generate Wireframes' : 'Generate Application';

    this.promptService
      .enhancePrompt(this.currentPrompt, projectType, imageDataUris)
      .subscribe({
        next: (response: any) => {
          let parsedResponse: any;
          try {
            // Handle response that might be wrapped in markdown code blocks
            let responseText = typeof response === 'string' ? response : JSON.stringify(response);

            // Check if response is wrapped in markdown code blocks
            const codeBlockMatch = responseText.match(/```(?:json)?\s*\n?([\s\S]*?)\n?```/);
            if (codeBlockMatch) {
              responseText = codeBlockMatch[1].trim();
            }

            parsedResponse = JSON.parse(responseText);
          } catch (error) {
            // If parsing fails, try to use the response as-is
            parsedResponse = typeof response === 'object' ? response : { prompt: response };
          }
          const { prompt, intent } = parsedResponse || {};
          if (!prompt) {
            this.enablePromptBar();
            return;
          }
          this.currentPrompt = prompt;
          this.promptService.setEnhancedPrompt(prompt);
          this.isPromptEnhanced = true;
          this.updateTextareaValue(prompt);
          const normalizedIntent = intent?.toLowerCase() || '';
          if (normalizedIntent === 'yes') {
            this.isEnhancedPromptValid = true;
          } else {
            this.isEnhancedPromptValid = false;
            if (this.enhanceClickCount < this.maxEnhanceClicks) {
              this.rightIcons[0].status = 'active';
            }
          }
          setTimeout(() => this.adjustTextareaHeight(), 0);
          if (normalizedIntent === 'yes') {
            this.enhanceClickCount++;
            if (this.enhanceClickCount >= this.maxEnhanceClicks) {
              this.rightIcons[0].status = 'disable';
            }
            this.toastService.success('Prompt enhanced successfully. Ready to generate code.');
          } else {
            if (this.enhanceClickCount < this.maxEnhanceClicks) {
              this.rightIcons[0].status = 'active';
            }
            this.toastService.warning(
              'Prompt needs more details. Click enhance again for better results.'
            );
          }
          this.updateSendButtonState();
        },
        error: () => {
          this.isEnhancedPromptValid = true;
          this.enablePromptBar();

          this.toastService.error(
            'Unable to enhance prompt. Please try again with more specific details.'
          );
        },
        complete: () => {
          if (this.enhanceClickCount < this.maxEnhanceClicks) {
            this.rightIcons[0].status = 'active';
          }
          this.enablePromptBar();
        },
      });
  }

  private updateSendButtonState(): void {
    const sendButtonIcon = this.rightIcons[1];
    if (sendButtonIcon) {
      if (this.isPromptEnhanced) {
        sendButtonIcon.status = this.isEnhancedPromptValid ? 'active' : 'disable';
      } else {
        sendButtonIcon.status = 'active';
      }
      const enhanceButtonIcon = this.rightIcons[0];
      if (enhanceButtonIcon) {
        enhanceButtonIcon.status =
          this.enhanceClickCount >= this.maxEnhanceClicks ? 'disable' : 'active';
        if (
          this.isPromptEnhanced &&
          !this.isEnhancedPromptValid &&
          this.enhanceClickCount < this.maxEnhanceClicks
        ) {
          enhanceButtonIcon.status = 'active';
        }
      }
    }
  }

  getIconColor(): string {
    const isDisabled =
      !this.currentPrompt?.trim() ||
      this.isEnhancing ||
      this.enhanceClickCount >= this.maxEnhanceClicks;
    return `var(--icon-${isDisabled ? 'disabled' : 'enabled'}-color)`;
  }

  /**
   * Get contextual loading text based on file selection for enhance functionality
   * @returns Appropriate loading text based on selected files
   */
  getEnhanceLoadingText(): string {
    // Check for image files first
    if (this.selectedFiles.length > 0) {
      const hasImage = this.selectedFiles.some(file =>
        file.type.startsWith('image/') ||
        /\.(jpg|jpeg|png|gif|webp|bmp|svg)$/i.test(file.name)
      );
      if (hasImage) {
        return 'Reading your image...';
      }
    }

    // Check for wireframe mode image file
    if (this.selectedImageFile) {
      return 'Reading your image...';
    }

    // Check for text files
    if (this.selectedTextFile || this.textFileContent || this.promptToAppTextContent) {
      return 'Analyzing your document...';
    }

    // Check for text files in selectedFiles array
    if (this.selectedFiles.length > 0) {
      const hasTextFile = this.selectedFiles.some(file =>
        file.type.startsWith('text/') ||
        file.name.endsWith('.txt') ||
        file.name.endsWith('.md') ||
        file.name.endsWith('.doc') ||
        file.name.endsWith('.docx')
      );
      if (hasTextFile) {
        return 'Analyzing your document...';
      }
    }

    // Default loading text when no files are selected
    return 'Enhancing prompt...';
  }

  handleSuggestionClick(suggestion: string): void {
    this.currentPrompt = suggestion.replace(/^✨\s*/, '').trim();
    this.promptService.setPrompt(this.currentPrompt);
    this.adjustTextareaHeight();
    this.updateIconDisabledState();
  }

  private initThemeManager(): void {
    // Initialize icon colors for current theme
    this.updateIconColors();

    // Set up theme change callback
    this.themeManager.onThemeChange(() => {
      this.updateIconColors();
    });
  }

  private initPromptData(): void {
    this.promptService.setSelectedCardTitle(this.selectedCardTitle);
    this.promptSubscription = this.promptService.promptData$.subscribe((data: any) => {
      // Only update currentPrompt if it's not empty or if we don't have a current prompt
      // This prevents overwriting user input when images are attached
      if (data.prompt && (!this.currentPrompt || this.currentPrompt.trim() === '')) {
        this.currentPrompt = data.prompt;
      }

      // Handle enhanced prompt state - STRICT: Only set enhanced if we have enhancedPrompt AND it's different from current
      if (
        data.enhancedPrompt &&
        data.enhancedPrompt.trim() !== '' &&
        data.enhancedPrompt !== this.currentPrompt
      ) {
        this.isPromptEnhanced = true;
        this.isEnhancedPromptValid = true;
      } else {
        // Reset enhanced state if no enhanced prompt, empty enhanced prompt, or if it matches current prompt
        this.isPromptEnhanced = false;
        this.isEnhancedPromptValid = true;
      }
      if (data.imageFile) {
        this.selectedFile = data.imageFile;
        this.selectedFileName = data.imageFile.name;
      } else {
        this.isPromptEnhanced = false;
        this.isEnhancedPromptValid = true;
        this.enhanceClickCount = 0;
      }
      setTimeout(() => {
        this.updateSendButtonState();
      }, 0);
    });
  }

  private initCardTitleSync(): void {
    this.cardTitleSubscription = this.cardDataService.selectedCardTitle$.subscribe(cardTitle => {
      if (cardTitle) {
        this.selectedCardTitle = cardTitle;
        this.promptService.setSelectedCardTitle(this.selectedCardTitle);
      }
    });
  }

  private fetchUserIdOnLogin(): void {
    const userEmail = this.userSignatureService.getUserSignatureSync();
    const handleUserId = (userId: string) => {
      this.appStateService.setUserId(userId);
      this.userService.setUserId(userId);
    };
    this.userService.getUserIdByEmail(userEmail).subscribe({
      next: handleUserId,
      error: () => handleUserId('01df7a8f-8af7-477a-9e69-7d3a236fa774'),
    });
  }

  private handlePasteEvent(event: ClipboardEvent): void {
    if (this.isProcessing() || this.isFileAttachDisabled) {
      return;
    }
    const items = event.clipboardData?.items;
    if (!items) return;
    for (const item of items) {
      if (!item.type.includes('image')) continue;
      const file = item.getAsFile();
      if (!file || !this.validateFile(file)) continue;

      // Process file immediately for fast UI response
      this.processFileImmediately(file);
      break;
    }
  }

  private handleFileReaderLoad(
    e: ProgressEvent<FileReader>,
    file: File,
    newFile: SelectedFile,
    fileUrl: string
  ): void {
    const imageDataUri = e.target?.result as string;
    this.submissionData = {
      ...this.submissionData,
      prompt: this.currentPrompt, // Keep the existing prompt
      imageFile: file,
      imageUrl: fileUrl,
      imageDataUri,
      fileName: newFile.name,
    };
    this.promptService.resetEnhancedPromptState();
    this.isPromptEnhanced = false;
    this.isEnhancedPromptValid = true;
    this.enhanceClickCount = 0;
    // Don't reset the prompt when attaching file
    // Update the service with current prompt before setting image
    this.promptService.setPrompt(this.currentPrompt);
    this.promptService.setImage(file);
    this.updateSendButtonState();
  }

  private resetComponentState(): void {
    this.currentPrompt = '';
    this.selectedFiles = [];
    this.selectedFile = null;
    this.selectedFileName = '';
    this.previewFile = null;
    this.isFileAttachDisabled = false;
    this.fileError = '';
    this.isEnhancing = false;
    this.isGenerating = false;

    // Reset wireframe dual file properties
    this.selectedImageFile = null;
    this.selectedTextFile = null;
    this.selectedImageFileName = '';
    this.selectedTextFileName = '';
    this.textFileContent = '';

    // Reset prompt-to-application text content
    this.promptToAppTextContent = '';

    // Reset UI Design generation state regardless of mode
    // File attachment is now available in UI Design mode
    this.isUIDesignGenerating = false;

    this.showPreview = false;
    this.enhanceClickCount = 0;
    this.isPolling = false;
    this.isEnhancedPromptValid = true;
    this.isPromptEnhanced = false;
    this.selectedTech = this.techOptions[0];
    this.selectedDesign = this.designOptions[0];
    this.selectedApplicationTarget = this.applicationTargetOptions[0]; // Mobile (first option)
    // Reset animated texts to default (will be updated in initRouteData)
    this.animatedTexts = promptContentConstants.animatedTexts;
    this.submissionData = {
      prompt: '',
      timestamp: new Date().toISOString(),
      imageFile: null,
      imageUrl: null,
      imageDataUri: null,
      fileName: null,
      textContent: null,
    };
    this.promptService.setPrompt('');
    this.promptService.resetEnhancedPromptState();
    this.promptService.setImage(null);

    // Reset UI Design service if in UI Design mode
    if (this.isUIDesignMode) {
      this.generateUIDesignService.resetData();
    }
  }

  private setupTextareaDisableObserver(): void {
    const observer = new MutationObserver(() => {
      if (this.isProcessing()) {
        const textareas = document.querySelectorAll('.awe-prompt-bar textarea');
        textareas.forEach(textarea => {
          const htmlTextarea = textarea as HTMLTextAreaElement;
          htmlTextarea.disabled = true;
          htmlTextarea.setAttribute('disabled', 'true');
          htmlTextarea.style.opacity = '0.7';
          htmlTextarea.style.cursor = 'not-allowed';
          htmlTextarea.style.pointerEvents = 'none';
          htmlTextarea.style.backgroundColor = 'transparent';
        });
      }
    });
    observer.observe(document.body, { childList: true, subtree: true });
    this.textareaObserver = observer;
  }

  private updateIconColors(): void {
    const iconColor = this.theme === 'dark' ? 'var(--header-icon)' : 'var(--header-icon-border)';
    this.leftIcons = [{ name: 'awe_enhanced_alternate', status: 'active', color: iconColor }];
    this.rightIcons = [
      { name: 'awe_enhance', status: 'active', color: iconColor },
      { name: 'awe_enhanced_send', status: 'active', color: iconColor },
    ];
  }

  private isProcessing(): boolean {
    return this.isEnhancing || this.isGenerating;
  }

  /**
   * Check if file attach pill should be disabled in UI Design mode
   * Disabled when: UI Design generation is in progress OR file already attached
   */
  get isFileAttachDisabledInUIDesign(): boolean {
    if (!this.isUIDesignMode) {
      return this.isFileAttachDisabled;
    }
    // Only disable during active generation, not just because we're in UI Design mode
    return this.isFileAttachDisabled || (this.isUIDesignGenerating && this.isGenerating);
  }

  /**
   * Get tooltip message for file attach pill in UI Design mode
   */
  get fileAttachTooltipMessage(): string {
    if (!this.isUIDesignMode) {
      return '';
    }

    if (this.isUIDesignGenerating && this.isGenerating) {
      return 'UI Design generation in progress...';
    }

    if (this.isFileAttachDisabled) {
      return 'Only 1 image can be uploaded at a time';
    }

    return '';
  }

  /**
   * Check if tooltip should be shown for file attach pill
   */
  get shouldShowFileAttachTooltip(): boolean {
    return this.isUIDesignMode && ((this.isUIDesignGenerating && this.isGenerating) || this.isFileAttachDisabled);
  }

  // Removed isMaxFilesReached() - now using isFileAttachDisabled based on selectedFiles.length > 0

  private validateFile(file: File): boolean {
    const acceptedImageTypes = promptContentConstants.acceptedImageTypes;
    if (!file.type) {
      this.fileError = 'Folders cannot be uploaded';
    } else if (!acceptedImageTypes.includes(file.type)) {
      this.fileError = 'Only image files (JPEG, PNG, GIF, WEBP, SVG) are allowed';
    } else if (file.size > 5 * 1024 * 1024) {
      this.fileError = 'File size must be less than 5MB';
    } else {
      this.fileError = '';
      return true;
    }
    return false;
  }

  private updateFileAttachPillStatus(): void {
    // Disable file attach after one image is attached
    const hasImage = this.selectedFiles.length > 0;
    const uploadIcon = this.leftIcons.find(icon => icon.name === 'awe_enhanced_alternate');
    if (uploadIcon) {
      uploadIcon.status = hasImage ? 'disable' : 'default';
    }
    this.isFileAttachDisabled = hasImage;
  }

  private disablePromptBarElements(isProcessing: boolean): void {
    try {
      document.body.classList.toggle('prompt-bar-processing', isProcessing);

      // Add or remove the processing class to the prompt bar
      const promptBar = document.querySelector('.prompt-bar') as HTMLElement;
      if (promptBar) {
        promptBar.classList.toggle('processing', isProcessing);
      }

      const textArea = document.querySelector('.awe-prompt-bar textarea') as HTMLTextAreaElement;
      if (textArea) {
        textArea.disabled = isProcessing;
        textArea.setAttribute('disabled', String(isProcessing));
        const textAreaStyles = isProcessing
          ? {
              opacity: '0.7',
              cursor: 'not-allowed',
              pointerEvents: 'none',
              backgroundColor: 'transparent',
            }
          : {
              opacity: '1',
              cursor: 'text',
              pointerEvents: 'auto',
              backgroundColor: '',
            };
        this.setElementStyles(textArea, true, textAreaStyles);
        if (isProcessing) {
          setTimeout(() => {
            textArea.disabled = true;
            textArea.setAttribute('disabled', 'true');
          }, 0);
        }
      }

      // Only disable pills when enhancing, not when generating
      const shouldDisablePills = this.isEnhancing;
      document.querySelectorAll('.pills-container > *').forEach(pill => {
        const htmlPill = pill as HTMLElement;
        const pillStyles = {
          opacity: shouldDisablePills ? '0.7' : '',
          pointerEvents: shouldDisablePills ? 'none' : '',
          cursor: shouldDisablePills ? 'not-allowed' : 'pointer',
          backgroundColor: shouldDisablePills ? 'transparent' : '',
        };
        this.setElementStyles(htmlPill, shouldDisablePills, pillStyles);
        this.toggleInteractiveElements(htmlPill, shouldDisablePills);
        if (shouldDisablePills) {
          htmlPill.querySelectorAll('*').forEach(child => {
            (child as HTMLElement).style.backgroundColor = 'transparent';
          });
        }
      });
      const iconGroups = [
        { selector: '.enhance-icons awe-icons', cursor: 'not-allowed' },
        { selector: '.file-item awe-icons', cursor: 'not-allowed' },
      ];
      iconGroups.forEach(({ selector, cursor }) => {
        document.querySelectorAll(selector).forEach(icon => {
          const htmlIcon = icon as HTMLElement;
          const iconStyles = {
            opacity: isProcessing ? '0.7' : '',
            pointerEvents: isProcessing ? 'none' : '',
            cursor: isProcessing ? cursor : 'pointer',
          };
          this.setElementStyles(htmlIcon, true, iconStyles);
        });
      });
    } catch (error) {

    }
  }

  public handleSubmit(): void {
    const trimmedPrompt = this.currentPrompt?.trim();
    if (!trimmedPrompt) {
      return;
    }
    this.isGenerating = true;

    if (this.isUIDesignMode) {
      // Handle UI Design generation
      this.handleUIDesignSubmit(trimmedPrompt);
    } else {
      // Handle regular application generation - call main API directly

      this.handleProjectResponse();
    }
  }

  private handleUIDesignSubmit(prompt: string): void {
    this.userSignatureService.getUserSignatureSync();
    const applicationTarget = this.selectedApplicationTarget.value as 'mobile' | 'web';

    // Get image data if available (for wireframes with dual files)
    const imageDataUri = this.selectedImageFile && this.submissionData.imageDataUri ? this.submissionData.imageDataUri : null;
    const hasImage = this.selectedImageFile && imageDataUri;

    // Get text content if available (for wireframes with dual files)
    const textContent = this.textFileContent || null;

    // Create image array for API payload
    const imageArray = hasImage ? [imageDataUri] : [];

    // Log the payload for debugging
    console.log('Wireframe generation payload:', {
      prompt,
      applicationTarget,
      hasImage,
      hasTextContent: !!textContent,
      imageArray,
      textContent
    });

    // Store UI Design data without generating (to avoid duplicate API calls)
    // COMMENTED OUT: The actual API call is now handled by the code-window component
    // This prevents duplicate calls to /wireframe-generation/generate endpoint
    this.generateUIDesignService.setCardTitle(this.selectedCardTitle);
    this.generateUIDesignService.setPromptData(prompt); // Original prompt without text content
    this.generateUIDesignService.setApplicationTarget(applicationTarget);

    // Set complete UI design data without making the API call
    const uiDesignData: UIDesignData = {
      cardTitle: this.selectedCardTitle,
      prompt, // Original prompt without text content combined
      applicationTarget,
      timestamp: new Date().toISOString(),
      ...(textContent && { docsContent: textContent }) // Include docs content as separate field
    };
    this.generateUIDesignService.setUIDesignData(uiDesignData);

    // Continue with the same flow as before
    this.handleProjectResponse();
  }

  private handleProjectResponse(): void {
    const completeData = this.buildCompleteData({ includeImage: true, includeIds: true });
    this.appStateService.setCompleteSelections(completeData);
    this.promptService.setCompleteSelections(completeData);
    this.isGenerating = false;

    // Keep file attach disabled in UI Design mode even after completion
    if (this.isUIDesignMode) {

    }

    this.generateCodeWithProject();
  }

  private setElementStyles(
    el: HTMLElement,
    isProcessing: boolean,
    styleOptions: Partial<CSSStyleDeclaration>
  ): void {
    if (!el) return;
    for (const [key, value] of Object.entries(styleOptions)) {
      if (key in el.style) {
        (el.style as any)[key] = isProcessing ? (value ?? '') : '';
      }
    }
    el.classList.toggle('disabled', isProcessing);
  }

  private toggleInteractiveElements(container: Element, isProcessing: boolean): void {
    const interactiveElements = container.querySelectorAll<HTMLElement>('button, a, input, select');
    interactiveElements.forEach(el => {
      el.toggleAttribute('disabled', isProcessing);
      el.style.pointerEvents = isProcessing ? 'none' : 'auto';
      el.style.backgroundColor = isProcessing ? 'transparent' : '';
    });
  }

  private buildCompleteData({
    technology = this.selectedTech?.value || 'angular',
    designLibrary = this.selectedDesign?.value || 'tailwind',
    includeImage,
    includeIds,
  }: {
    technology?: string;
    designLibrary?: string;
    includeImage: boolean;
    includeIds: boolean;
  }): CompleteSelections {
    // Only include image if we have valid image data and files
    // Handle both single file mode and wireframe dual file mode
    const hasValidImage =
      includeImage &&
      this.submissionData.imageDataUri &&
      (
        // Single file mode (other flows)
        (this.selectedFile && this.selectedFiles.length > 0) ||
        // Wireframe dual file mode
        (this.isUIDesignMode && this.selectedImageFile)
      );

    const data: CompleteSelections = {
      type: this.selectedCardTitle,
      prompt: this.currentPrompt,
      application: 'web',
      technology,
      designLibrary,
      ...(hasValidImage && {
        imageUrl: this.submissionData.imageDataUri,
        imageDataUri: this.submissionData.imageDataUri,
      }),
      ...(includeIds && {
        projectId: this.projectId,
        jobId: this.jobId,
      }),
    };
    return data;
  }

  private handleProjectCreationError(message: string): void {
    this.isGenerating = false;

    // Keep file attach disabled in UI Design mode even on error
    if (this.isUIDesignMode) {

    }

    // SSE Integration - Clean up both SSE and polling on error
    this.cleanupStatusMonitoring();

    this.enablePromptBar();
    this.toastService.error(message);
  }

  private generateCodeWithProject(): void {
    // 🛡️ CRITICAL: Block additional API calls in UI Design mode
    if (this.isUIDesignMode) {

      // For UI Design mode, the API call is already completed in handleUIDesignSubmit()
      // No additional API calls should be made - just enable the prompt bar
      this.enablePromptBar();
      return;
    }

    const { imageDataUri } = this.submissionData;
    // Check for valid image: must have file, data URI, and be in selectedFiles
    const hasImage = this.selectedFile && imageDataUri && this.selectedFiles.length > 0;
    const formFactor = 'web';
    const tech = this.selectedTech?.value || 'angular';
    const design = this.selectedDesign?.value || 'material';
    const userSignature = this.userSignatureService.getUserSignatureSync();

    // 📋 WORKFLOW DETERMINATION:
    // 1. Generate Application + Image = code-generation/generate/app API (image-to-code)
    // 2. Generate Application + No Image = design-generation/design-generation API (prompt-to-code)
    // 3. Generate Wireframes = wireframe-generation/generate API (handled separately)

    if (hasImage) {
      // IMAGE-TO-CODE WORKFLOW: Generate Application with image

      // Get text content for prompt-to-application flow if available
      let docsContent: string | undefined;
      if (this.isPromptToApplicationFlow && this.selectedFile && this.selectedFile.name.toLowerCase().endsWith('.txt')) {
        docsContent = this.promptToAppTextContent || undefined; // Use dedicated text content property
      }

      const request = this.codeGenerationService.mapSelectionsToRequest(
        this.currentPrompt, // Original prompt without text content
        formFactor,
        tech,
        design,
        imageDataUri!,
        userSignature,
        this.projectId || undefined,
        this.selectedCardTitle,
        docsContent // Pass docs content separately
      );
      this.codeGenerationService.generateCode(request, userSignature).subscribe({
        next: response => this.handleCodeGenerationResponse(response, tech, design, true),
        error: () =>
          this.handleProjectCreationError('Error generating code from image. Please try again.'),
      });
    } else {
      // PROMPT-TO-CODE WORKFLOW: Generate Application with prompt only

      // Get text content for prompt-to-application flow if available
      let docsContent: string | undefined;
      if (this.isPromptToApplicationFlow && this.selectedFile && this.selectedFile.name.toLowerCase().endsWith('.txt')) {
        docsContent = this.promptToAppTextContent || undefined; // Use dedicated text content property
      }

      // Use the ServiceFactoryService to create the wireframe service and avoid circular dependencies
      this.serviceFactory
        .mapSelectionsToWireframeRequest(
          this.currentPrompt, // Original prompt without text content
          formFactor,
          tech,
          design,
          this.projectId || undefined,
          this.selectedCardTitle,
          docsContent // Pass docs content separately
        )
        .then(wireframeRequest => {

          this.serviceFactory
            .generateWireframeCode(wireframeRequest, userSignature)
            .then(observable => {
              observable.subscribe({
                next: response => this.handleCodeGenerationResponse(response, tech, design, false),
                error: () =>
                  this.handleProjectCreationError(
                    'Error generating application from prompt. Please try again.'
                  ),
              });
            })
            .catch(() => {
              this.handleProjectCreationError(
                'Error generating application from prompt. Please try again.'
              );
            });
        })
        .catch(() => {
          this.handleProjectCreationError(
            'Error setting up application generation. Please try again.'
          );
        });
    }
  }

  private enablePromptBar(): void {
    this.isEnhancing = false;
    this.isGenerating = false;
    this.disablePromptBarElements(false);

    // Ensure the processing class is removed from the prompt bar
    const promptBar = document.querySelector('.prompt-bar') as HTMLElement;
    if (promptBar) {
      promptBar.classList.remove('processing');
    }

    setTimeout(() => {
      this.updateIconDisabledState();
      this.updateSendButtonState();
      if (this.enhanceClickCount < this.maxEnhanceClicks) {
        const enhanceButtonIcon = this.rightIcons[0];
        if (enhanceButtonIcon) {
          enhanceButtonIcon.status = 'active';
        }
      }
      this.disablePromptBarElements(false);
      this.adjustTextareaHeight();
      document.body.classList.remove('prompt-bar-processing');
    }, 0);
  }

  private handleCodeGenerationResponse(
    response: any,
    technology: string,
    designLibrary: string,
    hasImage: boolean
  ): void {
    // 🛡️ CRITICAL: Block response handling in UI Design mode
    if (this.isUIDesignMode) {

      return;
    }

    let parsedResponse: any;
    try {
      parsedResponse = typeof response === 'string' ? JSON.parse(response) : response;
    } catch (e) {
      this.handleProjectCreationError('Unexpected response format.');
      return;
    }
    const { job_id, project_id } = parsedResponse || {};
    if (job_id && project_id) {

      this.jobId = job_id;
      this.projectId = project_id;
      const completeData = this.buildCompleteData({
        technology,
        designLibrary,
        includeImage: hasImage,
        includeIds: true,
      });
      this.appStateService.setCompleteSelections(completeData);
      this.promptService.setCompleteSelections(completeData);
      // this.startSSEStatus();
    } else {
      const message = hasImage
        ? 'Error starting code generation from image. Please try again.'
        : 'Error starting application generation from prompt. Please try again.';
      this.handleProjectCreationError(message);
    }
  }

  private updateIconDisabledState(): void {
    requestAnimationFrame(() => {
      const isEmptyPrompt = !this.currentPrompt?.trim();
      const isProcessing = this.isProcessing();
      this.disablePromptBarElements(isProcessing);
      const enhanceIcons = document.querySelectorAll<HTMLElement>('.enhance-icons awe-icons');
      enhanceIcons.forEach((icon, index) => {
        const isEnhanceButton = index === 0;
        const isSendButton = index === 1;
        let isDisabled = isEmptyPrompt || isProcessing;
        if (isEnhanceButton) {
          isDisabled =
            isEmptyPrompt || isProcessing || this.enhanceClickCount >= this.maxEnhanceClicks;
          if (
            this.isPromptEnhanced &&
            !this.isEnhancedPromptValid &&
            this.enhanceClickCount < this.maxEnhanceClicks
          ) {
            isDisabled = isEmptyPrompt || isProcessing;
          }
        } else if (isSendButton) {
          if (this.isPromptEnhanced) {
            isDisabled = isEmptyPrompt || isProcessing || !this.isEnhancedPromptValid;
          } else {
            isDisabled = isEmptyPrompt || isProcessing;
          }
        }
        icon.classList.toggle('disabled', isDisabled);
        icon.style.cssText = `
          color: var(${isDisabled ? '--icon-disabled-color' : '--icon-enabled-color'});
          cursor: ${isDisabled ? 'not-allowed' : 'pointer'};
          opacity: ${isDisabled ? 'var(--icon-disabled-opacity, 0.9)' : '1'};
        `;
      });
    });
  }

  private updateTextareaValue(value: string): void {
    setTimeout(() => {
      const textarea = document.querySelector('.awe-prompt-bar textarea') as HTMLTextAreaElement;
      if (textarea) {
        textarea.value = value;
        const event = new Event('input', { bubbles: true });
        textarea.dispatchEvent(event);
        this.currentPrompt = value;
        this.cdr.detectChanges();
      }
    }, 0);
  }

  private adjustTextareaHeight(): void {
    const textAreas = document.querySelectorAll<HTMLTextAreaElement>('.prompt-text');
    if (!textAreas.length) return;
    let maxHeight = 0;
    textAreas.forEach(textArea => {
      textArea.style.height = 'auto';
      maxHeight = Math.max(maxHeight, textArea.scrollHeight);
    });
    const shouldApplyHeight = !!this.currentPrompt?.trim();
    const heightValue = shouldApplyHeight ? `${maxHeight}px` : '';
    textAreas.forEach(textArea => {
      textArea.style.height = heightValue;
    });
    this.cdr.detectChanges();
  }





  private handleEnhancedAlternate(): void {
    if (this.isEnhancing || this.isGenerating) {
      return;
    }
    if (this.isFileAttachDisabled) {
      const fileTypeText = this.isPromptToApplicationFlow ? 'document' : 'image';
      this.fileError = `Only 1 ${fileTypeText} can be uploaded at a time`;
      return;
    }
    const fileInput = this.createFileInput();
    fileInput.addEventListener('change', (event: Event) => this.handleFileSelect(event));
    fileInput.click();
  }

  private createFileInput(): HTMLInputElement {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';

    // Set accept attribute based on current flow
    if (this.isPromptToApplicationFlow) {
      fileInput.accept = '.txt,text/plain';
    } else if (this.isUIDesignMode) {
      fileInput.accept = 'image/*,.txt,text/plain';
    } else {
      fileInput.accept = 'image/*';
    }

    fileInput.style.display = 'none';
    document.body.appendChild(fileInput);
    return fileInput;
  }

  private handleFileSelect(event: Event): void {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];
    if (!file || !this.validateFileForCurrentFlow(file)) {
      document.body.removeChild(event.target as HTMLElement);
      return;
    }

    // Process file based on flow type
    if (this.isPromptToApplicationFlow) {
      this.handleTextFileSelection(file);
    } else if (this.isUIDesignMode) {
      this.handleWireframeFileSelection(file);
    } else {
      // Process image file immediately for fast UI response
      this.processFileImmediately(file);
    }

    document.body.removeChild(event.target as HTMLElement);
  }

  private processFileImmediately(file: File): void {
    // Reset enhancement state
    this.promptService.resetEnhancedPromptState();
    this.isPromptEnhanced = false;
    this.isEnhancedPromptValid = true;
    this.enhanceClickCount = 0;

    // Create file URL immediately for instant preview
    const fileUrl = URL.createObjectURL(file);
    const newFile: SelectedFile = {
      id: Math.random().toString(36).substring(2, 11),
      name: file.name || `pasted-${Date.now()}.png`,
      url: fileUrl,
      type: file.type,
    };

    // Update UI immediately
    this.selectedFile = file;
    this.selectedFileName = newFile.name;
    this.selectedFiles = [newFile];
    this.updateFileAttachPillStatus();
    this.updateIconDisabledState();
    this.updateSendButtonState();

    // Force immediate UI update to show file preview
    this.cdr.detectChanges();

    // Update services
    this.promptService.setPrompt(this.currentPrompt);
    this.promptService.setImage(file);

    // Process file data in background (slow part)
    const reader = new FileReader();
    reader.onload = e => this.handleFileReaderLoad(e, file, newFile, fileUrl);
    reader.readAsDataURL(file);

    // When image is uploaded, automatically append text instead of just changing placeholder
    if (!this.currentPrompt || this.currentPrompt.trim() === '') {
      // Stop the current typewriter animation
      this.typewriterService.stopTypewriter();

      // Automatically append a starter text for image-based prompts based on mode
      let imagePromptStarter: string;
      if (this.isUIDesignMode) {
        imagePromptStarter = 'Create a UI design based on this image';
      } else {
        imagePromptStarter = 'Create a website based on this image';
      }
      this.currentPrompt = imagePromptStarter;

      // Update the animated texts for future use based on mode
      if (this.isUIDesignMode) {
        this.animatedTexts = UI_DESIGN_CONSTANTS.imageUploadAnimatedTexts;
      } else {
        this.animatedTexts = promptContentConstants.imageUploadAnimatedTexts;
      }

      // Force UI update
      this.cdr.detectChanges();
    } else {
      // If there's already text, just update the animated texts for placeholder based on mode
      if (this.isUIDesignMode) {
        this.animatedTexts = UI_DESIGN_CONSTANTS.imageUploadAnimatedTexts;
      } else {
        this.animatedTexts = promptContentConstants.imageUploadAnimatedTexts;
      }
      this.initTypewriterEffect();
    }

    // Always update the service with current prompt after processing
    this.promptService.setPrompt(this.currentPrompt);
  }

  private cleanupDomListeners(): void {
    if (this.boundHandlePasteEvent) {
      document.removeEventListener('paste', this.boundHandlePasteEvent);
    }
    if (this.textareaObserver) {
      this.textareaObserver.disconnect();
      this.textareaObserver = null;
    }
  }

  /**
   * Update generation state service with current selections
   * Used for template loading and repository metadata features
   */
  private updateGenerationSelections(): void {
    const framework = this.selectedTech?.value || 'angular';
    const designLibrary = this.selectedDesign?.value || 'tailwind';
    const applicationTarget = this.selectedApplicationTarget?.value || 'web';

    this.generationStateService.updateSelections(framework, designLibrary, applicationTarget);

  }
}
