import {
  Component,
  OnInit,
  OnD<PERSON>roy,
  ChangeDetectionStrategy,
  inject,
  signal,
  computed
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { interval, map, startWith, switchMap, of, EMPTY } from 'rxjs';
import { WireframeMockDataService } from '../../../../services/wireframe-mock-data.service';
import { WireframeGenerationStateService } from '../../../../services/wireframe-generation-state.service';
import { WireframeGenerationStep } from '../../../../interfaces/wireframe-mock-data.interface';
import { ThemeService } from '../../../../services/theme-service/theme.service';

interface TypewriterState {
  currentText: string;
  isComplete: boolean;
  currentStep: WireframeGenerationStep | null;
}

@Component({
  selector: 'app-wireframe-typewriter',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="wireframe-typewriter" [class.dark-theme]="isDarkTheme()">
      <div class="typewriter-content">
        <span class="typewriter-text">{{ displayText() }}</span>
        <span class="typewriter-cursor" [class.blinking]="!isTyping()">|</span>
      </div>
      <div class="typewriter-description" *ngIf="currentStepDescription()">
        {{ currentStepDescription() }}
      </div>
    </div>
  `,
  styles: [`
    .wireframe-typewriter {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 24px;
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      opacity: 0;
      animation: fadeIn 0.5s ease-in-out forwards;
    }

    .typewriter-content {
      display: flex;
      align-items: center;
      min-height: 24px;
      position: relative;
    }

    .typewriter-text {
      font-size: 16px;
      font-weight: 500;
      color: #374151;
      letter-spacing: 0.025em;
      transition: color 0.3s ease;
    }

    .typewriter-cursor {
      font-size: 16px;
      font-weight: 500;
      color: #3b82f6;
      margin-left: 2px;
      animation: none;
      transition: color 0.3s ease;
    }

    .typewriter-cursor.blinking {
      animation: blink 1s infinite;
    }

    .typewriter-description {
      font-size: 14px;
      color: #6b7280;
      margin-top: 8px;
      text-align: center;
      max-width: 400px;
      line-height: 1.4;
      opacity: 0.8;
      transition: color 0.3s ease, opacity 0.3s ease;
    }

    /* Dark theme styles */
    .wireframe-typewriter.dark-theme .typewriter-text {
      color: #e5e7eb;
    }

    .wireframe-typewriter.dark-theme .typewriter-description {
      color: #9ca3af;
    }

    .wireframe-typewriter.dark-theme .typewriter-cursor {
      color: #60a5fa;
    }

    @keyframes blink {
      0%, 50% { opacity: 1; }
      51%, 100% { opacity: 0; }
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .wireframe-typewriter {
        margin-top: 16px;
      }

      .typewriter-text {
        font-size: 14px;
      }

      .typewriter-description {
        font-size: 12px;
        max-width: 300px;
        margin-top: 6px;
      }
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class WireframeTypewriterComponent implements OnInit, OnDestroy {
  private readonly wireframeMockDataService = inject(WireframeMockDataService);
  private readonly wireframeGenerationStateService = inject(WireframeGenerationStateService);
  private readonly themeService = inject(ThemeService);

  // Signals for reactive state management
  private readonly typewriterState = signal<TypewriterState>({
    currentText: '',
    isComplete: false,
    currentStep: null
  });

  // Theme signal
  private readonly currentTheme = signal<'light' | 'dark'>(this.themeService.getCurrentTheme());

  // Computed signals
  readonly displayText = computed(() => this.typewriterState().currentText);
  readonly isTyping = computed(() => !this.typewriterState().isComplete);
  readonly currentStepDescription = computed(() => this.typewriterState().currentStep?.description || '');
  readonly isDarkTheme = computed(() => this.currentTheme() === 'dark');

  private readonly TYPING_SPEED = 50; // milliseconds per character
  private readonly PAUSE_BETWEEN_STEPS = 2000; // milliseconds

  ngOnInit(): void {
    this.initializeTypewriterAnimation();
    this.initializeThemeSubscription();
  }

  ngOnDestroy(): void {
    // Cleanup handled by takeUntilDestroyed
  }

  private initializeThemeSubscription(): void {
    // Subscribe to theme changes
    this.themeService.themeObservable
      .pipe(takeUntilDestroyed())
      .subscribe(theme => {
        this.currentTheme.set(theme);
      });
  }

  private initializeTypewriterAnimation(): void {
    // Subscribe to current step changes from wireframe mock data service
    this.wireframeMockDataService.currentStep$
      .pipe(
        takeUntilDestroyed(),
        switchMap(step => {
          if (!step) return EMPTY;

          // Update current step
          this.typewriterState.update(state => ({
            ...state,
            currentStep: step,
            isComplete: false
          }));

          // Start typewriter animation for the step title
          return this.createTypewriterAnimation(step.title);
        })
      )
      .subscribe(text => {
        this.typewriterState.update(state => ({
          ...state,
          currentText: text,
          isComplete: text === state.currentStep?.title
        }));
      });

    // Handle generation completion
    this.wireframeGenerationStateService.isGenerating$
      .pipe(takeUntilDestroyed())
      .subscribe(isGenerating => {
        if (!isGenerating) {
          // Reset typewriter state when generation stops
          this.typewriterState.update(state => ({
            ...state,
            currentText: '',
            isComplete: true,
            currentStep: null
          }));
        }
      });
  }

  private createTypewriterAnimation(targetText: string) {
    if (!targetText || targetText.length === 0) {
      return of('');
    }

    const chars = targetText.split('');

    return interval(this.TYPING_SPEED).pipe(
      startWith(0),
      map(index => {
        if (index >= chars.length) {
          return targetText;
        }
        return chars.slice(0, index + 1).join('');
      }),
      takeUntilDestroyed()
    );
  }
}
