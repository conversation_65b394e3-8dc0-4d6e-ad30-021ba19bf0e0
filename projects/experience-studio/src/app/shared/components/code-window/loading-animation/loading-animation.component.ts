import { Component, Input, OnInit, OnDestroy, ChangeDetectionStrategy, inject, DestroyRef, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BehaviorSubject } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { TextTransformationService } from '../../../services/text-transformation.service';

@Component({
  selector: 'app-loading-animation',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './loading-animation.component.html',
  styleUrls: ['./loading-animation.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LoadingAnimationComponent implements OnInit, OnDestroy {
  @Input() messages: string[] = [];
  @Input() theme: 'light' | 'dark' = 'dark';
  @Input() enableTypewriter: boolean = false;
  @Input() isWireframeGeneration: boolean = false;

  currentMessage$ = new BehaviorSubject<string>('');

  // Angular 19+ modern patterns
  private destroyRef = inject(DestroyRef);
  readonly typewriterText = signal<string>('');
  readonly isTyping = signal<boolean>(false);

  private currentIndex = 0;
  private messageInterval: any;
  private transformedMessages: string[] = [];
  private typewriterTimeout: any;
  private currentCharIndex = 0;

  // Wireframe generation specific messages
  private wireframeMessages = [
    'Analyzing your requirements...',
    'Creating wireframe structure...',
    'Designing UI components...',
    'Optimizing layout design...',
    'Generating responsive wireframes...',
    'Finalizing UI elements...',
    'Preparing wireframe preview...'
  ];

  constructor(private textTransformationService: TextTransformationService) {}

  ngOnInit() {
    this.transformMessages();
    if (this.enableTypewriter && this.isWireframeGeneration) {
      this.startWireframeTypewriter();
    } else {
      this.rotateMessages();
    }
  }

  ngOnDestroy() {
    this.cleanup();
  }

  private cleanup(): void {
    if (this.messageInterval) {
      clearInterval(this.messageInterval);
    }
    if (this.typewriterTimeout) {
      clearTimeout(this.typewriterTimeout);
    }
  }

  private transformMessages() {
    this.transformedMessages = this.textTransformationService.transformMessages(this.messages);

    if (this.transformedMessages.length === 0) {
      this.transformedMessages = ['Loading...'];
    }
  }

  private rotateMessages() {
    this.currentMessage$.next(this.transformedMessages[0]);

    if (this.transformedMessages.length > 1) {
      this.messageInterval = setInterval(() => {
        this.currentIndex = (this.currentIndex + 1) % this.transformedMessages.length;
        this.currentMessage$.next(this.transformedMessages[this.currentIndex]);
      }, 3000);
    }
  }

  /**
   * Start typewriter animation for wireframe generation
   * Uses Angular 19+ patterns with signals and takeUntilDestroyed
   */
  private startWireframeTypewriter(): void {
    this.isTyping.set(true);
    this.currentIndex = 0;
    this.currentCharIndex = 0;
    this.typeNextMessage();
  }

  /**
   * Type the next message in the wireframe messages array
   */
  private typeNextMessage(): void {
    if (this.currentIndex >= this.wireframeMessages.length) {
      // Restart from beginning
      this.currentIndex = 0;
    }

    const currentMessage = this.wireframeMessages[this.currentIndex];
    this.currentCharIndex = 0;
    this.typewriterText.set('');
    this.typeCharacter(currentMessage);
  }

  /**
   * Type individual characters with typewriter effect
   */
  private typeCharacter(message: string): void {
    if (this.currentCharIndex < message.length) {
      const currentText = message.substring(0, this.currentCharIndex + 1);
      this.typewriterText.set(currentText);
      this.currentCharIndex++;

      this.typewriterTimeout = setTimeout(() => {
        this.typeCharacter(message);
      }, 50); // Typing speed
    } else {
      // Message complete, pause then move to next
      this.typewriterTimeout = setTimeout(() => {
        this.currentIndex++;
        this.typeNextMessage();
      }, 2000); // Pause between messages
    }
  }
}
