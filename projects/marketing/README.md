# Marketing Studio

A modern Angular application for managing marketing campaigns and analytics.

## Features

- Campaign Management
- Analytics Dashboard
- AI-powered Insights
- Modern UI/UX

## Development

### Prerequisites

- Node.js 20+
- Angular CLI 19+

### Setup

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npm run start:marketing
   ```

3. Open your browser and navigate to `http://localhost:4204`

### Build

To build the project for production:

```bash
npm run build:marketing
```

### Docker

To build and run with Docker:

```bash
docker build -t marketing-studio .
docker run -p 8080:8080 marketing-studio
```

## Project Structure

```
src/
├── app/
│   ├── pages/
│   │   └── home/
│   │       └── home.component.ts
│   ├── app.component.ts
│   ├── app.config.ts
│   └── app.routes.ts
├── assets/
├── environments/
│   ├── environment.ts
│   └── environment.prod.ts
├── bootstrap.ts
├── main.ts
└── styles.scss
```

## Configuration

The application uses environment-specific configuration files:

- `environment.ts` - Development configuration
- `environment.prod.ts` - Production configuration

## Module Federation

This project is configured for Module Federation, allowing it to be used as a micro-frontend in larger applications. 