// .marketplace-container {
//   min-height: 100vh;
//   background: transparent; // Let hero component handle background
// }

// // Features Section
// .features-section {
//   padding: 80px 0;
//   background: white;
//   color: #1f2937;

//   .container {
//     max-width: 1200px;
//     margin: 0 auto;
//     padding: 0 2rem;
//   }

//   .section-title {
//     text-align: center;
//     font-size: 2.5rem;
//     font-weight: 700;
//     margin-bottom: 3rem;
//     color: #1f2937;
//   }

//   .features-grid {
//     display: grid;
//     grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
//     gap: 2rem;

//     .feature-card {
//       text-align: center;
//       padding: 2rem;
//       border-radius: 16px;
//       background: #f9fafb;
//       border: 1px solid #e5e7eb;
//       transition:
//         transform 0.3s ease,
//         box-shadow 0.3s ease;

//       &:hover {
//         transform: translateY(-5px);
//         box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
//       }

//       .feature-icon {
//         margin-bottom: 1.5rem;
//       }

//       h3 {
//         font-size: 1.5rem;
//         font-weight: 600;
//         margin-bottom: 1rem;
//         color: #1f2937;
//       }

//       p {
//         color: #6b7280;
//         line-height: 1.6;
//       }
//     }
//   }
// }

// // CTA Section
// .cta-section {
//   padding: 80px 0;
//   background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
//   color: white;

//   .container {
//     max-width: 800px;
//     margin: 0 auto;
//     padding: 0 2rem;
//     text-align: center;
//   }

//   .cta-content {
//     h2 {
//       font-size: 2.5rem;
//       font-weight: 700;
//       margin-bottom: 1rem;
//     }

//     p {
//       font-size: 1.25rem;
//       margin-bottom: 2rem;
//       opacity: 0.9;
//     }
//   }
// }

// // Responsive Design
// @media (max-width: 768px) {
//   .features-section,
//   .cta-section {
//     padding: 60px 0;

//     .container {
//       padding: 0 1rem;
//     }
//   }

//   .features-grid {
//     grid-template-columns: 1fr;
//   }

//   .section-title {
//     font-size: 2rem;
//   }

//   .cta-content h2 {
//     font-size: 2rem;
//   }
// }
