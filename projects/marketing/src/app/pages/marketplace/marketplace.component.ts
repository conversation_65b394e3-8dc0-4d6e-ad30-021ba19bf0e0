import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TokenStorageService } from '../../shared/auth/services/token-storage.service';
import {
  HeroBannerComponent,
  HeroAction,
} from '../../shared/components/hero-banner/hero-banner.component';
import { MarketplaceFooterComponent } from '../../shared/components/marketplace-footer/marketplace-footer.component';
import { NewsBlogsComponent } from '../../shared/components/news-blogs/news-blogs.component';
import { TestimonialsSectionComponent } from '../../shared/components/testimonials-section/testimonials-section.component';
import { ClientsSectionComponent } from '../../shared/components/clients-section/clients-section.component';
import { AnalyticsComponent } from '../../shared/components/analytics/analytics.component';
import { MarketplaceAgentsSectionComponent } from '../../shared/components/marketplace-agents-section/marketplace-agents-section.component';

interface HeroConfig {
  title: string;
  brandText: string;
  subtitle: string;
  description: string;
  imageSrc: string;
  imageAlt: string;
  actions: HeroAction[];
  backgroundColor: string;
  textColor: string;
  layout: 'default' | 'centered' | 'image-left' | 'image-right';
}

@Component({
  selector: 'app-marketplace',
  standalone: true,
  imports: [
    CommonModule,
    HeroBannerComponent,
    MarketplaceFooterComponent,
    NewsBlogsComponent,
    TestimonialsSectionComponent,
    ClientsSectionComponent,
    AnalyticsComponent,
    MarketplaceAgentsSectionComponent,
  ],
  templateUrl: './marketplace.component.html',
  styleUrl: './marketplace.component.scss',
})
export class MarketplaceComponent implements OnInit {
  showAuthStatus = false;
  authStatusMessage = '';

  heroConfig: HeroConfig = {
    title: 'The Next Generation',
    brandText: 'Software Engineering',
    subtitle: 'Powered by AI. Crafted for engineers.',
    description:
      'Revolutionize your development workflow with intelligent Studios, autonomous Agents, and seamless Workflows that adapt to your team needs.',
    imageSrc: 'marketplace-hero-image.png', // Updated path - files in public/ folder are served from root
    imageAlt: 'AI Robot working on development tasks',
    actions: [
      {
        label: 'Let’s hop in to AAVA',
        variant: 'primary',
        size: 'large',
        action: () => this.signIn(),
      },
    ],
    backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    textColor: 'white',
    layout: 'default',
  };

  constructor(
    private router: Router,
    private tokenStorage: TokenStorageService,
  ) {}

  ngOnInit(): void {
    console.log('🏪 Marketplace component loaded');
    this.checkAuthenticationStatus();
  }

  private checkAuthenticationStatus(): void {
    const accessToken = this.tokenStorage.getAccessToken();
    const refreshToken = this.tokenStorage.getRefreshToken();

    if (accessToken) {
      this.showAuthStatus = true;
      // this.authStatusMessage =
      //   '✅ You are already signed in. Click "Sign In to Continue" to access your dashboard.';
    } else if (refreshToken) {
      this.showAuthStatus = true;
      // this.authStatusMessage =
      //   '🔄 Session expired. Please sign in again to continue.';
    } else {
      this.showAuthStatus = true;
      // this.authStatusMessage =
      //   '👋 Welcome! Sign in to access your personalized AI workspace.';
    }
  }

  signIn(): void {
    console.log('🔐 Navigating to login...');
    this.router.navigate(['/login']);
  }

  exploreMarketplace(): void {
    // Smooth scroll to features section
    const featuresSection = document.querySelector('.features-section');
    if (featuresSection) {
      featuresSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  }
}
