:root {
  // Font family
  --font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-size: 16px;
  --line-height: 1.5;
  
  // Light Theme Variables (default)
  
  // Core colors
  --background-color: #ffffff;
  --text-color: #333333;
  --text-secondary: #666666;
  
  // Background image
  // --bg-image: url('assets/images/bg-light.png') !important;
  
  // Glass effect
  --glass-bg: linear-gradient(102.14deg, rgba(255, 255, 255, 0.5) 1.07%, rgba(255, 255, 255, 0.6) 98.01%);
  --glass-border: rgba(255, 255, 255, 0.3);
  --glass-shadow: rgba(31, 38, 135, 0.07);
  
  // Branding colors
  --primary-start: #2563EB;
  --primary-end: #03BDD4;
  --gradient-primary: linear-gradient(90deg, var(--primary-start) 0%, var(--primary-end) 100%);
  
  // Component colors
  --card-bg: white;
  --card-border: rgba(255, 255, 255, 0.2);
  --card-shadow: rgba(0, 0, 0, 0.05);
  --card-hover-shadow: rgba(0, 0, 0, 0.08);
  --card-hover-border: rgba(37, 99, 235, 0.3);
  --card-border-radius: 12px;
  --card-border-color: rgba(255, 255, 255, 0.2);
  --card-box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  --card-header-bg: transparent;
  --card-header-color: var(--text-color);
  --card-body-bg: transparent;
  --card-body-color: var(--text-color);
  --card-footer-bg: transparent;
  --card-footer-color: var(--text-color);
  
  // Dashboard specific colors
  --dashboard-primary: #2563EB;
  --dashboard-secondary: #03BDD4;
  --dashboard-text-primary: #333;
  --dashboard-text-secondary: #666;
  --dashboard-text-kpi-title: #666D99;
  --dashboard-text-kpi-value: #3D415C;
  --dashboard-border-light: #F0F0F5;
  --dashboard-bg-light: #f8f8f8;
  --dashboard-bg-lighter: #f5f5f5;
  --dashboard-bg-action-button: #fff;
  --dashboard-bg-icon-button: #F6F6FE;
  --dashboard-quick-action-button-bg: var(--gradient-primary);
  --dashboard-quick-action-text: #ffffff;
  --dashboard-text-welcome: #33364D;
  --dashboard-shadow-color: rgba(0, 0, 0, 0.05);
  --dashboard-shadow-hover: rgba(37, 99, 235, 0.15);
  --dashboard-shadow-medium: rgba(0, 0, 0, 0.08);
  --dashboard-shadow-strong: rgba(0, 0, 0, 0.12);
  --dashboard-border-accent: rgba(37, 99, 235, 0.2);
  --dashboard-action-button-border: #e0e0e0;
  --dashboard-card-bg: #FFFFFF;
  --dashboard-scrollbar-track: #f6f6f6;
  --dashboard-scrollbar-thumb: rgba(37, 99, 235, 0.15);
  --dashboard-scrollbar-thumb-hover: rgba(37, 99, 235, 0.3);
  --dashboard-scrollbar-thumb-border: rgba(37, 99, 235, 0.05);
  --dashboard-toggle-stroke: #2563EB;
  --dashboard-toggle-stroke-collapsed: white;
  --dashboard-fading-border: rgba(240, 240, 245, 0.6);
  --dashboard-table-row-border: rgba(240, 240, 245, 0.6);
  --dashboard-copyright: #6E6E80;
  --dashboard-approve-button-bg: #D6FFF6;
  --dashboard-approve-button-color: #474C6B;
  --dashboard-approve-button-border: #02D4A6;
  --dashboard-approve-button-shadow: rgba(2, 212, 166, 0.15);
  
  // DataCard specific
  --tag-bg: rgba(255, 255, 255, 0.5);
  --tag-text: #333333;
  --tag-border: rgba(120, 117, 117, 0.2);
  --subtag-bg: rgba(248, 248, 248, 0.5);
  --subtag-text: #666666;
  --subtag-border: rgba(238, 238, 238, 0.5);
  --action-icon-color: #858AAD;
  --action-button-hover: rgba(240, 240, 240, 0.7);
  --action-button-hover-color: #333333;
  --view-icon-color: #4a90e2;
  --edit-icon-color: #6cc04a;
  --delete-icon-color: #e25a4a;
  
  // Code styling
  --code-bg: #1e293b;
  --code-color: #f8fafc;
  --code-scrollbar-track: rgba(255, 255, 255, 0.05);
  --code-scrollbar-thumb: rgba(255, 255, 255, 0.2);
  --code-scrollbar-thumb-hover: rgba(255, 255, 255, 0.3);
  
  // Status indicators
  --status-success: #10b981;
  --status-warning: #f59e0b;
  --status-error: #ef4444;
  --status-info: #3b82f6;
  
  // Scrollbar colors
  --scrollbar-track: #f6f6f6;
  --scrollbar-thumb: rgba(37, 99, 235, 0.15);
  --scrollbar-thumb-hover: rgba(37, 99, 235, 0.3);
  
  // Global color palette
  --global-color-white: #ffffff;
  --global-color-black: #000000;
  --global-color-gray-50: #f9fafb;
  --global-color-gray-100: #f3f4f6;
  --global-color-gray-200: #e5e7eb;
  --global-color-gray-300: #d1d5db;
  --global-color-gray-400: #9ca3af;
  --global-color-gray-500: #6b7280;
  --global-color-gray-600: #4b5563;
  --global-color-gray-700: #374151;
  --global-color-gray-800: #1f2937;
  --global-color-gray-900: #111827;
  
  // Blue colors
  --global-color-blue-50: #eff6ff;
  --global-color-blue-100: #dbeafe;
  --global-color-blue-200: #bfdbfe;
  --global-color-blue-300: #93c5fd;
  --global-color-blue-400: #60a5fa;
  --global-color-blue-500: #3b82f6;
  --global-color-blue-600: #2563eb;
  --global-color-blue-700: #1d4ed8;
  --global-color-blue-800: #1e40af;
  --global-color-blue-900: #1e3a8a;
  
  // Green colors
  --global-color-green-50: #f0fdf4;
  --global-color-green-100: #dcfce7;
  --global-color-green-200: #bbf7d0;
  --global-color-green-300: #86efac;
  --global-color-green-400: #4ade80;
  --global-color-green-500: #22c55e;
  --global-color-green-600: #16a34a;
  --global-color-green-700: #15803d;
  --global-color-green-800: #166534;
  --global-color-green-900: #14532d;
  
  // Red colors
  --global-color-red-50: #fef2f2;
  --global-color-red-100: #fee2e2;
  --global-color-red-200: #fecaca;
  --global-color-red-300: #fca5a5;
  --global-color-red-400: #f87171;
  --global-color-red-500: #ef4444;
  --global-color-red-600: #dc2626;
  --global-color-red-700: #b91c1c;
  --global-color-red-800: #991b1b;
  --global-color-red-900: #7f1d1d;
  
  // Yellow colors
  --global-color-yellow-50: #fffbeb;
  --global-color-yellow-100: #fef3c7;
  --global-color-yellow-200: #fde68a;
  --global-color-yellow-300: #fcd34d;
  --global-color-yellow-400: #fbbf24;
  --global-color-yellow-500: #f59e0b;
  --global-color-yellow-600: #d97706;
  --global-color-yellow-700: #b45309;
  --global-color-yellow-800: #92400e;
  --global-color-yellow-900: #78350f;
  
  // Purple colors
  --global-color-purple-50: #faf5ff;
  --global-color-purple-100: #f3e8ff;
  --global-color-purple-200: #e9d5ff;
  --global-color-purple-300: #d8b4fe;
  --global-color-purple-400: #c084fc;
  --global-color-purple-500: #a855f7;
  --global-color-purple-600: #9333ea;
  --global-color-purple-700: #7c3aed;
  --global-color-purple-800: #6b21a8;
  --global-color-purple-900: #581c87;
  
  // Elevation tokens
  --global-elevation-01: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --global-elevation-02: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --global-elevation-03: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --global-elevation-04: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --global-elevation-05: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  // Font weights
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  // Font sizes
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  // Spacing
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  
  // Border radius
  --border-radius-none: 0;
  --border-radius-sm: 0.125rem;
  --border-radius-base: 0.25rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 0.75rem;
  --border-radius-2xl: 1rem;
  --border-radius-3xl: 1.5rem;
  --border-radius-full: 9999px;
  
  // Transitions
  --transition-fast: 150ms ease-in-out;
  --transition-base: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
  
  // Z-index
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
} 