import {
  HeaderConfig,
  SharedNavItem,
} from '@shared/components/app-header/app-header.component';

// Product Studio specific navigation items
const productStudioNavItems: SharedNavItem[] = [
  {
    label: 'Dashboard',
    route: '/dashboard',
    selected: true,
    hasDropdown: false,
    icon: `assets/icons/dashboard.svg`,
  },
  {
    label: 'Business Model Canvas',
    route: '/product/canvas',
    selected: false,
    hasDropdown: false,
    icon: `assets/icons/canvas.svg`,
  },
  {
    label: 'Product Analysis',
    route: '/product/analysis',
    selected: false,
    hasDropdown: false,
    icon: `assets/icons/analytics.svg`,
  },
  {
    label: 'Market Research',
    route: '/product/research',
    selected: false,
    hasDropdown: false,
    icon: `assets/icons/research.svg`,
  },
];

// Available studio apps for the app drawer
const availableStudioApps = [
  {
    name: 'Product Studio',
    route: '/dashboard',
    icon: 'assets/icons/analytics.svg',
    description: 'Product strategy and business model canvas',
  },
  {
    name: 'Experience Studio',
    route: '/experience-studio',
    icon: 'assets/icons/analytics.svg',
    description: 'AI-powered design analysis and code generation',
  },
  {
    name: 'Elder Wand',
    route: '/elder-wand',
    icon: 'assets/icons/dashboard.svg',
    description: 'Central application launcher and hub',
  },
  {
    name: 'Console',
    route: '/console',
    icon: 'assets/icons/dashboard.svg',
    description: 'Agent management and workflow automation',
  },
];

// Available languages for the language switcher
const availableLanguages = [
  { code: 'en', name: 'English' },
  { code: 'es', name: 'Español' },
  { code: 'fr', name: 'Français' },
];

// Product Studio header configuration
export const productStudioHeaderConfig: HeaderConfig = {
  logoSrc: 'assets/svgs/ascendion-logo-light.svg',
  navItems: productStudioNavItems,
  showOrgSelector: false,
  showThemeToggle: true,
  showAppDrawer: true,
  showProfileDropdown: true,
  showThemeToggleInProfile: true,
  showLanguageSwitcher: true,
  projectName: 'Product Studio',
  redirectUrl: '/dashboard',
  currentApp: 'Product Studio',
  availableApps: availableStudioApps,
  availableLanguages: availableLanguages,
  enableLogoAnimation: true,
  logoAnimationInterval: 3500, // 3.5 seconds between transitions
  logoAnimationStyle: 'fade-rotate', // Use 2D rotation with fade
  studioLogos: ['ascendionAAVA-logo-light.svg', 'AAVA_logo.svg', 'PS_LOGO.svg'],
  studioNames: ['Experience Studio', 'Design Innovation', 'Code Generation'],
};
