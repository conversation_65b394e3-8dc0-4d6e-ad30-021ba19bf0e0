<div class="brainstorming-container p-2">
  <!-- Page Header -->
  <!-- <div class="page-header mb-4">
    <h1 class="page-title mb-1">{{ currentStep?.label || 'Brainstorming' }}</h1>
  </div> -->

  <!-- Custom Stepper -->
  <!-- <section class="stepper-section">
    <app-brainstormer-stepper
      [showDescriptions]="true"
      [showProgressBar]="false"
      [allowStepClick]="false"
      (stepChanged)="onStepChanged($event)"
    >
    </app-brainstormer-stepper>
  </section> -->



  <!-- Split Screen View - Conditional based on showSplitScreen -->
  <awe-split-screen
    [leftPanelWidth]="23"
    [toggle]="true"
    [theme]="isDarkMode ? 'dark' : 'light'"
    class="split-screen-container mt-4 always-visible"
  >
    <!-- Left Panel Content (Chat) - Always Visible -->
    <div slot="left-panel">
      <app-chat-panel></app-chat-panel>
    </div>

    <!-- Right Panel Content (Main App) -->
    <div slot="right-panel" class="main-content">
      <!-- Right Panel Header with Step Navigation -->
      <app-right-panel-header
        [currentStep]="currentStep"
        [isHeaderLoading]="isHeaderLoading"
        [headerLoadingMessage]="headerLoadingMessage"
        (nextStepClicked)="nextStep()"
        (previousStepClicked)="previousStep()"
      ></app-right-panel-header>

      <div class="content-container-panel">
        <!-- Understanding Component -->
        <div *ngIf="shouldShowComponent('understanding')" class="step-content">
          <app-understanding></app-understanding>
        </div>

        <!-- User Persona Component -->
        <div *ngIf="shouldShowPersonaList()" class="step-content">
          <app-user-persona
            (personaSelected)="showPersonaDetailsView($event)"
          ></app-user-persona>
        </div>

        <!-- Persona Details Component -->
        <div *ngIf="shouldShowPersonaDetails()" class="step-content">
          <app-persona-details
            (backToList)="hidePersonaDetailsView()"
          ></app-persona-details>
        </div>

        <!-- Fe0ure List Component -->
        <div *ngIf="shouldShowComponent('features')" class="step-content">
          <app-feature-list></app-feature-list>
        </div>

        <!-- SWOT Analysis Component (Placeholder) -->
        <div *ngIf="shouldShowComponent('swot')" class="step-content">
          <app-swot-analysis></app-swot-analysis>
        </div>

        <!-- Product Roadmap Component (Placeholder) -->
        <div *ngIf="shouldShowComponent('roadmap')" class="step-content">
          <app-product-roadmap></app-product-roadmap>
        </div>
      </div>
    </div>
  </awe-split-screen>

  <!-- Regular View (when split screen is not active) -->

  <!-- <div class="content-container" *ngIf="!showSplitScreen"> -->
    <!-- Understanding Component -->
    <!-- <div *ngIf="shouldShowComponent('understanding')" class="step-content">
      <app-understanding></app-understanding>
    </div> -->

    <!-- User Persona Component -->
    <!-- <div *ngIf="shouldShowPersonaList()" class="step-content">
      <app-user-persona
        (personaSelected)="showPersonaDetailsView($event)"
      ></app-user-persona>
    </div> -->

    <!-- Persona Details Component -->
    <!-- <div *ngIf="shouldShowPersonaDetails()" class="step-content">
      <app-persona-details
        (backToList)="hidePersonaDetailsView()"
      ></app-persona-details>
    </div> -->

    <!-- SWOT Analysis Component (Placeholder) -->
    <!-- <div *ngIf="shouldShowComponent('swot')" class="step-content">
      <app-swot-analysis></app-swot-analysis>
    </div> -->

    <!-- Feature List Component -->
    <!-- <div *ngIf="shouldShowComponent('features')" class="step-content">
      <app-feature-list></app-feature-list>
    </div> -->

    <!-- Product Roadmap Component (Placeholder) -->
    <!-- <div *ngIf="shouldShowComponent('roadmap')" class="step-content">
      <app-product-roadmap></app-product-roadmap>
    </div> -->
  <!-- </div> -->

  <!-- Navigation Buttons - Commented out since split screen is always visible -->
  <!-- <div
    class="ai-assistant-prompt"
    *ngIf="!showSplitScreen"
    [@promptAnimation]="promptState"
  >
    <div
      class="ai-assistant-icon"
      (click)="toggleSplitScreen()"
      (mouseenter)="onRoboBallHover()"
      (mouseleave)="onRoboBallLeave()"
      [class.chat-open]="showSplitScreen"
      [@roboBallAnimation]="roboBallState"
    >
      <img [src]="roboBallIcon" alt="AI Assistant" />
    </div>
    <p class="ai-assistant-text">Hi there - need a hand?</p>
  </div> -->

  <div class="finish-btn" *ngIf="currentStep?.component === 'roadmap'">
    <button (click)="finish()">Finish</button>
  </div>
</div>

<!-- Loading states are now handled by the stepper component -->

<!-- Navigation Confirmation Modal -->
<app-navigation-confirmation-modal
  [isVisible]="(showNavigationModal$ | async) || false"
  title="Exit Session"
  message="Are you really sure you want to exit the session?"
  cancelButtonText="Cancel"
  confirmButtonText="OK"
  (onCancel)="onNavigationCancel()"
  (onConfirm)="onNavigationConfirm()"
  (onBackdropClick)="onNavigationCancel()"
>
</app-navigation-confirmation-modal>
