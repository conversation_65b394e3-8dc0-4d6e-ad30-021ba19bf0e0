import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Inject, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Observable, Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  trigger,
  state,
  style,
  transition,
  animate,
} from '@angular/animations';

import { BrainstormerStepperComponent } from '../components/stepper/stepper.component';
import { HeadingComponent } from '@awe/play-comp-library';
import { IconsComponent } from '../../../shared/components/icons/icons.component';
import {
  StepperService,
  StepperStep,
} from '../../shared/services/stepper-service/stepper.service';

// Import all page components

import { UserPersonaComponent } from '../pages/user-persona/user-persona.component';
import { PersonaDetailsComponent } from '../pages/persona-details/persona-details.component';
import { FeatureListComponent } from '../pages/feature-list/feature-list.component';
import { SwotAnalysisComponent } from '../pages/swot-analysis/swot-analysis.component';
import { ProductRoadmapComponent } from '../pages/product-roadmap/product-roadmap.component';
import { SplitScreenComponent } from '../components/split-screen/split-screen.component';
import { ChatPanelComponent } from '../components/chat-panel/chat-panel.component';
import { RightPanelHeaderComponent } from '../components/right-panel-header/right-panel-header.component';
import { UnderstandingComponent } from '../pages/understanding/understanding.component';
import { Router } from '@angular/router';
import { ProductPipelineService } from '../services/product-pipeline.service';
// LoadingComponent removed - loading states now handled by stepper
import { AppStateService } from '../../shared/services/app-state.service';
import { NavigationGuardService } from '../../shared/services/navigation-guard.service';
import { NavigationConfirmationModalComponent } from '../../shared/components/navigation-confirmation-modal/navigation-confirmation-modal.component';
import { SplitScreenService } from '../services/split-screen.service';

@Component({
  selector: 'app-brainstorming',
  imports: [
    CommonModule,
    BrainstormerStepperComponent,
    UnderstandingComponent,
    UserPersonaComponent,
    PersonaDetailsComponent,
    FeatureListComponent,
    SwotAnalysisComponent,
    ProductRoadmapComponent,
    IconsComponent,
    HeadingComponent,
    SplitScreenComponent,
    ChatPanelComponent,
    RightPanelHeaderComponent,
    NavigationConfirmationModalComponent,
  ],
  templateUrl: './brainstorming.component.html',
  styleUrl: './brainstorming.component.scss',
  animations: [
    trigger('roboBallAnimation', [
      state(
        'idle',
        style({
          transform: 'scale(1) rotate(0deg)',
          boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.15)',
        }),
      ),
      state(
        'hover',
        style({
          transform: 'scale(1.1) rotate(5deg)',
          boxShadow: '0px 8px 25px rgba(74, 144, 226, 0.3)',
        }),
      ),
      state(
        'clicked',
        style({
          transform: 'scale(0.95) rotate(-5deg)',
          boxShadow: '0px 4px 15px rgba(74, 144, 226, 0.5)',
        }),
      ),
      transition(
        'idle => hover',
        animate('0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)'),
      ),
      transition(
        'hover => idle',
        animate('0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)'),
      ),
      transition('* => clicked', animate('0.15s ease-in')),
      transition('clicked => *', animate('0.3s ease-out')),
    ]),
    trigger('promptAnimation', [
      state(
        'hidden',
        style({
          opacity: 0,
          transform: 'translateX(-20px) scale(0.9)',
        }),
      ),
      state(
        'visible',
        style({
          opacity: 1,
          transform: 'translateX(0) scale(1)',
        }),
      ),
      transition(
        'hidden => visible',
        animate('0.4s 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94)'),
      ),
      transition('visible => hidden', animate('0.2s ease-in')),
    ]),
  ],
})
export class BrainstormingComponent implements OnInit, OnDestroy {
  roboBallIcon: string = ' icons/robo_ball.svg';
  chevronLeftIcon: string = 'assets/icons/awe_chevron_left.svg';
  chevronRightIcon: string = 'assets/icons/awe_chevron_right.svg';

  // State observables
  currentStep$: Observable<StepperStep | null>;
  currentStepIndex$: Observable<number>;
  canGoNext$: Observable<boolean>;
  canGoPrevious$: Observable<boolean>;
  isLoading$: Observable<boolean>;
  loadingMessage$: Observable<string | null>;
  hasErrors$: Observable<boolean>;
  showSplitScreen$: Observable<boolean>;

  // Navigation confirmation modal
  showNavigationModal$: Observable<boolean>;

  // Local state
  currentStep: StepperStep | null = null;
  currentStepIndex: number = 0;

  // Header navigation loading state
  isHeaderLoading: boolean = false;
  headerLoadingMessage: string = '';

  // Loading states are now handled by the stepper component

  // State for left panel toggle - now managed by service
  leftPanelWidth: number = 25; // Default width for left panel
  showSplitScreen = true; // Managed by SplitScreenService
  isDarkMode = false;

  // Track if any edit modal is open across all components
  isAnyEditModalOpen = false;

  // Store the callback reference for cleanup
  private stateChangeCallback = (isOpen: boolean) => {
    console.log('📢 Brainstorming component received state change notification:', isOpen);
    this.showSplitScreen = isOpen;
    // Trigger change detection to update the UI
    this.cdr.detectChanges();
  };

  // Store the edit modal state callback reference for cleanup
  private editModalStateCallback = (isModalOpen: boolean) => {
    console.log('📢 Brainstorming component received edit modal state notification:', isModalOpen);
    this.isAnyEditModalOpen = isModalOpen;
    // Trigger change detection to update the UI
    this.cdr.detectChanges();
  };

  // Animation states
  roboBallState = 'idle';
  promptState = 'visible';

  // Persona details state
  showPersonaDetails = false;
  selectedPersonaId: string | null = null;

  // Error states (loading now handled by stepper)
  stepError: string | null = null;

  private subscriptions: Subscription[] = [];

  constructor(
    public stepperService: StepperService, // Made public for template access
    private router: Router,
    private pipelineService: ProductPipelineService,
    private appStateService: AppStateService,
    @Inject(NavigationGuardService) private navigationGuardService: NavigationGuardService,
    private splitScreenService: SplitScreenService,
    private cdr: ChangeDetectorRef
  ) {
    // Initialize state observables
    this.currentStep$ = this.stepperService.currentStep$;
    this.currentStepIndex$ = this.stepperService.currentStepIndex$;
    this.canGoNext$ = this.appStateService.navigationState$.pipe(
      map(state => state.canGoNext)
    );
    this.canGoPrevious$ = this.appStateService.navigationState$.pipe(
      map(state => state.canGoPrevious)
    );
    this.isLoading$ = this.appStateService.selectIsLoading();
    this.loadingMessage$ = this.appStateService.loadingState$.pipe(
      map(state => state.loadingMessage)
    );
    this.hasErrors$ = this.appStateService.selectHasErrors();
    this.showSplitScreen$ = this.appStateService.uiState$.pipe(
      map(state => state.showSplitScreen)
    );

    // Navigation confirmation modal
    this.showNavigationModal$ = this.navigationGuardService.showConfirmationModal$;
  }

  ngOnInit(): void {
    // Initialize split screen state from service
    this.showSplitScreen = this.splitScreenService.isOpen();
    console.log('🔄 Split screen initialized from service:', this.showSplitScreen);

    // Initialize edit modal state from service
    this.isAnyEditModalOpen = this.splitScreenService.isAnyEditModalOpen();
    console.log('🔄 Edit modal state initialized from service:', this.isAnyEditModalOpen);

    // Register for state change notifications from the service
    this.splitScreenService.onStateChange(this.stateChangeCallback);

    // Register for edit modal state change notifications from the service
    this.splitScreenService.onEditModalStateChange(this.editModalStateCallback);

    // Initialize from stored data if available
    this.initializeFromStoredData();

    // Subscribe to current step changes
    this.subscriptions.push(
      this.stepperService.currentStep$.subscribe((step) => {
        this.currentStep = step;
      }),
    );

    // Subscribe to current step index changes
    this.subscriptions.push(
      this.stepperService.currentStepIndex$.subscribe((index) => {
        this.currentStepIndex = index;
      }),
    );

    // Subscribe to pipeline state changes to handle API responses
    this.subscriptions.push(
      this.appStateService.pipelineState$.subscribe((pipelineState) => {
        // When market research API completes, update stepper to show Understanding step
        if (pipelineState.current_step === 'market_research' &&
            pipelineState.data &&
            Object.keys(pipelineState.data).length > 0) {
          console.log('🎯 Market research completed, updating stepper to Understanding step');
          this.stepperService.updateStepFromApiResponse('market_research');
        }
      })
    );

    // Loading state blocking removed to allow continuous step progression
  }

  

  finish() {
    // Use absolute path to ensure correct navigation
    this.router.navigate(['brainstormer/summary'], {
      relativeTo: this.router.routerState.root.firstChild?.firstChild,
    });
  }

  /**
   * Initialize stepper state from current BehaviorSubject state
   */
  private initializeFromStoredData(): void {
    if (this.appStateService.hasStoredData()) {
      const currentState = this.appStateService.pipelineState;
      console.log('🔄 Initializing from current BehaviorSubject state:', currentState);

      // Restore stepper state from current data
      this.stepperService.restoreFromStoredData({
        ...currentState.data,
        current_step: currentState.current_step
      });

      console.log('✅ Successfully initialized from BehaviorSubject state');
    }
  }

  /**
   * Clear stored data and reset stepper (useful for starting new project)
   */
  clearStoredDataAndReset(): void {
    this.appStateService.resetState();
    console.log('🗑️ Cleared stored data and reset centralized state');
  }

  /**
   * Handle navigation confirmation modal cancel
   */
  onNavigationCancel(): void {
    this.navigationGuardService.cancelNavigation();
  }

  /**
   * Handle navigation confirmation modal confirm
   */
  onNavigationConfirm(): void {
    this.navigationGuardService.confirmNavigation();
  }
  ngOnDestroy(): void {
    // Clean up subscriptions
    this.subscriptions.forEach((sub) => sub.unsubscribe());

    // Clean up split screen service callback
    this.splitScreenService.removeStateChangeCallback(this.stateChangeCallback);

    // Clean up edit modal state service callback
    this.splitScreenService.removeEditModalStateCallback(this.editModalStateCallback);

    console.log('🧹 BrainstormingComponent destroyed, subscriptions and callbacks cleaned up');
  }

  onStepChanged(event: { step: StepperStep; index: number }): void {
    // Handle step change if needed
    console.log('Step changed:', event);
  }

  // Loading messages are now handled by the stepper component

  nextStep(): void {
    // Allow continuous step progression - blocking removed
    if (this.stepperService.canGoNext()) {
      const nextStepIndex = this.currentStepIndex + 1;
      const nextStep = this.stepperService.getStepByIndex(nextStepIndex);

      if (nextStep) {
        // Clear previous errors
        this.stepError = null;

        // Check if the next step already has data (smart navigation)
        const shouldCallApi = this.stepperService.shouldCallApiForStep(nextStep.id);

        if (shouldCallApi) {
          // Get the API step that should be called for the current stepper step
          const pipelineCurrentStep = this.stepperService.getNextApiStepForCurrentStep();

          if (pipelineCurrentStep) {
            // Start header loading state
            this.isHeaderLoading = true;
            this.headerLoadingMessage = `Loading ${nextStep.label}...`;
            console.log(`🔄 Calling API for step: ${pipelineCurrentStep}`);

            // Call pipeline API for the next step
            this.pipelineService.progressToStep(pipelineCurrentStep).subscribe({
              next: (response) => {
                console.log(`✅ Step ${pipelineCurrentStep} completed:`, response);

                // Update stepper based on API response
                this.stepperService.updateStepFromApiResponse(response.step as any);

                // End header loading state
                this.isHeaderLoading = false;
                this.headerLoadingMessage = '';
              },
              error: (error) => {
                console.error(
                  `❌ Error progressing to step ${pipelineCurrentStep}:`,
                  error,
                );
                this.stepError = `Failed to progress to ${nextStep.label}. Please try again.`;

                // End header loading state on error
                this.isHeaderLoading = false;
                this.headerLoadingMessage = '';
              },
            });
          } else {
            // For steps that don't require API calls, just move forward
            this.stepperService.nextStep();
            this.stepperService.markStepAsCompleted(this.currentStepIndex - 1);
          }
        } else {
          // Step already has data, just navigate without API call
          console.log(`🚀 Step ${nextStep.id} already has data, navigating without API call`);
          this.stepperService.nextStep();
        }
      }
    }
  }

  previousStep(): void {
    this.stepperService.previousStep();
  }

  // Navigation state now handled directly by stepper service in template

  toggleLeftPanel(): void {
    // Implement left panel toggle logic
  }

  // Helper method to get current component name for rendering
  getCurrentComponent(): string {
    return this.currentStep?.component || 'understanding';
  }

  // Helper method to check if a specific component should be shown
  shouldShowComponent(componentName: string): boolean {
    return this.getCurrentComponent() === componentName;
  }

  openSplitScreen() {
    console.log('🔓 Opening split screen via service');
    this.splitScreenService.openSplitScreen();
    // State will be updated automatically via callback
    this.roboBallState = 'clicked';

    setTimeout(() => {
      this.roboBallState = 'idle';
    }, 200);
  }

  // Split screen toggle functionality using SplitScreenService
  toggleSplitScreen() {
    console.log('🔄 Toggling split screen via service');
    this.splitScreenService.toggleSplitScreen();
    // State will be updated automatically via callback
  }

  closeSplitScreen() {
    console.log('🔒 Closing split screen via service');
    this.splitScreenService.closeSplitScreen();
    // State will be updated automatically via callback
    // this.roboBallState = 'idle';
  }

  /**
   * Clear step error message
   */
  clearStepError(): void {
    this.stepError = null;
  }

  /**
   * Handle split screen close event
   */
  // onSplitScreenClose(): void {
  //   console.log('🔒 Split screen closed event');
  //   this.roboBallState = 'idle';
  // }

  /**
   * Handle split screen open event
   */
  // onSplitScreenOpen(): void {
  //   console.log('🔓 Split screen opened event');
  //   this.roboBallState = 'idle';
  // }

  onRoboBallHover() {
    if (this.roboBallState === 'idle') {
      this.roboBallState = 'hover';
    }
  }

  onRoboBallLeave() {
    if (this.roboBallState === 'hover') {
      this.roboBallState = 'idle';
    }
  }

  toggleTheme() {
    this.isDarkMode = !this.isDarkMode;
  }

  // --- Persona Details Methods ---
  showPersonaDetailsView(personaId: string): void {
    this.selectedPersonaId = personaId;
    this.showPersonaDetails = true;
  }

  hidePersonaDetailsView(): void {
    this.showPersonaDetails = false;
    this.selectedPersonaId = null;
  }

  // Helper method to check if persona details should be shown
  shouldShowPersonaDetails(): boolean {
    return this.shouldShowComponent('persona') && this.showPersonaDetails;
  }

  // Helper method to check if persona list should be shown
  shouldShowPersonaList(): boolean {
    return this.shouldShowComponent('persona') && !this.showPersonaDetails;
  }
}
