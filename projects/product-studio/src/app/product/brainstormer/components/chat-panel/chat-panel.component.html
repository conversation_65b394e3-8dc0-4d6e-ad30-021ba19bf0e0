<div class="chat-panel">
  <!-- Chat <PERSON>er -->
  <div class="chat-header">
    <!-- <div class="status-indicator"></div> -->
   
  </div>

  <!-- Chat Content Area (Messages) -->
  <div class="chat-content" #chatContent>
    <!-- Welcome message (only show when no messages) -->
    <div class="welcome-message" *ngIf="(messages$ | async)?.length === 0">
      <p>👋 Hi! I'm your AI assistant. How can I help you with your product brainstorming today?</p>
    </div>

    <!-- Chat messages -->
    <div class="messages-container">
      <div
        *ngFor="let message of messages$ | async"
        class="message-wrapper"
        [ngClass]="{
          'user-message': message.sender === 'user',
          'ai-message': message.sender === 'ai',
          'error-message': message.responseType === 'error',
          'clarification-message': message.responseType === 'clarification'
        }"
      >
        <div class="message-bubble">
          <!-- Response type indicator for AI messages -->
          <div class="response-type-indicator" *ngIf="message.sender === 'ai' && message.responseType">
            <span class="response-type-badge" [ngClass]="message.responseType">
              {{ getResponseTypeLabel(message.responseType) }}
            </span>
          </div>
          <p>{{ message.content }}</p>
          <span class="message-time">{{ message.timestamp | date:'short' }}</span>
        </div>
      </div>

      <!-- AI typing indicator -->
      <div class="message-wrapper ai-message" *ngIf="isAiTyping$ | async">
        <div class="message-bubble typing-indicator">
          <div class="typing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Chat Input Container (Fixed at bottom) -->
  <div class="chat-input-container">
    <!-- <div class="ai-assistant-icon" (click)="onCloseSplitScreen()">
      <img [src]="roboBallIcon" alt="AI Assistant" />
    </div> -->

    <awe-input
      #messageInput
      type="text"
      variant="fluid"
      class="message-input position-relative right-0 buttom-0 d-flex flex-grow-1 justify-content-center align-items-center"
      (keyup.enter)="sendMessage($event)"
      (input)="onMessageInput($event)"
      (iconClicked)="sendMessage($event)"
      placeholder="Type your prompt here..."
      [icons]="['awe_send']"
      iconColor="action"
      [disabled]="(isAiTyping$ | async) || false"
      [loading]="(isLoading$ | async) || false"
      loadingType="spinner"
    >
    </awe-input>
  </div>
</div>
