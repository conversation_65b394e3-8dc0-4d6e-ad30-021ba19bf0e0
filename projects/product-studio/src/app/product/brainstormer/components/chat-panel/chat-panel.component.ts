import { Component, ViewChild, ElementRef, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { InputComponent } from '@awe/play-comp-library';
import { ProductPipelineService } from '../../services/product-pipeline.service';
import { ChatResponse } from '../../interfaces/pipeline-api.interface';
import { AppStateService } from '../../../shared/services/app-state.service';
import { ChatMessage } from '../../../shared/interfaces/app-state.interface';
import { Observable, Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import { SplitScreenService } from '../../services/split-screen.service';



@Component({
  selector: 'app-chat-panel',
  imports: [CommonModule, InputComponent],
  templateUrl: './chat-panel.component.html',
  styleUrl: './chat-panel.component.scss'
})
export class ChatPanelComponent implements OnInit, OnD<PERSON>roy {
  // State observables
  messages$: Observable<ChatMessage[]>;
  isAiTyping$: Observable<boolean>;
  isLoading$: Observable<boolean>;
  errorMessage$: Observable<string | null>;

  // Local properties
  currentMessage: string = '';
  roboBallIcon: string = 'icons/robo_ball.svg';
  private subscriptions: Subscription[] = [];

  @ViewChild('chatContent') chatContent!: ElementRef;
  @ViewChild('messageInput') messageInput!: ElementRef;

  // Output event for closing split screen
  @Output() closeSplitScreen = new EventEmitter<void>();

  constructor(
    private pipelineService: ProductPipelineService,
    private appStateService: AppStateService,
    private splitScreenService: SplitScreenService
  ) {
    // Initialize state observables
    this.messages$ = this.appStateService.selectChatMessages();
    this.isAiTyping$ = this.appStateService.chatState$.pipe(
      map(state => state.isAiTyping)
    );
    this.isLoading$ = this.appStateService.chatState$.pipe(
      map(state => state.isLoading)
    );
    this.errorMessage$ = this.appStateService.chatState$.pipe(
      map(state => state.errorMessage)
    );
  }

  ngOnInit(): void {
    // Subscribe to messages for auto-scrolling
    this.subscriptions.push(
      this.messages$.subscribe(() => {
        setTimeout(() => this.scrollToBottom(), 100);
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }



  sendMessage(event?: any) {
    const inputElement = event?.target as HTMLInputElement;
    const message = inputElement?.value?.trim() || this.currentMessage?.trim();

    if (!message) {
      return;
    }

    // Clear input
    if (inputElement) {
      inputElement.value = '';
    }
    this.currentMessage = '';

    // Send message through pipeline service (which handles state updates)
    this.pipelineService.sendChatMessage(message).subscribe({
      next: (response) => {
        console.log('✅ Chat message sent successfully:', response);
      },
      error: (error) => {
        console.error('❌ Failed to send chat message:', error);
      }
    });
  }



  private scrollToBottom(): void {
    if (this.chatContent) {
      const element = this.chatContent.nativeElement;
      element.scrollTop = element.scrollHeight;
    }
  }

  // Method to handle input changes (for two-way binding if needed)
  onMessageInput(event: any): void {
    this.currentMessage = event.target.value;
  }

  // Method to handle close split screen
  onCloseSplitScreen(): void {
    console.log('🔒 Chat panel requesting split screen close');
    this.splitScreenService.closeSplitScreen();
    this.closeSplitScreen.emit();
  }

  /**
   * Get display label for response type
   */
  getResponseTypeLabel(responseType: string): string {
    switch (responseType) {
      case 'clarification':
        return '❓ Clarification';
      case 'answer':
        return '✅ Answer';
      case 'error':
        return '❌ Error';
      default:
        return '💬 Response';
    }
  }
}
