// gantt-chart.component.ts
import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { HeaderComponent, HeadingComponent } from '@awe/play-comp-library';

export interface GanttTask {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
  color: string;
  quarter: number;
}

export interface GanttQuarter {
  quarter: number;
  year: number;
  months: string[];
}

@Component({
  selector: 'app-gantt-chart',
  standalone: true,
  imports: [CommonModule, HeadingComponent],
  templateUrl: './gantt-chart.component.html',
  styleUrl: './gantt-chart.component.scss',
})
export class GanttChartComponent implements OnInit {
  @Input() tasks: GanttTask[] = [];
  @Input() year: number = 2025;

  quarters: GanttQuarter[] = [];
  months: string[] = [
    'JAN',
    'FEB',
    'MAR',
    'APR',
    'MAY',
    'JUN',
    'JUL',
    'AUG',
    'SEP',
    'OCT',
    'NOV',
    'DEC',
  ];

  // Add these properties to your component class
  showTrackingLine = false;
  trackingLinePosition = '0%';
  trackingDate = '';

  constructor() {}

  ngOnInit(): void {
    this.initializeQuarters();
    if (this.tasks.length === 0) {
      // this.loadSampleData();
    }
  }

  initializeQuarters(): void {
    this.quarters = [
      { quarter: 1, year: this.year, months: ['JAN', 'FEB', 'MAR'] },
      { quarter: 2, year: this.year, months: ['APR', 'MAY', 'JUN'] },
      { quarter: 3, year: this.year, months: ['JUL', 'AUG', 'SEP'] },
      { quarter: 5, year: this.year, months: ['OCT', 'NOV', 'DEC'] },
    ];
  }

  // loadSampleData(): void {
  //   this.tasks = [
  //     {
  //       id: '1',
  //       name: 'Smart Nap Timer',
  //       startDate: new Date(2025, 0, 15), // Jan 15
  //       endDate: new Date(2025, 2, 15), // Mar 15
  //       color: '#10b981',
  //       quarter: 1,
  //     },
  //     {
  //       id: '2',
  //       name: 'Nap Stats Dashboard',
  //       startDate: new Date(2025, 1, 1), // Feb 1
  //       endDate: new Date(2025, 3, 30), // Apr 30
  //       color: '#f59e0b',
  //       quarter: 1,
  //     },
  //     {
  //       id: '3',
  //       name: 'Gentle Wake System',
  //       startDate: new Date(2025, 3, 1), // Apr 1
  //       endDate: new Date(2025, 5, 30), // Jun 30
  //       color: '#10b981',
  //       quarter: 2,
  //     },
  //     {
  //       id: '4',
  //       name: 'Mood-to-Nap Match',
  //       startDate: new Date(2025, 5, 15), // Jun 15
  //       endDate: new Date(2025, 8, 30), // Sep 30
  //       color: '#f59e0b',
  //       quarter: 3,
  //     },
  //     {
  //       id: '5',
  //       name: 'Nap Streak Gamification',
  //       startDate: new Date(2025, 9, 1), // Oct 1
  //       endDate: new Date(2025, 11, 31), // Dec 31
  //       color: '#ec4899',
  //       quarter: 5,
  //     },
  //   ];
  // }

  getTaskPosition(task: GanttTask): { left: string; width: string } {
    const yearStart = new Date(this.year, 0, 1);
    const yearEnd = new Date(this.year, 11, 31);
    const totalDays = this.getDaysBetween(yearStart, yearEnd);

    const taskStartDays = this.getDaysBetween(yearStart, task.startDate);
    const taskDuration = this.getDaysBetween(task.startDate, task.endDate);

    const leftPercentage = (taskStartDays / totalDays) * 100;
    const widthPercentage = (taskDuration / totalDays) * 100;

    return {
      left: `${leftPercentage}%`,
      width: `${widthPercentage}%`,
    };
  }

  private getDaysBetween(startDate: Date, endDate: Date): number {
    const timeDiff = endDate.getTime() - startDate.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  }

  getQuarterWidth(): string {
    return `${100 / this.quarters.length}%`;
  }

  getMonthWidth(): string {
    return `${100 / 12}%`;
  }

  getCurrentMonth(): string {
    const currentDate = new Date();
    return this.months[currentDate.getMonth()];
  }

  onTimelineMouseMove(event: MouseEvent): void {
    const timeline = event.currentTarget as HTMLElement;
    const rect = timeline.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const percentage = (x / rect.width) * 100;

    this.trackingLinePosition = `${percentage}%`;
    this.trackingDate = this.getDateAtPosition(percentage);
    this.showTrackingLine = true;
  }

  onTimelineMouseLeave(): void {
    this.showTrackingLine = false;
  }

  private getDateAtPosition(percentage: number): string {
    const yearStart = new Date(this.year, 0, 1);
    const yearEnd = new Date(this.year, 11, 31);
    const totalDays = this.getDaysBetween(yearStart, yearEnd);
    const dayAtPosition = Math.floor((percentage / 100) * totalDays);

    const targetDate = new Date(yearStart);
    targetDate.setDate(targetDate.getDate() + dayAtPosition);

    return targetDate.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  }

  isCurrentMonth(month: string): boolean {
    return month === this.getCurrentMonth();
  }

  // Returns true if the bar is wide enough to show the name (e.g., >80px)
  // shouldShowTaskName(task: GanttTask): boolean {
  //   const { width } = this.getTaskPosition(task);
  //   // The parent container is 100% width, so 1% = containerWidth/100 px
  //   // We'll assume the container is at least 800px wide for this logic
  //   const containerWidth = 800;
  //   const barWidthPx = (parseFloat(width) / 100) * containerWidth;
  //   return barWidthPx > 80;
  // }

  getTaskTooltip(task: GanttTask): string {
    const start = task.startDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
    const end = task.endDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
    return `${task.name}\n${start} - ${end}`;
  }
  getMonthDividerPositions(): string[] {
    const positions: string[] = [];
    const monthWidth = 100 / 12; // 8.333%

    // Create 11 divider lines (between 12 months)
    for (let i = 1; i < 12; i++) {
      positions.push(`${i * monthWidth}%`);
    }
    return positions;
  }
}
