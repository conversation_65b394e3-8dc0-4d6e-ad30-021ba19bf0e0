.split-screen-overlay {
  backdrop-filter: blur(2px);
  pointer-events: none;
  transition: backdrop-filter 0.3s ease;
  z-index: 999;
}

.split-screen-overlay.active {
  pointer-events: all;
  backdrop-filter: blur(4px);
}
@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8) rotate(-90deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

.split-container {
  display: flex;
  overflow: hidden;
}

.left-panel {
  // height: 100%;
  border-right: 1px solid;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.left-panel.light {
  background: #f8fafc;
  border-color: #e2e8f0;
}

.left-panel.dark {
  background: #1a202c;
  border-color: #2d3748;
}

.right-panel {
  height: 82vh;
  overflow-x: scroll;
  scrollbar-width: none;
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  border: 8px solid #fff;
  // background: #f5f5f7;
  background-image: url('/svgs/right-panel-bg.svg');
  box-shadow:
    0px 4px 8px -2px rgba(16, 24, 40, 0.1),
    0px 2px 4px -2px rgba(16, 24, 40, 0.06);
}

.right-panel.light {
  // background: white;
}

.right-panel.dark {
  background: #2d3748;
}

.resize-handle {
  width: 8px;
  height: 100%;
  cursor: col-resize;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.resize-indicator {
  width: 2px;
  height: 40px;
  border-radius: 1px;
  opacity: 0.6;
}

/* Responsive design */
@media (max-width: 768px) {
  .split-container {
    flex-direction: column;
  }

  .left-panel {
    width: 100% !important;
    height: 40%;
    border-right: none;
    border-bottom: 1px solid;
  }

  .right-panel {
    width: 100% !important;
    height: 60%;
  }

  .resize-handle {
    width: 100%;
    height: 8px;
    cursor: row-resize;
  }

  .resize-indicator {
    width: 40px;
    height: 2px;
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: #4a5568;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: #718096;
}
