// split-screen.component.ts
import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { trigger, state, style, transition, animate, keyframes, query, stagger } from '@angular/animations';

@Component({
  selector: 'awe-split-screen',
  standalone: true,
  imports: [CommonModule],
   templateUrl: './split-screen.component.html',
  styleUrl: './split-screen.component.scss',
  animations: [
    trigger('slideAnimation', [
      state('closed', style({
        transform: 'scale(0) translate(-100%, 100%)',
        transformOrigin: 'bottom left',
        opacity: 0,
        visibility: 'hidden'
      })),
      state('open', style({
        transform: 'scale(1) translate(0, 0)',
        transformOrigin: 'bottom left',
        opacity: 1,
        visibility: 'visible'
      })),
      transition('closed => open', [
        style({ visibility: 'visible' }),
        animate('0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)', keyframes([
          style({ transform: 'scale(0.1) translate(-80%, 80%)', transformOrigin: 'bottom left', opacity: 0, offset: 0 }),
          style({ transform: 'scale(0.8) translate(-10%, 10%)', transformOrigin: 'bottom left', opacity: 0.8, offset: 0.7 }),
          style({ transform: 'scale(1) translate(0, 0)', transformOrigin: 'bottom left', opacity: 1, offset: 1 })
        ]))
      ]),
      transition('open => closed', [
        animate('0.4s cubic-bezier(0.55, 0.06, 0.68, 0.19)', keyframes([
          style({ transform: 'scale(1) translate(0, 0)', transformOrigin: 'bottom left', opacity: 1, offset: 0 }),
          style({ transform: 'scale(0.8) translate(-10%, 10%)', transformOrigin: 'bottom left', opacity: 0.8, offset: 0.3 }),
          style({ transform: 'scale(0.1) translate(-80%, 80%)', transformOrigin: 'bottom left', opacity: 0, offset: 1 })
        ])),
        style({ visibility: 'hidden' })
      ])
    ]),
    trigger('panelAnimation', [
      transition(':enter', [
        style({ transform: 'scale(0.95)', opacity: 0 }),
        animate('0.3s 0.2s ease-out', style({ transform: 'scale(1)', opacity: 1 }))
      ])
    ]),
    trigger('backdropAnimation', [
      state('closed', style({ opacity: 0 })),
      state('open', style({ opacity: 1 })),
      transition('closed => open', animate('0.3s ease-out')),
      transition('open => closed', animate('0.2s ease-in'))
    ])
  ]
})
export class SplitScreenComponent implements OnInit, OnDestroy {
  @Input() leftPanelWidth: number = 10; // Default 35% width for left panel
  @Input() toggle: boolean = false;
  @Input() theme: 'light' | 'dark' = 'light';
  
  @Output() toggleChange = new EventEmitter<boolean>();
  @Output() onClose = new EventEmitter<void>();
  @Output() onOpen = new EventEmitter<void>();

  isOpen = false;
  private isResizing = false;
  private startX = 0;
  private startWidth = 0;
  
   chevronLeftIcon: string = 'assets/icons/awe_chevron_left.svg';
  chevronRightIcon: string = 'assets/icons/awe_chevron_right.svg';

  ngOnInit() {
    this.isOpen = this.toggle;
    
    // Listen for escape key to close
    document.addEventListener('keydown', this.handleEscapeKey.bind(this));
    document.addEventListener('mousemove', this.handleMouseMove.bind(this));
    document.addEventListener('mouseup', this.handleMouseUp.bind(this));
  }

  ngOnDestroy() {
    document.removeEventListener('keydown', this.handleEscapeKey.bind(this));
    document.removeEventListener('mousemove', this.handleMouseMove.bind(this));
    document.removeEventListener('mouseup', this.handleMouseUp.bind(this));
  }

  ngOnChanges() {
    if (this.toggle !== this.isOpen) {
      this.isOpen = this.toggle;
      if (this.isOpen) {
        this.onOpen.emit();
      }
    }
  }

  openSplitScreen() {
    this.isOpen = true;
    this.toggleChange.emit(true);
    this.onOpen.emit();
  }

  closeSplitScreen() {
    this.isOpen = false;
    this.toggleChange.emit(false);
    this.onClose.emit();
  }

  private handleEscapeKey(event: KeyboardEvent) {
    if (event.key === 'Escape' && this.isOpen) {
      this.closeSplitScreen();
    }
  }

  startResize(event: MouseEvent) {
    event.preventDefault();
    this.isResizing = true;
    this.startX = event.clientX;
    this.startWidth = this.leftPanelWidth;
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  }

  private handleMouseMove(event: MouseEvent) {
    if (!this.isResizing) return;

    const deltaX = event.clientX - this.startX;
    const containerWidth = window.innerWidth;
    const deltaPercent = (deltaX / containerWidth) * 100;
    
    let newWidth = this.startWidth + deltaPercent;
    
    // Constrain width between 20% and 80%
    newWidth = Math.max(20, Math.min(80, newWidth));
    
    this.leftPanelWidth = newWidth;
  }

  private handleMouseUp() {
    if (this.isResizing) {
      this.isResizing = false;
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    }
  }
}

