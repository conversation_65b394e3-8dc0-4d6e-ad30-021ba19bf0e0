.custom-stepper {
  width: 100%;
  margin-bottom: 32px;
  background: transparent;

  .stepper-container {
    width: 80%;
  }

  .stepper-header {
    width: 100%;
    padding: 20px 0;
  }

  .steps-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    width: 100%;
  }

  .step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    flex: 1;
    cursor: default;
    transition: all 0.3s ease;

    &.clickable {
      cursor: pointer;

      &:hover {
        .step-circle {
          transform: scale(1.1);
        }
      }
    }

    &.disabled {
      cursor: not-allowed;
      // opacity: 0.6;
    }

    &.loading {
      .step-circle {
        background: #6D6DF6;
        border: 2px solid transparent;
        background-clip: padding-box;
        color: #fff;
        animation: pulse-gradient 2s infinite;
      }
    }

    &:not(:last-child) {
      margin-right: 20px;
    }
  }

  .step-circle {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background-color: #413d3d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
    margin-bottom: 8px;
    overflow: hidden; // Hide parts of the loader that might stick out

    .check-icon {
      width: 32px;
      height: 32px;
      color: white;
    }
  }

  .step-label {
    text-align: center;
    max-width: 220px;

    .step-title {
      display: block;
      font-weight: 500;
      font-size: 14px;
      line-height: 1.2;
      margin-bottom: 4px;
      transition: color 0.3s ease;
    }

    .step-description {
      display: block;
      font-size: 12px;
      line-height: 1.3;
      opacity: 0.7;
    }
  }

  .step-connector {
    position: absolute;
    top: 24px; // Adjusted for new circle size
    left: 54%;
    right: -50%;
    height: 2px;
    background-color: #e0e0e0;
    transition: background 0.3s ease;
    z-index: 1;

    &.completed {
      background: #6D6DF6;
    }
  }

  // Step States
  .step-item {
    &.inactive {
      .step-circle {
        background-color: #f5f5f5;
        border: 2px solid #e0e0e0;
        color: #9e9e9e;
      }

      .step-title {
        color: #9e9e9e;
      }
    }

    &.active {
      .step-circle {
        background: #6D6DF6;
        border: 2px solid transparent;
        background-clip: padding-box;
        color: #fff;
        box-shadow: 0 0 0 4px rgba(109, 109, 246, 0.2);
      }

      .step-title {
        color: #000;
        font-weight: 600;
      }
    }

    &.completed {
      .step-circle {
        background: #6D6DF6;
        border: 2px solid transparent;
        background-clip: padding-box;
        color: #fff;
      }

      .step-title {
        color: #000;
        font-weight: 600;
      }
    }
  }

  // Progress Bar
  .progress-container {
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 12px;

    .progress-bar {
      flex: 1;
      height: 6px;
      background-color: #f5f5f5;
      border-radius: 3px;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: #6D6DF6;
        border-radius: 3px;
        transition: width 0.5s ease;
      }
    }

    .progress-text {
      font-size: 12px;
      font-weight: 500;
      color: #666;
      white-space: nowrap;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .custom-stepper {
    .steps-wrapper {
      flex-direction: column;
      gap: 20px;
    }

    .step-item {
      flex-direction: row;
      align-items: center;
      width: 100%;
      margin-right: 0 !important;

      .step-circle {
        margin-bottom: 0;
        margin-right: 12px;
        width: 36px; // Adjusted for responsive view
        height: 36px;
        font-size: 12px;

        .check-icon {
          width: 18px;
          height: 18px;
        }
      }

      .step-label {
        text-align: left;
        max-width: none;
        flex: 1;

        .step-title {
          font-size: 16px;
          margin-bottom: 2px;
        }

        .step-description {
          font-size: 14px;
        }
      }

      .step-connector {
        display: none;
      }
    }
  }
}


// --- NEWLY ADDED TRAFFIC LOADER STYLES ---

.traffic-loader {
  width: 24px;
  height: 24px;
  --c: radial-gradient(farthest-side, #ffffff 92%, #0000);
  background: var(--c) 50% 0, var(--c) 50% 100%, var(--c) 100% 50%, var(--c) 0 50%;
  background-size: 6px 6px;
  background-repeat: no-repeat;
  animation: s8 1s infinite linear;
  position: relative;
}

.traffic-loader::before {
  content: "";
  position: absolute;
  inset: 0;
  margin: 2px;
  background: repeating-conic-gradient(#0000 0 35deg, #ffffff 0 90deg);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 2px), #000 0);
  mask: radial-gradient(farthest-side, #0000 calc(100% - 2px), #000 0);
  border-radius: 50%;
}

@keyframes s8 {
  100% {
    transform: rotate(1turn);
  }
}

// Pulse animation for loading state
@keyframes pulse-gradient {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

// Traffic loader for stepper steps with gradient colors
.loading-spinner {
  width: 30px;
  height: 30px;
  --c: radial-gradient(farthest-side, #fff 92%, #0000);
  background: var(--c) 50% 0, var(--c) 50% 100%, var(--c) 100% 50%, var(--c) 0 50%;
  background-size: 8px 8px;
  background-repeat: no-repeat;
  animation: s8 1s infinite;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner::before {
  content: "";
  position: absolute;
  inset: 0;
  margin: 2px;
  background: repeating-conic-gradient(#0000 0 35deg, #f6f6f6 0 90deg);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 2px), #000 0);
  mask: radial-gradient(farthest-side, #0000 calc(100% - 2px), #000 0);
  border-radius: 50%;
}

// Step number styling
.step-number {
  font-size: 12px;
  font-weight: 600;
  color: #6C757D;

  &.inactive {
    color: #ADB5BD;
  }
}