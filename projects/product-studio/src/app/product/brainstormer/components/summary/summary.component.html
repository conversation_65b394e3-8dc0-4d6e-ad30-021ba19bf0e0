<div class="project-summary-container p-4 p-md-5" *ngIf="summaryData">
  <!-- Loading State -->
  <!-- <div *ngIf="summaryData?.isLoading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-3">Loading your brainstorming summary...</p>
  </div> -->

  <!-- Main Content -->
  <div *ngIf="!summaryData.isLoading">
    <!-- TOP SECTION: Title, Description, and Progress -->
    <div class="row mb-5">
      <div class="col-12 col-lg-12 col-md-12">
        <awe-heading variant="h5" type="bold" class="project-title">{{
          (projectName$ | async)
        }}</awe-heading>
        <awe-caption type="regular" class="project-description">{{
          summaryData?.description || 'Loading project description...'
        }}</awe-caption>
      </div>
    </div>
    <div class="row gap-3">
      <div class="col-12 d-flex align-items-center justify-content-between mt-4 mt-lg-0">
        <div class="progress-wrapper d-flex align-items-center">
          <awe-progress-bar
            [progress]="summaryData?.progress || 0"
            class="my-custom-bar"
          ></awe-progress-bar>

          <div class="ms-4">
            <awe-heading variant="s2" type="bold" class="progress-heading">{{
              summaryData.progress === 100
                ? "Congratulations! Session Complete"
                : "Great Progress!"
            }}</awe-heading>
            <awe-heading
              variant="s2"
              type="regular"
              class="progress-subheading"
              [innerHTML]="summaryData.contributionText"
            ></awe-heading>
          </div>
        </div>
        <button
          class="btn btn-primary export-btn"
          (click)="exportProject()"
          [disabled]="!summaryData.hasData"
        >
          Export
        </button>
      </div>
    </div>

    <!-- MAIN GRID OF CARDS -->
    <div class="row g-4 mt-4">
      <!-- Left Column -->
      <div class="col-12 col-lg-8 d-flex flex-column gap-4">
        <div class="row g-4">
          <!-- Understanding -->
          <div class="col-12 col-md-6 d-flex">
            <awe-card [applyBodyPadding]="true" [showFooter]="true">
              <div awe-card-header-content class="px-4 pt-4">
                <awe-heading variant="s1" type="bold"
                  >Understanding</awe-heading
                >
              </div>
              <div class="px-4">
                <awe-body-text
                  class="px-4"
                  *ngIf="hasStepData('understanding')"
                >
                  {{ summaryData.understandingText }}
                </awe-body-text>
                <awe-body-text
                  class="px-4 text-muted"
                  *ngIf="!hasStepData('understanding')"
                >
                  {{ getPlaceholderText("understanding") }}
                </awe-body-text>
              </div>
              <div awe-card-footer-content class="d-flex justify-content-end">
                <button (click)="redirectToUnderstanding()" class="btn-icon">
                  <awe-icons
                    iconName="awe_arrow_circle_right_outlined"
                  ></awe-icons>
                </button>
              </div>
            </awe-card>
          </div>

          <!-- UserPersona -->
          <div class="col-12 col-md-6 d-flex">
            <awe-card [applyBodyPadding]="true" [showFooter]="true">
              <div awe-card-header-content class="px-4 pt-3">
                <awe-heading variant="s1" type="bold">User Persona</awe-heading>
              </div>
              <div
                class="persona-list row g-3 px-4 pt-4"
                *ngIf="hasStepData('personas')"
              >
                <div
                  *ngFor="let persona of summaryData.personas"
                  class="persona-item col-5 col-lg-5 col-md-5 d-flex align-items-center justify-content-start"
                >
                  <img
                    [src]="persona.avatarUrl"
                    [alt]="persona.name"
                    class="avatar p-2"
                  />
                  <div>
                    <h3>{{ persona.name }}</h3>
                    <awe-caption
                      inputId="caption"
                      class="persona-role"
                      ariaLabel="caption"
                      >{{ persona.role }}</awe-caption
                    >
                  </div>
                </div>
              </div>
              <div class="px-4 pt-4" *ngIf="!hasStepData('personas')">
                <awe-body-text class="text-muted">
                  {{ getPlaceholderText("personas") }}
                </awe-body-text>
              </div>
              <div awe-card-footer-content class="d-flex justify-content-end">
                <button (click)="redirectToPersona()" class="btn-icon">
                  <awe-icons
                    iconName="awe_arrow_circle_right_outlined"
                  ></awe-icons>
                </button>
              </div>
            </awe-card>
          </div>
        </div>

        <div class="row g-4">
          <!-- Feature List -->
          <div class="col-12 col-md-6 d-flex">
            <awe-card [applyBodyPadding]="true" [showFooter]="true">
              <div awe-card-header-content class="px-4 pt-4">
                <awe-heading variant="s1" type="bold">Feature List</awe-heading>
              </div>
              <div
                class="feature-list d-flex justify-content-between flex-wrap row px-4 pt-4"
                *ngIf="hasStepData('features')"
              >
                <div
                  class="task-bar flex-row flex-wrap m-2 py-2 px-2"
                  *ngFor="let feature of summaryData.features"
                  [style.border-top]="`3px solid ${feature.color}`"
                >
                  <awe-caption
                    *ngIf="feature"
                    inputId="caption"
                    ariaLabel="caption"
                    [title]="feature.name"
                    >{{ feature.name }}</awe-caption
                  >
                </div>
              </div>
              <div class="px-4 pt-4" *ngIf="!hasStepData('features')">
                <awe-body-text class="text-muted">
                  {{ getPlaceholderText("features") }}
                </awe-body-text>
              </div>
              <div awe-card-footer-content class="d-flex justify-content-end">
                <button (click)="redirectToFeature()" class="btn-icon">
                  <awe-icons
                    iconName="awe_arrow_circle_right_outlined"
                  ></awe-icons>
                </button>
              </div>
            </awe-card>
          </div>

          <!-- SWOT Analysis -->
          <div class="col-12 col-md-6 d-flex">
            <awe-card [applyBodyPadding]="true" [showFooter]="true">
              <div awe-card-header-content class="px-4 pt-4">
                <awe-heading variant="s1" type="bold">
                  SWOT Analysis</awe-heading
                >
              </div>
              <div
                class="feature-list d-flex justify-content-between flex-wrap row px-4 pt-4"
                *ngIf="hasStepData('swot')"
              >
                <div
                  class="task-bar-swot m-2 py-2 px-4 d-flex justify-content-between flex wrap align-items-center"
                  *ngFor="let item of summaryData.swot"
                  [style.border-left]="`7px solid ${item.color}`"
                >
                  <ul
                    *ngIf="item.category"
                    class="p-0 m-0"
                    variant="s2"
                    type="bold"
                  >
                    <li
                      *ngFor="let point of item.points"
                      class="p-0 m-0"
                      [title]="point"
                    >
                      {{ point }}
                    </li>
                  </ul>
                </div>
              </div>
              <div class="px-4 pt-4" *ngIf="!hasStepData('swot')">
                <awe-body-text class="text-muted">
                  {{ getPlaceholderText("swot") }}
                </awe-body-text>
              </div>
              <div awe-card-footer-content class="d-flex justify-content-end">
                <button (click)="redirectToSwot()" class="btn-icon">
                  <awe-icons
                    iconName="awe_arrow_circle_right_outlined"
                  ></awe-icons>
                </button>
              </div>
            </awe-card>
          </div>
        </div>
      </div>

      <!-- Right Column -->
      <div class="col-12 col-lg-4 d-flex flex-column gap-4">
        <div class="col-12 d-flex">
          <awe-card [applyBodyPadding]="false" [showFooter]="true">
            <!-- Card Header -->
            <div awe-card-header-content class="p-4">
              <h4>ROADMAP</h4>
            </div>

            <!-- Card Body with Timeline -->
            <div class="p-4 pt-0">
              <div class="timeline-container" *ngIf="hasStepData('roadmap')">
                <!-- The main vertical line running down the center -->
                <div class="timeline-main-line"></div>

                <!-- Loop through each quarter -->
                <div
                  *ngFor="
                    let quarter of summaryData.timeline;
                    let isLast = last
                  "
                  class="timeline-item"
                >
                  <div class="timeline-content">
                    <h5 class="quarter-title">{{ quarter.quarter }}</h5>
                    <div class="quarter-tasks">
                      <div *ngFor="let task of quarter.items" class="task-item">
                        <awe-icons
                          [iconName]="task.icon"
                          iconSize="16px"
                        ></awe-icons>
                        <span [title]="task.label">{{ task.label }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div *ngIf="!hasStepData('roadmap')" class="text-center py-4">
                <awe-body-text class="text-muted">
                  {{ getPlaceholderText("roadmap") }}
                </awe-body-text>
              </div>
            </div>

            <!-- Card Footer -->
            <div awe-card-footer-content class="d-flex justify-content-end">
              <button (click)="redirectToRoadmap()" class="btn-icon">
                <awe-icons
                  iconName="awe_arrow_circle_right_outlined"
                ></awe-icons>
              </button>
            </div>
          </awe-card>
        </div>
      </div>
    </div>
  </div>
  <!-- Close main content div -->
</div>
