import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Observable, Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import { HeadingComponent, BodyTextComponent, CaptionComponent } from '@awe/play-comp-library';
import { IconsComponent } from '../../../../shared/components/icons/icons.component';
import { AweCardComponent } from '../awe-card/awe-card.component';
import { AweProgressBarComponent } from "../awe-progress-bar/awe-progress-bar.component";
import { SummaryService } from './summary.service';
import { SummaryData } from './summary.interfaces';
import { StepperService } from '../../../shared/services/stepper-service/stepper.service';
import { AppStateService } from '../../../shared/services/app-state.service';
@Component({
  selector: 'app-summary',
  imports: [
    CommonModule,
    AweCardComponent,
    IconsComponent,
    HeadingComponent,
    BodyTextComponent,
    AweProgressBarComponent,
    CaptionComponent,
],
  templateUrl: './summary.component.html',
  styleUrl: './summary.component.scss',
})
export class SummaryComponent implements OnInit, OnDestroy {
  // Real-time summary data from centralized state store
  summaryData$: Observable<SummaryData>;
  summaryData: SummaryData | null = null;

  // Direct observable for project name from pipeline state
  projectName$: Observable<string>;

  private subscriptions: Subscription[] = [];

  constructor(
    private summaryService: SummaryService,
    private stepperService: StepperService,
    private appStateService: AppStateService,
    private router: Router
  ) {
    this.summaryData$ = this.summaryService.summaryData$;

    // Create a direct observable for project name from pipeline state
    this.projectName$ = this.appStateService.pipelineState$.pipe(
      map(pipelineState => pipelineState.project_name || 'Untitled Project')
    );
  }

  ngOnInit(): void {
    console.log('🔄 SummaryComponent ngOnInit - subscribing to real data');

    // Subscribe to real-time summary data updates
    const summarySubscription = this.summaryData$.subscribe(data => {
      console.log('📊 Received summary data update:', data);
      this.summaryData = data;
    });

    this.subscriptions.push(summarySubscription);
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
    console.log('🧹 SummaryComponent destroyed, subscriptions cleaned up');
  }

  /**
   * Export project data functionality
   */
  exportProject(): void {
    if (this.summaryData && this.summaryData.hasData) {
      console.log('📤 Exporting project data:', this.summaryData);
      // TODO: Implement actual export functionality
      alert('Export functionality will be implemented soon!');
    } else {
      console.warn('⚠️ No data available to export');
      alert('Please complete some brainstorming steps before exporting.');
    }
  }

  /**
   * Check if specific step has data for conditional rendering
   */
  hasStepData(step: string): boolean {
    return this.summaryService.hasStepData(step);
  }

  /**
   * Get placeholder text for empty states
   */
  getPlaceholderText(step: string): string {
    switch (step) {
      case 'understanding':
        return 'Complete the Understanding step to see market insights here.';
      case 'personas':
        return 'Complete the User Persona step to see personas here.';
      case 'features':
        return 'Complete the Feature List step to see features here.';
      case 'swot':
        return 'Complete the SWOT Analysis step to see analysis here.';
      case 'roadmap':
        return 'Complete the Roadmap step to see timeline here.';
      default:
        return 'No data available yet.';
    }
  }

  /**
   * Navigation methods for summary card buttons
   */

  /**
   * Navigate to Understanding step
   */
  redirectToUnderstanding(): void {
    console.log('🧭 Navigating to Understanding step');
    this.navigateToBrainstormingStep('understanding');
  }

  /**
   * Navigate to User Persona step
   */
  redirectToPersona(): void {
    console.log('🧭 Navigating to User Persona step');
    this.navigateToBrainstormingStep('persona');
  }

  /**
   * Navigate to Feature List step
   */
  redirectToFeature(): void {
    console.log('🧭 Navigating to Feature List step');
    this.navigateToBrainstormingStep('features');
  }

  /**
   * Navigate to SWOT Analysis step
   */
  redirectToSwot(): void {
    console.log('🧭 Navigating to SWOT Analysis step');
    this.navigateToBrainstormingStep('swot');
  }

  /**
   * Navigate to Roadmap step
   */
  redirectToRoadmap(): void {
    console.log('🧭 Navigating to Roadmap step');
    this.navigateToBrainstormingStep('roadmap');
  }

  /**
   * Helper method to navigate to brainstorming steps
   * @param stepId - The step ID to navigate to
   */
  private navigateToBrainstormingStep(stepId: string): void {
    // First navigate to the brainstorming route if not already there
    const currentUrl = this.router.url;

    if (!currentUrl.includes('/brainstorming')) {
      // Navigate to brainstorming route first, then set the step
      this.router.navigate(['/brainstormer/brainstorming']).then(() => {
        // Use stepper service to navigate to specific step
        this.stepperService.goToStepById(stepId);
      });
    } else {
      // Already in brainstorming, just navigate to the step
      this.stepperService.goToStepById(stepId);
    }
  }
}
