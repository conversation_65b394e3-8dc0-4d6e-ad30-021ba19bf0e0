import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, combineLatest } from 'rxjs';
import { map, distinctUntilChanged, shareReplay } from 'rxjs/operators';
import { AppStateService } from '../../../shared/services/app-state.service';
import { ProductPipelineService } from '../../services/product-pipeline.service';
import { PersonaDataService } from '../../services/persona-data.service';
import {
  PipelineState,
  MarketResearchData,
  LBCData,
  PersonaData,
  SWOTData,
  FeaturesData,
  RoadmapData,
  UserPersona,
  SWOTItem,
  FeatureItem
} from '../../interfaces/pipeline-api.interface';

// Summary-specific interfaces
export interface SummaryPersona {
  name: string;
  role: string;
  avatarUrl: string;
}

export interface SummaryFeature {
  name: string;
  color: string;
  category: 'must_have' | 'should_have' | 'could_have' | 'wont_have';
}

export interface SummarySwotItem {
  category: 'strengths' | 'weaknesses' | 'opportunities' | 'threats';
  points: string[];
  color: string;
}

export interface SummaryTimelineItem {
  icon: string;
  label: string;
}

export interface SummaryTimelineQuarter {
  quarter: string;
  items: SummaryTimelineItem[];
}

export interface SummaryData {
  name: string;
  description: string;
  progress: number;
  contributionText: string;
  understandingText: string;
  features: SummaryFeature[];
  personas: SummaryPersona[];
  swot: SummarySwotItem[];
  timeline: SummaryTimelineQuarter[];
  isLoading: boolean;
  hasData: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class SummaryService {
  private readonly summaryDataSubject = new BehaviorSubject<SummaryData>(this.getDefaultSummaryData());
  public readonly summaryData$ = this.summaryDataSubject.asObservable();

  // Color mappings for different categories
  private readonly FEATURE_COLORS = {
    must_have: '#0F9D57',    // Green
    should_have: '#FDC100',  // Yellow
    could_have: '#FD7542',   // Orange
    wont_have: '#25364D'     // Dark Blue
  };

  private readonly SWOT_COLORS = {
    strengths: '#0F9D57',     // Green
    opportunities: '#FDC100', // Yellow
    weaknesses: '#FD7542',    // Orange
    threats: '#FF4444'        // Red
  };

  constructor(
    private appStateService: AppStateService,
    private pipelineService: ProductPipelineService,
    private personaDataService: PersonaDataService
  ) {
    this.initializeSummaryData();
  }

  private initializeSummaryData(): void {
    // Subscribe to pipeline state changes and transform data
    combineLatest([
      this.appStateService.pipelineState$,
      this.appStateService.loadingState$
    ]).pipe(
      map(([pipelineState, loadingState]) => this.transformPipelineDataToSummary(pipelineState, loadingState.isLoadingPipeline)),
      distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)),
      shareReplay(1)
    ).subscribe(summaryData => {
      this.summaryDataSubject.next(summaryData);
      console.log('📊 Summary data updated:', summaryData);
    });
  }

  private transformPipelineDataToSummary(pipelineState: PipelineState, isLoading: boolean): SummaryData {
    const hasData = pipelineState.run_id !== null && Object.keys(pipelineState.data).length > 0;
    
    return {
      name: this.truncateText(pipelineState.project_name || 'Untitled Project', 50),
      description: this.extractProjectDescription(pipelineState),
      progress: this.calculateProgress(pipelineState),
      contributionText: this.generateContributionText(pipelineState),
      understandingText: this.extractUnderstandingText(pipelineState),
      features: this.extractFeatures(pipelineState.data.features),
      personas: this.extractPersonas(pipelineState.data.persona),
      swot: this.extractSwotAnalysis(pipelineState.data.swot),
      timeline: this.extractTimeline(pipelineState.data.roadmap),
      isLoading,
      hasData
    };
  }

  private extractProjectDescription(pipelineState: PipelineState): string {
    // Try to get description from market research or LBC data
    const marketData = pipelineState.data.market_research;
    const lbcData = pipelineState.data.lbc;
    
    if (marketData?.market_summary) {
      return this.truncateText(marketData.market_summary, 200);
    }
    
    if (lbcData?.value_proposition && lbcData.value_proposition.length > 0) {
      return this.truncateText(lbcData.value_proposition.join('. '), 200);
    }
    
    return 'Project description will be available after completing the understanding step.';
  }

  private calculateProgress(pipelineState: PipelineState): number {
    const totalSteps = 6; // market_research, lbc, persona, swot, features, roadmap
    const completedSteps = pipelineState.completed_steps.length;
    return Math.round((completedSteps / totalSteps) * 100);
  }

  private generateContributionText(pipelineState: PipelineState): string {
    const progress = this.calculateProgress(pipelineState);
    if (progress === 0) {
      return 'Ready to start your brainstorming journey!';
    } else if (progress < 50) {
      return `Great start! You've completed ${progress}% of your brainstorming session.`;
    } else if (progress < 100) {
      return `Excellent progress! You're ${progress}% through your brainstorming session.`;
    } else {
      return `Congratulations! You've completed your brainstorming session with ${progress}% progress.`;
    }
  }

  private extractUnderstandingText(pipelineState: PipelineState): string {
    const marketData = pipelineState.data.market_research;
    const lbcData = pipelineState.data.lbc;
    
    if (marketData) {
      let understanding = '';
      
      if (marketData.market_summary) {
        understanding += this.truncateText(marketData.market_summary, 100);
      }
      
      if (marketData.identified_gaps) {
        understanding += understanding ? ' ' : '';
        understanding += this.truncateText(marketData.identified_gaps, 100);
      }
      
      return understanding || 'Market research insights will appear here.';
    }
    
    if (lbcData?.problem && lbcData.problem.length > 0) {
      return this.truncateText(lbcData.problem.join('. '), 200);
    }
    
    return 'Understanding insights will be available after completing the market research step.';
  }

  private extractFeatures(featuresData?: FeaturesData): SummaryFeature[] {
    if (!featuresData) {
      return [];
    }

    const features: SummaryFeature[] = [];
    
    // Extract titles from each MoSCoW category
    if (featuresData.must_have) {
      features.push(...featuresData.must_have.map(item => ({
        name: this.truncateText(item.title, 15),
        color: this.FEATURE_COLORS.must_have,
        category: 'must_have' as const
      })));
    }
    
    if (featuresData.should_have) {
      features.push(...featuresData.should_have.map(item => ({
        name: this.truncateText(item.title, 15),
        color: this.FEATURE_COLORS.should_have,
        category: 'should_have' as const
      })));
    }
    
    if (featuresData.could_have) {
      features.push(...featuresData.could_have.map(item => ({
        name: this.truncateText(item.title, 15),
        color: this.FEATURE_COLORS.could_have,
        category: 'could_have' as const
      })));
    }
    
    if (featuresData.wont_have) {
      features.push(...featuresData.wont_have.map(item => ({
        name: this.truncateText(item.title, 15),
        color: this.FEATURE_COLORS.wont_have,
        category: 'wont_have' as const
      })));
    }
    
    return features;
  }

  private extractPersonas(personaData?: PersonaData): SummaryPersona[] {
    if (!personaData?.personas) {
      return [];
    }

    return personaData.personas.slice(0, 4).map((persona, index) => {
      // Use gender-based avatar mapping from PersonaDataService
      const avatarUrl = persona.avatar ||
        this.personaDataService.getGenderBasedAvatar(
          (persona as any).gender, // Cast to access gender field
          persona.role,
          index
        );

      return {
        name: this.truncateText(persona.name, 20),
        role: this.truncateText(persona.role, 20),
        avatarUrl: avatarUrl
      };
    });
  }

  private extractSwotAnalysis(swotData?: SWOTData): SummarySwotItem[] {
    if (!swotData) {
      return [];
    }

    const swotItems: SummarySwotItem[] = [];
    
    if (swotData.strengths && swotData.strengths.length > 0) {
      swotItems.push({
        category: 'strengths',
        color: this.SWOT_COLORS.strengths,
        points: swotData.strengths.slice(0, 2).map(item => this.truncateText(item.title, 40))
      });
    }
    
    if (swotData.weaknesses && swotData.weaknesses.length > 0) {
      swotItems.push({
        category: 'weaknesses',
        color: this.SWOT_COLORS.weaknesses,
        points: swotData.weaknesses.slice(0, 2).map(item => this.truncateText(item.title, 40))
      });
    }
    
    if (swotData.opportunities && swotData.opportunities.length > 0) {
      swotItems.push({
        category: 'opportunities',
        color: this.SWOT_COLORS.opportunities,
        points: swotData.opportunities.slice(0, 2).map(item => this.truncateText(item.title, 40))
      });
    }
    
    if (swotData.threats && swotData.threats.length > 0) {
      swotItems.push({
        category: 'threats',
        color: this.SWOT_COLORS.threats,
        points: swotData.threats.slice(0, 2).map(item => this.truncateText(item.title, 40))
      });
    }
    
    return swotItems;
  }

  private extractTimeline(roadmapData?: RoadmapData): SummaryTimelineQuarter[] {
    if (!roadmapData?.project_tasks || roadmapData.project_tasks.length === 0) {
      return this.getDefaultTimeline();
    }

    // Transform roadmap data into timeline quarters
    const quarterMap = new Map<number, SummaryTimelineItem[]>();

    // Process each task from the roadmap data
    roadmapData.project_tasks.forEach((apiTask: any, index: number) => {
      const quarter = apiTask.quarter || 1; // Default to quarter 1 if not specified
      const taskName = apiTask.task || `Task ${index + 1}`;

      // Get appropriate icon based on task name/type
      const icon = this.getTaskIcon(taskName, apiTask.priority);

      const timelineItem: SummaryTimelineItem = {
        icon: icon,
        label: taskName
      };

      // Add to the appropriate quarter
      if (!quarterMap.has(quarter)) {
        quarterMap.set(quarter, []);
      }
      quarterMap.get(quarter)!.push(timelineItem);
    });

    // Convert map to SummaryTimelineQuarter array
    const timelineQuarters: SummaryTimelineQuarter[] = [];

    // Ensure we have at least 4 quarters, even if some are empty
    for (let q = 1; q <= Math.max(4, Math.max(...quarterMap.keys())); q++) {
      const quarterTasks = quarterMap.get(q) || [];

      timelineQuarters.push({
        quarter: `Quarter ${q}`,
        items: quarterTasks
      });
    }

    return timelineQuarters.length > 0 ? timelineQuarters : this.getDefaultTimeline();
  }

  private getDefaultTimeline(): SummaryTimelineQuarter[] {
    return [
      {
        quarter: 'Quarter 1',
        items: [
          { icon: 'awe_research', label: 'Market Research' },
          { icon: 'awe_planning', label: 'Project Planning' },
          { icon: 'awe_design', label: 'Initial Design' }
        ]
      },
      {
        quarter: 'Quarter 2',
        items: [
          { icon: 'awe_prototype', label: 'Prototype Development' },
          { icon: 'awe_test', label: 'Testing & Validation' }
        ]
      },
      {
        quarter: 'Quarter 3',
        items: [
          { icon: 'awe_code', label: 'Development' },
          { icon: 'awe_review', label: 'Quality Review' }
        ]
      },
      {
        quarter: 'Quarter 4',
        items: [
          { icon: 'awe_deploy', label: 'Launch Preparation' },
          { icon: 'awe_business', label: 'Market Launch' }
        ]
      }
    ];
  }

  private truncateText(text: string, maxLength: number): string {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
  }

  /**
   * Get appropriate icon for a task based on its name and priority
   */
  private getTaskIcon(taskName: string, priority?: string): string {
    const lowerTaskName = taskName.toLowerCase();

    // Map task types to icons based on common keywords
    if (lowerTaskName.includes('research') || lowerTaskName.includes('analysis')) {
      return 'awe_research';
    }
    if (lowerTaskName.includes('design') || lowerTaskName.includes('ui') || lowerTaskName.includes('ux')) {
      return 'awe_design';
    }
    if (lowerTaskName.includes('develop') || lowerTaskName.includes('code') || lowerTaskName.includes('implement')) {
      return 'awe_code';
    }
    if (lowerTaskName.includes('test') || lowerTaskName.includes('qa') || lowerTaskName.includes('quality')) {
      return 'awe_test';
    }
    if (lowerTaskName.includes('deploy') || lowerTaskName.includes('launch') || lowerTaskName.includes('release')) {
      return 'awe_deploy';
    }
    if (lowerTaskName.includes('plan') || lowerTaskName.includes('strategy') || lowerTaskName.includes('roadmap')) {
      return 'awe_planning';
    }
    if (lowerTaskName.includes('prototype') || lowerTaskName.includes('mvp') || lowerTaskName.includes('demo')) {
      return 'awe_prototype';
    }
    if (lowerTaskName.includes('market') || lowerTaskName.includes('business') || lowerTaskName.includes('sales')) {
      return 'awe_business';
    }
    if (lowerTaskName.includes('document') || lowerTaskName.includes('spec') || lowerTaskName.includes('requirement')) {
      return 'awe_document';
    }
    if (lowerTaskName.includes('review') || lowerTaskName.includes('feedback') || lowerTaskName.includes('approval')) {
      return 'awe_review';
    }

    // Default icons based on priority if no keyword match
    if (priority) {
      const lowerPriority = priority.toLowerCase();
      if (lowerPriority === 'high') return 'awe_priority_high';
      if (lowerPriority === 'medium') return 'awe_priority_medium';
      if (lowerPriority === 'low') return 'awe_priority_low';
    }

    // Default fallback icon
    return 'awe_task';
  }

  private getDefaultSummaryData(): SummaryData {
    return {
      name: 'Loading...',
      description: 'Loading project data...',
      progress: 0,
      contributionText: 'Preparing your brainstorming session...',
      understandingText: 'Understanding insights will appear here.',
      features: [],
      personas: [],
      swot: [],
      timeline: [],
      isLoading: true,
      hasData: false
    };
  }

  /**
   * Get current summary data synchronously
   */
  getCurrentSummaryData(): SummaryData {
    return this.summaryDataSubject.value;
  }

  /**
   * Check if specific step data is available
   */
  hasStepData(step: string): boolean {
    const currentData = this.summaryDataSubject.value;
    switch (step) {
      case 'understanding':
        return currentData.understandingText !== 'Understanding insights will appear here.';
      case 'personas':
        return currentData.personas.length > 0;
      case 'features':
        return currentData.features.length > 0;
      case 'swot':
        return currentData.swot.length > 0;
      case 'roadmap':
        return currentData.timeline.length > 0;
      default:
        return false;
    }
  }
}
