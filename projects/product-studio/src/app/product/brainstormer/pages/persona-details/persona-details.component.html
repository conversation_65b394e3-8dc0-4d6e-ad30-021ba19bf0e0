<div class="page-container">
  <!-- Top Decorative Header Bar -->
  <!-- <div class="top-header-gradient"></div> -->

  <!-- Main Header with Navigation -->
  <header class="page-header px-3 py-2">
    <button class="btn btn-light d-flex align-items-center" (click)="goBack()">
      <awe-icons iconName="awe_chevron_left" iconSize="20px"></awe-icons>
      &nbsp;&nbsp;&nbsp; Persona List
    </button>
    <div class="persona-filter" *ngIf="selectedPersona">
      <button
        class="mb-3 btn btn-light border dropdown-toggle d-flex align-items-center"
        (click)="togglePersonaSelector()"
      >
        <img [src]="selectedPersona.avatar" class="avatar-sm me-2" />
        <span>{{ selectedPersona.name || selectedPersona.role }}</span>
      </button>
      <div *ngIf="isPersonaSelectorOpen" class="persona-selector-card">
        <div class="card shadow-lg border-0">
          <div class="p-2">
            <button
              *ngFor="let p of personas"
              (click)="selectPersona(p.id)"
              class="list-group-item list-group-item-action border-0 rounded-3 p-2 mb-1"
            >
              <div class="d-flex align-items-center">
                <img [src]="p.avatar" class="avatar-md me-3" />
                <div>
                  <div class="fw-bold">{{ p.name }}</div>
                  <!-- <div class="text-muted small">{{ p.role }}</div> -->
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Main Content Grid -->
  <div class="row g-3 d-flex align-items-stretch p-4">
    <!-- LEFT COLUMN: PERSONA SUMMARY CARD -->
    <div class="col-12 col-sm-6 col-lg-3 col-md-4 col-xl-3" *ngIf="selectedPersona">
      <div class="card h-100 persona-summary-card">
        <div class="card-body">
          <div class="profile-section">
            <div class="position-relative">
              <awe-icons
                [iconName]="'awe_edit'"
                (click)="openEditModal('profile', 'Profile')"
                [iconColor]="'blue'"
                class="three-dots-btn d-flex justify-content-end align-content-end"
              ></awe-icons>
            </div>
            <div class="avatar-wrapper">
              <img [src]="selectedPersona.avatar" class="avatar-img" />
            </div>

            <h2 class="role-title">{{ selectedPersona.role }}</h2>
          </div>
          <div class="info-section">
            <div class="info-row">
              <span>Age</span><span>{{ selectedPersona.age }}</span>
            </div>
            <div class="info-row">
              <span>Education</span><span>{{ selectedPersona.education }}</span>
            </div>
            <div class="info-row">
              <span>Status</span><span>{{ selectedPersona.status }}</span>
            </div>
            <div class="info-row">
              <span>Location</span><span>{{ selectedPersona.location }}</span>
            </div>
            <div class="info-row">
              <span>Tech Literacy</span
              ><span>{{ selectedPersona.techLiteracy }}</span>
            </div>
          </div>
          <div class="quote-section">
            <img [src]="colon" alt="Quote" class="quote-icon" />
            <p class="quote-text">{{ selectedPersona.quote }}</p>
          </div>
          <div class="personality-section">
            <h3 class="personality-title">Personality</h3>
            <div class="personality-tags">
              <span
                *ngFor="let trait of selectedPersona.personality"
                class="tag"
                >{{ trait }}</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- RIGHT COLUMN: DETAIL CARDS -->
    <div class="col-12 col-sm-6 col-lg-9 col-md-8 col-xl-9" *ngIf="selectedPersona">
      <div class="row g-3">
        <!-- Pain Points Card -->
        <div class="col-12 col-md-6 col-lg-6">
          <div class="card detail-card">
            <div
              class="card-header d-flex justify-content-between align-items-center"
            >
              <h3>Pain Points</h3>
              <div class="position-relative">
                <awe-icons
                  [iconName]="'awe_edit'"
                  (click)="openEditModal('painPoints', 'Pain Points')"
                  [iconColor]="'blue'"
                  class="three-dots-btn"
                ></awe-icons>
              </div>
            </div>
            <div class="card-body">
              <ul>
                <li *ngFor="let item of selectedPersona.painPoints">
                  {{ item }}
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Motivation Card -->
        <div class="col-12 col-md-6 col-lg-6">
          <div class="card detail-card">
            <div
              class="card-header d-flex justify-content-between align-items-center"
            >
              <h3>Motivation</h3>
              <div class="position-relative">
                <awe-icons
                  [iconName]="'awe_edit'"
                  (click)="openEditModal('motivation', 'Motivation')"
                  [iconColor]="'blue'"
                  class="three-dots-btn"
                ></awe-icons>
              </div>
            </div>
            <div class="card-body">
              <ul>
                <li *ngFor="let item of selectedPersona.motivation">
                  {{ item }}
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Goals Card -->
        <div class="col-12 col-md-6 col-lg-6">
          <div class="card detail-card">
            <div
              class="card-header d-flex justify-content-between align-items-center"
            >
              <h3>Goals</h3>
              <div class="position-relative">
                <awe-icons
                  [iconName]="'awe_edit'"
                  (click)="openEditModal('goals', 'Goals')"
                  [iconColor]="'blue'"
                  class="three-dots-btn"
                ></awe-icons>
              </div>
            </div>
            <div class="card-body">
              <ul>
                <li *ngFor="let item of selectedPersona.goals">{{ item }}</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Expectations Card -->
        <div class="col-12 col-md-6 col-lg-6">
          <div class="card detail-card">
            <div
              class="card-header d-flex justify-content-between align-items-center"
            >
              <h3>Expectations</h3>
              <div class="position-relative">
                <awe-icons
                  [iconName]="'awe_edit'"
                  (click)="openEditModal('expectations', 'Expectations')"
                  [iconColor]="'blue'"
                  class="three-dots-btn"
                ></awe-icons>
              </div>
            </div>
            <div class="card-body">
              <ul>
                <li *ngFor="let item of selectedPersona.expectations">
                  {{ item }}
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Skills Card -->
        <div class="col-12 col-sm-6 col-md-7 col-lg-8">
          <div class="card detail-card">
            <div
              class="card-header d-flex justify-content-between align-items-center"
            >
              <h3>Skills</h3>
              <div class="position-relative">
                <awe-icons
                  [iconName]="'awe_edit'"
                  (click)="openEditModal('skills', 'Skills')"
                  [iconColor]="'blue'"
                  class="three-dots-btn"
                ></awe-icons>
                <div class="dropdown-arrow position-relative">
                  <div
                    class="dropdown-menu dropdown-menu-end"
                    [class.show]="isDropdownOpen('skills')"
                  >
                    <button
                      class="dropdown-item border-buttom"
                      (click)="openEditModal('skills', 'Skills')"
                      type="button"
                    >
                      Edit
                    </button>
                    <button class="dropdown-item text-danger" type="button">
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div class="card-body">
              <div class="d-flex flex-column gap-3 pt-2">
                <div
                  *ngFor="let skill of selectedPersona.skills; let i = index"
                >
                  <span class="small d-flex align-items-start text-muted">{{
                    skill.name
                  }}</span>
                  <div class="progress" style="height: 8px">
                    <div
                      class="progress-bar rounded"
                      [ngClass]="getProgressBarClass(i)"
                      [style.width.%]="skill.level"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Devices Card -->
        <div class="col-12 col-sm-6 col-md-5 col-lg-4 ">
          <div class="card detail-card device-height">
            <div
              class="card-header d-flex justify-content-between align-items-center"
            >
              <h3>Devices</h3>
              <div class="position-relative">
                <awe-icons
                  [iconName]="'awe_edit'"
                  (click)="openEditModal('devices', 'Devices')"
                  [iconColor]="'blue'"
                  class="three-dots-btn"
                ></awe-icons>
              </div>
            </div>
            <div class="card-body device-body">
              <img
                *ngIf="selectedPersona.devices.includes('mobile')"
                [src]="MobileIcon"
                alt="Mobile"
              />
              <img
                *ngIf="selectedPersona.devices.includes('laptop')"
                [src]="LaptopIcon"
                alt="Laptop"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Click Outside Handler -->
  <div
    *ngIf="openDropdownId"
    class="click-outside-overlay"
    (click)="closeAllDropdowns()"
  ></div>

  <!-- Edit Modal -->
  <awe-modal
    [isOpen]="isEditModalOpen"
    [showHeader]="true"
    [showFooter]="true"
    [width]="'600px'"
    [maxWidth]="'90vw'"
    (closed)="closeEditModal()"
  >
    <div class="mb-2" awe-modal-header>
      <awe-heading variant="s1" type="bold"
        >Edit {{ selectedCardForEdit?.title }}</awe-heading
      >
    </div>

    <div awe-modal-body>
      <div class="modal-form">
        <!-- Profile Data -->
        <div *ngIf="selectedCardForEdit?.type === 'profile'">
          <div class="profile-data row g-3">
            <div class="col-md-12 inp-container">
              <div class="label">
                <label for="name">Name:</label>
              </div>
              <div class="input-wrapper">
                <awe-input
                  variant="fluid"
                  label="Name:"
                  [(ngModel)]="editData.name"
                  placeholder="Enter name"
                  class="w-100"
                ></awe-input>
              </div>
            </div>
            <div class="col-md-12 inp-container">
              <div class="label">
                <label for="role">Role:</label>
              </div>
              <div class="input-wrapper">
                <awe-input
                  variant="fluid"
                  label="Role:"
                  [(ngModel)]="editData.role"
                  placeholder="Enter role"
                  class="w-100"
                ></awe-input>
              </div>
            </div>
            <div class="col-md-12 inp-container">
              <div class="label">
                <label for="age">Age:</label>
              </div>
              <div class="input-wrapper">
                <awe-input
                  variant="fluid"
                  label="Age:"
                  [(ngModel)]="editData.age"
                  type="number"
                  placeholder="Enter age"
                  class="w-100"
                ></awe-input>
              </div>
            </div>
            <div class="col-md-12 inp-container">
              <div class="label">
                <label for="education">Education:</label>
              </div>
              <div class="input-wrapper">
                <awe-input
                  label="Education:"
                  variant="fluid"
                  [(ngModel)]="editData.education"
                  placeholder="Enter education"
                  class="w-100"
                ></awe-input>
              </div>
            </div>
            <div class="col-md-12 inp-container">
              <div class="label">
                <label for="status">Status:</label>
              </div>
              <div class="input-wrapper">
                <awe-input
                  label="Status:"
                  variant="fluid"
                  [(ngModel)]="editData.status"
                  placeholder="Enter status"
                  class="w-100"
                ></awe-input>
              </div>
            </div>
            <div class="col-md-12 inp-container">
              <div class="label">
                <label for="location">Location:</label>
              </div>
              <div class="input-wrapper">
                <awe-input
                  label="Location:"
                  variant="fluid"
                  [(ngModel)]="editData.location"
                  placeholder="Enter location"
                  class="w-100"
                ></awe-input>
              </div>
            </div>
            <div class="col-md-12 inp-container">
              <div class="label">
                <label for="techLiteracy">Tech Literacy:</label>
              </div>
              <div class="input-wrapper">
                <awe-input
                  label="Tech Literacy:"
                  variant="fluid"
                  [(ngModel)]="editData.techLiteracy"
                  placeholder="Enter tech literacy"
                  class="w-100"
                ></awe-input>
              </div>
            </div>
            <div class="col-12 inp-container">
              <div class="label">
                <label for="quote">Quote:</label>
              </div>
              <div class="input-wrapper">
                <awe-input
                  label="Quote:"
                  variant="fluid"
                  [(ngModel)]="editData.quote"
                  placeholder="Enter quote"
                  class="w-100"
                ></awe-input>
              </div>
            </div>
          </div>
        </div>

        <!-- Array Data (Pain Points, Goals, etc.) -->
        <div
          *ngIf="
            isArrayData(editData) &&
            selectedCardForEdit?.type !== 'skills' &&
            selectedCardForEdit?.type !== 'devices'
          "
        >
          <div
            *ngFor="let item of editData; let i = index; trackBy: trackByIndex"
            class="mb-3"
          >
            <div class="d-flex gap-2">
              <awe-input
                variant="fluid"
                [(ngModel)]="editData[i]"
                type="text"
                [icons]="['awe_trash']"
                [placeholder]="
                  'Enter ' + selectedCardForEdit?.title?.toLowerCase() + ' item'
                "
                class="flex-grow-1"
                (iconClickEvent)="removeArrayItem(i)"
              >
              </awe-input>
            </div>
          </div>
          <button (click)="addArrayItem()" class="add-new">Add new +</button>
        </div>

        <!-- Skills Data -->
        <div *ngIf="selectedCardForEdit?.type === 'skills'">
          <div
            *ngFor="let skill of editData; let i = index; trackBy: trackByIndex"
            class="mb-3"
          >
            <label for="skillName">Skill Name:</label>
            <awe-input
              variant="fluid"
              [icons]="['awe_trash']"
              [required]="true"
              errorMessage="This field is required"
              [(ngModel)]="skill.name"
              placeholder="Skill name"
              (iconClickEvent)="removeArrayItem(i)"
              class="w-100"
            >
            </awe-input>
            <div class="input-wrapper">
              <awe-slider
                label="Level"
                [(ngModel)]="skill.level"
                mobileSize="small"
                tabletSize="medium"
                desktopSize="large"
                touchTargetSize="44px"
                [showTicks]="true"
                [customTickValues]="[0, 25, 50, 75, 100]"
                variant="primary"
                (dragStart)="onDragStart(i)"
                (dragEnd)="onDragEnd(i)"
                [min]="0"
                [max]="100"
              ></awe-slider>
            </div>
          </div>
          <button (click)="addArrayItem()" class="mb-3 add-new">
            Add Skills+
          </button>
        </div>

        <!-- Devices Data -->
        <div *ngIf="selectedCardForEdit?.type === 'devices'">
          <div class="mb-3">
            <label class="form-label">Available Devices</label>
            <div class="form-check">
              <input
                class="form-check-input"
                type="checkbox"
                [checked]="editData.includes('mobile')"
                (change)="toggleDevice('mobile')"
                id="deviceMobile"
              />
              <label class="form-check-label" for="deviceMobile">
                Mobile
              </label>
            </div>
            <div class="form-check">
              <input
                class="form-check-input"
                type="checkbox"
                [checked]="editData.includes('laptop')"
                (change)="toggleDevice('laptop')"
                id="deviceLaptop"
              />
              <label class="form-check-label" for="deviceLaptop">
                Laptop
              </label>
            </div>
            <div class="form-check">
              <input
                class="form-check-input"
                type="checkbox"
                [checked]="editData.includes('tablet')"
                (change)="toggleDevice('tablet')"
                id="deviceTablet"
              />
              <label class="form-check-label" for="deviceTablet">
                Tablet
              </label>
            </div>
          </div>
        </div>

        <!-- Regenerate Section -->
        <div class="regenerate-section mt-3">
          <awe-heading variant="h6" type="bold">Regenerate with AI</awe-heading>

          <awe-input
            label="Prompt:"
            [expand]="true"
            [(ngModel)]="regeneratePrompt"
            [icons]="['awe_send']"
            variant="fluid"
            placeholder="Enter prompt to regenerate content..."
            (iconClickEvent)="onRegenerate()"
            class="mb-2"
          >
          </awe-input>
        </div>
      </div>
    </div>

    <div awe-modal-footer>
      <div class="action-btn gap-4 mt-4 d-flex w-100 justify-content-center">
        <button
          type="button"
          class="btn-cancel px-5"
          (click)="closeEditModal()"
        >
          Cancel
        </button>
        <button type="button" class="btn-delete px-5" (click)="saveCardData()">
          Update
        </button>
      </div>
    </div>
  </awe-modal>
</div>
