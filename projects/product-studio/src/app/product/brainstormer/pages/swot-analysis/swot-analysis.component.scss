@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap");

// SWOT Container and Grid Layout
.swot-container {
  padding: 0.75rem;
  background: #f1f5f9;
  min-height: 100vh;
}

.swot-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 0.75rem;
  margin: 0 auto;
  height: 85vh;
  min-height: 700px;
}

.swot-quadrant {
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  max-height: 100%;
}

// Section Headers with Minimal Design
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  color: white;
  position: relative;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);

  .section-title {
    font-size: 1.125rem;
    font-weight: 600;
    letter-spacing: 0.25px;
    color: white;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .section-action {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    color: white;
    width: 2.25rem;
    height: 2.25rem;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 0.25);
      border-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.05);
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
    }

    .plus-icon {
      font-size: 1rem;
      font-weight: bold;
    }
  }
}

// Subtle gradients for each SWOT section
.swot-quadrant:nth-child(1) .section-header {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.85) 0%, rgba(5, 150, 105, 0.85) 100%);
}

.swot-quadrant:nth-child(2) .section-header {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.85) 0%, rgba(217, 119, 6, 0.85) 100%);
}

.swot-quadrant:nth-child(3) .section-header {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.85) 0%, rgba(37, 99, 235, 0.85) 100%);
}

.swot-quadrant:nth-child(4) .section-header {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.85) 0%, rgba(220, 38, 38, 0.85) 100%);
}

// Accordion Container (Optimized Scrollable)
.accordion-container {
  flex: 1;
  padding: 0.375rem;
  background-color: #f8fafc;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  min-height: 0; // Important for flex scrolling

  // Custom scrollbar styling
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;

    &:hover {
      background: #94a3b8;
    }
  }
}

// Accordion Card Styles (No Drag, No Scale)
.accordion-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  flex-shrink: 0; // Prevent shrinking in flex container

  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border-color: #cbd5e1;
  }

  &.active {
    border-color: #6366f1;
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
    z-index: 10;
  }

  &.blur-inactive {
    filter: blur(1px);
    opacity: 0.7;
  }
}

// Accordion Header (simplified for modal trigger)
.accordion-header {
  width: 100%;
  padding: 0.75rem 1rem;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8fafc;
  }

  &:focus {
    outline: none;
    background-color: #f1f5f9;
  }
}

.accordion-title-section {
  flex: 1;
  text-align: left;
  overflow: hidden;
}

.accordion-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  padding-right: 0.75rem;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.accordion-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-shrink: 0;
}

.three-dots-container {
  display: flex;
  align-items: center;
  position: relative;
}

.three-dot-icon {
  cursor: pointer;
}

.chevron-icon {
  color: #64748b;
  transition: transform 0.2s ease;

  svg {
    transition: transform 0.2s ease;
  }
}
// Accordion Content (Inline Expansion)
.accordion-content {
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  max-height: 0;
  opacity: 0;

  &.expanded {
    max-height: 300px;
    opacity: 1;
  }
}

.accordion-content-inner {
  padding: 0 1rem 1rem;
}

.content-divider {
  height: 1px;
  background: linear-gradient(to right, transparent, #e2e8f0, transparent);
  margin-bottom: 0.75rem;
}

.feature-description {
  color: #64748b;
  line-height: 1.5;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
}

// Tags Styles
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.tag-pill {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  background-color: transparent;
  border: 1px solid #3b82f6;
  border-radius: 50px;
  color: #3b82f6;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
}

.tag-pill-readonly {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  background-color: transparent;
  border: 1px solid #3b82f6;
  border-radius: 50px;
  color: #3b82f6;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

.tag-pill-editable {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  background-color: transparent;
  border: 1px solid #3b82f6;
  border-radius: 50px;
  color: #3b82f6;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;

  .remove-tag-btn {
    background: none;
    border: none;
    color: #3b82f6;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(59, 130, 246, 0.1);
    }

    svg {
      width: 12px;
      height: 12px;
    }
  }
}



// Empty State
.empty-state {
  text-align: center;
  color: #64748b;
  font-style: italic;
  padding: 1.5rem 0.75rem;
  border: 2px dashed #cbd5e1;
  border-radius: 8px;
  margin: 0.5rem;
  background-color: #f8fafc;
  font-size: 0.875rem;
}

// Add More Button
.add-more-section {
  margin-top: auto;
  padding: 0.5rem;
}

.add-more-btn {
  width: 100%;
  background-color: #f1f5f9;
  border: 1px solid #cbd5e1;
  border-radius: 6px;
  color: #475569;
  font-size: 0.8rem;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #e2e8f0;
    border-color: #94a3b8;
  }

  .plus-icon {
    font-size: 0.875rem;
  }
}

// Clear Selection Button
.clear-selection-container {
  text-align: center;
  margin-top: 1.5rem;
}

.clear-selection-btn {
  padding: 0.75rem 1.5rem;
  background-color: #64748b;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #475569;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(100, 116, 139, 0.3);
  }
}





// Dropdown Styling
.dropdown-arrow {
  position: relative;

  .dropdown-menu {
    display: none;
    position: absolute;
    top: calc(100% + 0.25rem);
    right: 0;
    min-width: 120px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    padding: 0.5rem 0;
    z-index: 1050; // Higher z-index for better visibility
    border: 1px solid #e2e8f0;
    overflow: hidden; // Prevent content overflow

    &.show {
      display: block;
    }

    .dropdown-item {
      display: block;
      width: 100%;
      padding: 0.5rem 1rem;
      font-weight: 400;
      color: #374151;
      text-align: left;
      background-color: transparent;
      border: none;
      cursor: pointer;
      transition: background-color 0.15s ease;

      &:hover,
      &:focus {
        background-color: #f3f4f6;
        color: #111827;
      }

      &.text-danger {
        color: #dc2626;

        &:hover {
          background-color: #fef2f2;
          color: #991b1b;
        }
      }
    }
  }
}

.border-buttom {
  border-bottom: 1px solid #e5e7eb;
}

// Modal Button Styles
.btn-cancel {
  border: 1px solid var(--Primary-500, #7c3aed);
  background: var(--Neutral-Neutral-colors-Solid, #fff);
  border-radius: 50px;
  padding: 12px 24px;
}

.btn-delete {
  background: var(--Primary-500, #7c3aed);
  border: 1px solid var(--Primary-500, #7c3aed);
  color: #fff;
  border-radius: 50px;
  padding: 12px 24px;
}

// Input Container Overrides
:host ::ng-deep .input-container label {
  display: none;
}

:host ::ng-deep .input-container .input-wrapper.expanded {
  height: 125px;
}

:host ::ng-deep .input-container {
  padding: 0;
}

// Modal Input Alignment Fixes
.edit-modal-body {
  .inp-container {
    margin-top: 0;

    .label {
      margin-bottom: 0.5rem;

      label {
        font-weight: 500;
        color: #374151;
        font-size: 0.875rem;
      }
    }

    .input-wrapper {
      width: 100%;

      :host ::ng-deep awe-input {
        width: 100%;
      }
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .swot-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, auto);
    height: auto;
    min-height: auto;
    gap: 0.5rem;
  }

  .swot-quadrant {
    min-height: 300px;
    max-height: 400px;
  }

  .accordion-container {
    max-height: 250px;
  }
}

@media (max-width: 768px) {
  .swot-container {
    padding: 0.5rem;
  }

  .section-header {
    padding: 0.75rem 1rem;

    .section-title {
      font-size: 1.25rem;
    }

    .section-action {
      min-width: 2.5rem;
      min-height: 2.5rem;
      font-size: 1rem;
    }
  }

  .accordion-header {
    padding: 0.5rem 0.75rem;
  }

  .accordion-title {
    font-size: 0.85rem;
  }

  .accordion-container {
    max-height: 200px;
  }

  .accordion-content-inner {
    padding: 0 0.75rem 0.75rem;
  }
}