import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SwotAnalysisComponent } from './swot-analysis.component';
import { SwotDataService } from '../../services/swot-data.service';
import { ProductPipelineService } from '../../services/product-pipeline.service';

describe('SwotAnalysisComponent', () => {
  let component: SwotAnalysisComponent;
  let fixture: ComponentFixture<SwotAnalysisComponent>;
  let swotDataService: SwotDataService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SwotAnalysisComponent, HttpClientTestingModule],
      providers: [SwotDataService, ProductPipelineService]
    }).compileComponents();

    fixture = TestBed.createComponent(SwotAnalysisComponent);
    component = fixture.componentInstance;
    swotDataService = TestBed.inject(SwotDataService);

    // Mock some test data
    const mockSections = [
      {
        id: 'strengths',
        title: 'S',
        subtitle: 'STRENGTHS',
        features: [
          {
            id: 'strength-1',
            title: 'Test Strength',
            description: 'Test description',
            tags: ['test']
          }
        ]
      }
    ];

    spyOn(swotDataService, 'getSections').and.returnValue(mockSections);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Inline accordion functionality', () => {
    it('should initialize with no active accordion', () => {
      expect(component.activeAccordion).toBeNull();
    });

    it('should toggle accordion when toggleAccordion is called', () => {
      const featureId = 'strength-1';

      // First call should activate the accordion
      component.toggleAccordion(featureId);
      expect(component.activeAccordion).toBe(featureId);

      // Second call should deactivate the accordion
      component.toggleAccordion(featureId);
      expect(component.activeAccordion).toBeNull();
    });

    it('should return correct active state for accordion', () => {
      const featureId = 'strength-1';

      expect(component.isAccordionActive(featureId)).toBeFalse();

      component.activeAccordion = featureId;
      expect(component.isAccordionActive(featureId)).toBeTrue();
    });

    it('should clear accordion selection', () => {
      component.activeAccordion = 'strength-1';
      component.clearAccordionSelection();
      expect(component.activeAccordion).toBeNull();
    });

    it('should switch between different accordions', () => {
      const firstFeature = 'strength-1';
      const secondFeature = 'strength-2';

      // Activate first accordion
      component.toggleAccordion(firstFeature);
      expect(component.activeAccordion).toBe(firstFeature);

      // Activate second accordion (should deactivate first)
      component.toggleAccordion(secondFeature);
      expect(component.activeAccordion).toBe(secondFeature);
    });

    it('should truncate long titles correctly', () => {
      const shortTitle = 'Short title';
      const longTitle = 'This is a very long title that should be truncated because it exceeds the maximum character limit for display in the accordion header';

      expect(component.truncateTitle(shortTitle)).toBe(shortTitle);
      expect(component.truncateTitle(longTitle)).toContain('...');
      expect(component.truncateTitle(longTitle).length).toBeLessThanOrEqual(73); // 70 chars + '...'
    });

    it('should truncate at sentence boundary when possible', () => {
      const titleWithSentence = 'First sentence. Second sentence that is much longer and should be truncated.';
      const result = component.truncateTitle(titleWithSentence);

      expect(result).toBe('First sentence...');
    });
  });
});
