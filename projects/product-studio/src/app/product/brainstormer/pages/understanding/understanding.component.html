<div class="container-fluid mt-3">
  <!-- First Row -->
  <div class="row canvas-row">
    <!-- First Column - 2 cards stacked -->
    <div class="col-lg-4 col-md-12 col-sm-12 mb-3 px-1">
      <!-- Problem Card -->
      <div class="column-container first-column d-flex">
        <awe-card
          [showHeader]="true"
          [showBody]="true"
          [applyHeaderPadding]="true"
          [applyBodyPadding]="true"
          cardClass="understanding-canvas-item"
        >
          <!-- Problem Card -->
          <div
            class="problem-card"
            *ngFor="let item of firstRowProblemCardWithData"
          >
            <div
              awe-card-header-content
              class="custom-card-header px-lg-3 pt-lg-4 d-flex justify-content-between align-items-center"
            >
              <div class="d-flex align-items-center">
                <awe-heading variant="s1" type="bold">{{
                  item.title
                }}</awe-heading>
              </div>
              <div class="card-actions">
                <awe-icons
                  iconName="awe_edit"
                  class="pt-2"
                  role="button"
                  tabindex="0"
                  color="#007bff"
                  (click)="openEditModal(item)"
                ></awe-icons>
              </div>
            </div>
            <div class="flex-container">
              <ul class="px-lg-3 pt-3 list-unstyled mb-0">
                <li
                  *ngFor="let dataItem of item.data; let i = index"
                  class="mb-2 d-flex align-items-start"
                >
                  <!-- <span class="me-2 boole text-muted">*</span> -->
                  <awe-caption class="bullet-point" variant="s2" type="regular">
                    {{ dataItem }}
                  </awe-caption>
                </li>
              </ul>
              <div class="img-position">
                <img
                  class="card-icon-img"
                  [src]="item.icon"
                  alt="Solution icon"
                />
              </div>
            </div>
          </div>
        </awe-card>
      </div>
    </div>
    <!-- solution card -->
    <div class="col-lg-4 col-md-12 col-sm-12 mb-3 px-1">
      <div class="column-container first-column d-flex">
        <awe-card
          [showHeader]="true"
          [showBody]="true"
          [applyHeaderPadding]="true"
          [applyBodyPadding]="true"
          cardClass="understanding-canvas-item"
        >
          <!-- Problem Card -->
          <div
            class="problem-card"
            *ngFor="let item of firstRowSolutionCardWithData"
          >
            <div
              awe-card-header-content
              class="custom-card-header px-lg-3 pt-lg-4 d-flex justify-content-between align-items-center"
            >
              <div class="d-flex align-items-center">
                <awe-heading variant="s1" type="bold">{{
                  item.title
                }}</awe-heading>
              </div>
              <div class="card-actions">
                <awe-icons
                  iconName="awe_edit"
                  class="pt-2"
                  role="button"
                  tabindex="0"
                  color="#007bff"
                  (click)="openEditModal(item)"
                ></awe-icons>
              </div>
            </div>
            <div class="flex-container">
              <ul class="px-lg-3 pt-3 list-unstyled mb-0">
                <li
                  *ngFor="let dataItem of item.data; let i = index"
                  class="mb-2 d-flex align-items-start"
                >
                  <!-- <span class="me-2 boole text-muted">*</span> -->
                  <awe-caption class="bullet-point" variant="s2" type="regular">
                    {{ dataItem }}
                  </awe-caption>
                </li>
              </ul>
              <div class="img-position">
                <img
                  class="card-icon-img"
                  [src]="item.icon"
                  alt="Solution icon"
                />
              </div>
            </div>
          </div>
        </awe-card>
      </div>
    </div>
    <!-- Key Partners card-->
    <div class="col-lg-4 col-md-12 col-sm-12 mb-3 px-1">
      <div class="column-container second-column">
        <awe-card
          *ngFor="let item of keyPartnersDataWithData"
          [showHeader]="true"
          [showBody]="true"
          [applyHeaderPadding]="true"
          [applyBodyPadding]="true"
          cardClass="understanding-canvas-item"
        >
          <div
            awe-card-header-content
            class="custom-card-header px-lg-3 pt-lg-4 d-flex justify-content-between align-items-center"
          >
            <div class="d-flex align-items-center">
              <awe-heading variant="s1" type="bold">{{
                item.title
              }}</awe-heading>
            </div>
            <div class="card-actions">
              <awe-icons
                iconName="awe_edit"
                class="pt-2"
                role="button"
                tabindex="0"
                color="#007bff"
                (click)="openEditModal(item)"
              ></awe-icons>
            </div>
          </div>
          <div class="flex-container">
            <!-- Projected Header Content -->
            <div class="key-partners align-self-start">
              <div class="key-partners-wrapper">
                <ul class="px-lg-3 pt-3 list-unstyled mb-0">
                  <li
                    *ngFor="let dataItem of item.data; let i = index"
                    class="mb-2 d-flex align-items-start"
                  >
                    <!-- <span class="me-2 boole text-muted">*</span> -->
                    <awe-caption
                      class="bullet-point"
                      variant="s2"
                      type="regular"
                    >
                      {{ dataItem }}
                    </awe-caption>
                  </li>
                </ul>
              </div>
            </div>

            <div class="img-position">
              <img
                class="card-icon-img"
                [src]="item.icon"
                alt="Solution icon"
              />
            </div>
          </div>
          <!-- Projected Body Content (Default Slot) -->
        </awe-card>
      </div>
    </div>
  </div>

  <div class="row canvas-row">
    <div class="col-lg-4 col-md-12 col-sm-12 mb-3 px-1">
      <div class="column-container second-column">
        <awe-card
          *ngFor="let item of valuePropositionDataWithData"
          [showHeader]="true"
          [showBody]="true"
          [applyHeaderPadding]="true"
          [applyBodyPadding]="true"
          cardClass="understanding-canvas-item"
        >
          <div
            awe-card-header-content
            class="custom-card-header px-lg-3 pt-lg-4 d-flex justify-content-between align-items-center"
          >
            <div class="d-flex align-items-center">
              <awe-heading variant="s1" type="bold">{{
                item.title
              }}</awe-heading>
            </div>
            <div class="card-actions">
              <awe-icons
                iconName="awe_edit"
                class="pt-2"
                role="button"
                tabindex="0"
                color="#007bff"
                (click)="openEditModal(item)"
              ></awe-icons>
            </div>
          </div>
          <div class="flex-container">
            <!-- Projected Header Content -->
            <div class="key-partners align-self-start">
              <div class="key-partners-wrapper">
                <ul class="px-lg-3 pt-3 list-unstyled mb-0">
                  <li
                    *ngFor="let dataItem of item.data; let i = index"
                    class="mb-2 d-flex align-items-start"
                  >
                    <!-- <span class="me-2 boole text-muted">*</span> -->
                    <awe-caption
                      class="bullet-point"
                      variant="s2"
                      type="regular"
                    >
                      {{ dataItem }}
                    </awe-caption>
                  </li>
                </ul>
              </div>
            </div>

            <div class="img-position">
              <img
                class="card-icon-img"
                [src]="item.icon"
                alt="Solution icon"
              />
            </div>
          </div>
          <!-- Projected Body Content (Default Slot) -->
        </awe-card>
      </div>
    </div>
    <div class="col-lg-4 col-md-12 col-sm-12 mb-3 px-1">
      <div class="column-container second-column">
        <awe-card
          *ngFor="let item of keyMetricsDataWithData"
          [showHeader]="true"
          [showBody]="true"
          [applyHeaderPadding]="true"
          [applyBodyPadding]="true"
          cardClass="understanding-canvas-item"
        >
          <div
            awe-card-header-content
            class="custom-card-header px-lg-3 pt-lg-4 d-flex justify-content-between align-items-center"
          >
            <div class="d-flex align-items-center">
              <awe-heading variant="s1" type="bold">{{
                item.title
              }}</awe-heading>
            </div>
            <div class="card-actions">
              <awe-icons
                iconName="awe_edit"
                class="pt-2"
                role="button"
                tabindex="0"
                color="#007bff"
                (click)="openEditModal(item)"
              ></awe-icons>
            </div>
          </div>
          <div class="flex-container">
            <!-- Projected Header Content -->
            <div class="key-partners align-self-start">
              <div class="key-partners-wrapper">
                <ul class="px-lg-3 pt-3 list-unstyled mb-0">
                  <li
                    *ngFor="let dataItem of item.data; let i = index"
                    class="mb-2 d-flex align-items-start"
                  >
                    <!-- <span class="me-2 boole text-muted">*</span> -->
                    <awe-caption
                      class="bullet-point"
                      variant="s2"
                      type="regular"
                    >
                      {{ dataItem }}
                    </awe-caption>
                  </li>
                </ul>
              </div>
            </div>

            <div class="img-position">
              <img
                class="card-icon-img"
                [src]="item.icon"
                alt="Solution icon"
              />
            </div>
          </div>
          <!-- Projected Body Content (Default Slot) -->
        </awe-card>
      </div>
    </div>
    <div class="col-lg-4 col-md-12 col-sm-12 mb-3 px-1">
      <div class="column-container second-column">
        <awe-card
          *ngFor="let item of alternativesDataWithData"
          [showHeader]="true"
          [showBody]="true"
          [applyHeaderPadding]="true"
          [applyBodyPadding]="true"
          cardClass="understanding-canvas-item"
        >
          <div
            awe-card-header-content
            class="custom-card-header px-lg-3 pt-lg-4 d-flex justify-content-between align-items-center"
          >
            <div class="d-flex align-items-center">
              <awe-heading variant="s1" type="bold">{{
                item.title
              }}</awe-heading>
            </div>
            <div class="card-actions">
              <awe-icons
                iconName="awe_edit"
                class="pt-2"
                role="button"
                tabindex="0"
                color="#007bff"
                (click)="openEditModal(item)"
              ></awe-icons>
            </div>
          </div>
          <div class="flex-container">
            <!-- Projected Header Content -->
            <div class="key-partners align-self-start">
              <div class="key-partners-wrapper">
                <ul class="px-lg-3 pt-3 list-unstyled mb-0">
                  <li
                    *ngFor="let dataItem of item.data; let i = index"
                    class="mb-2 d-flex align-items-start"
                  >
                    <!-- <span class="me-2 boole text-muted">*</span> -->
                    <awe-caption
                      class="bullet-point"
                      variant="s2"
                      type="regular"
                    >
                      {{ dataItem }}
                    </awe-caption>
                  </li>
                </ul>
              </div>
            </div>

            <!-- <div class="img-position">
              <img
                class="card-icon-img"
                [src]="item.icon"
                alt="Solution icon"
              />
            </div> -->
          </div>
          <!-- Projected Body Content (Default Slot) -->
        </awe-card>
      </div>
    </div>
  </div>

  <!-- Second Row - 2 cards side by side -->
  <div class="row canvas-row">
    <!-- The *ngFor is on the column div, and awe-card is inside it -->
    <div class="col-lg-4 col-md-12 col-sm-12 mb-3 px-1">
      <div class="column-container second-column">
        <awe-card
          *ngFor="let item of solutionTenantsDataWithData"
          [showHeader]="true"
          [showBody]="true"
          [applyHeaderPadding]="true"
          [applyBodyPadding]="true"
          cardClass="understanding-canvas-item"
        >
          <div
            awe-card-header-content
            class="custom-card-header px-lg-3 pt-lg-4 d-flex justify-content-between align-items-center"
          >
            <div class="d-flex align-items-center">
              <awe-heading variant="s1" type="bold">{{
                item.title
              }}</awe-heading>
            </div>
            <div class="card-actions">
              <awe-icons
                iconName="awe_edit"
                class="pt-2"
                role="button"
                tabindex="0"
                color="#007bff"
                (click)="openEditModal(item)"
              ></awe-icons>
            </div>
          </div>
          <div class="flex-container">
            <!-- Projected Header Content -->
            <div class="key-partners align-self-start">
              <div class="key-partners-wrapper">
                <ul class="px-lg-3 pt-3 list-unstyled mb-0">
                  <li
                    *ngFor="let dataItem of item.data; let i = index"
                    class="mb-2 d-flex align-items-start"
                  >
                    <!-- <span class="me-2 boole text-muted">*</span> -->
                    <awe-caption
                      class="bullet-point"
                      variant="s2"
                      type="regular"
                    >
                      {{ dataItem }}
                    </awe-caption>
                  </li>
                </ul>
              </div>
            </div>

            <div class="img-position">
              <img
                class="card-icon-img"
                [src]="item.icon"
                alt="Solution icon"
              />
            </div>
          </div>
          <!-- Projected Body Content (Default Slot) -->
        </awe-card>
      </div>
    </div>
    <div
      class="col-lg-4 col-md-6 col-sm-6 mb-3 px-1 second-row"
      *ngFor="let item of costRevenueItemsWithData"
    >
      <awe-card
        [showHeader]="true"
        [showBody]="true"
        [applyHeaderPadding]="true"
        [applyBodyPadding]="true"
        cardClass="understanding-canvas-item"
      >
        <div
          awe-card-header-content
          class="custom-card-header px-lg-3 pt-lg-4 d-flex justify-content-between align-items-center"
        >
          <div class="d-flex align-items-center">
            <awe-heading variant="s1" type="regular">{{
              item.title
            }}</awe-heading>
          </div>
          <div class="card-actions">
            <awe-icons
              iconName="awe_edit"
              class="pt-2"
              role="button"
              tabindex="0"
              color="#007bff"
              (click)="openEditModal(item)"
            ></awe-icons>
          </div>
        </div>
        <!-- Projected Header Content -->
        <div class="d-flex flex-row justify-content-between align-items-end">
          <div class="cost-revenue-wrapper">
            <div class="flex-container-sec-row">
              <div class="matrix-alternative-wrapper">
                <ul class="px-lg-3 pt-3 list-unstyled mb-0">
                  <li
                    *ngFor="let dataItem of item.data; let i = index"
                    class="mb-2 d-flex align-items-start"
                  >
                    <!-- <span class="me-2 text-muted">*</span> -->
                    <awe-caption
                      class="bullet-point"
                      variant="s2"
                      type="regular"
                    >
                      {{ dataItem }}
                    </awe-caption>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="img-position">
            <img class="card-icon-img" [src]="item.icon" alt="Solution icon" />
          </div>
        </div>
      </awe-card>
    </div>
  </div>

  <awe-modal
    [isOpen]="isEditModalOpen"
    (closed)="closeEditModal()"
    [showHeader]="true"
    [showFooter]="true"
    width="33%"
    height="auto"
    position="center"
    animation="fade"
    [showCloseButton]="true"
    modalClass="edit-understanding-modal"
  >
    <!-- Projected Modal Header -->
    <div awe-modal-header class="edit-modal-header">
      <awe-heading variant="s1" type="bold">Edit Understanding</awe-heading>
    </div>

    <!-- Projected Modal Body -->
    <div
      awe-modal-body
      *ngIf="selectedItemForEdit"
      class="edit-modal-body mt-2"
    >
      <awe-body-text type="body-test">{{
        selectedItemForEdit.title
      }}</awe-body-text>

      <!-- Editable Data Items -->
      <div
        *ngFor="
          let dataPoint of editableItemData;
          let i = index;
          trackBy: trackByFn
        "
        class="editable-data-item d-flex align-items-center mb-2 p-1"
      >
        <input
          type="text"
          class="form-control-sm flex-grow-1"
          [(ngModel)]="editableItemData[i]"
          placeholder="Enter data"
        />
        <awe-icons
          iconName="awe_trash"
          iconColor="danger"
          (click)="removeEditableDataItem(i)"
        ></awe-icons>
      </div>

      <!-- Add New Data Item -->
      <div
        (click)="addEditableDataItem()"
        class="editable-data-item w-100 add-new-data-btn"
      >
        <span class="d-flex align-items-center justify-content-start"
          >Add New</span
        >
        <span class="d-flex align-items-center justify-content-end">
          <awe-icons
            class="d-flex align-items-center justify-content-end"
            iconName="awe_plus"
            iconColor="danger"
          ></awe-icons>
        </span>
      </div>

      <!-- Regenerate Section -->
      <div class="regenerate-section mt-4">
        <div class="input-group">
          <awe-input
            id="regeneratePrompt"
            label="Regenerate"
            variant="fluid"
            placeholder="This field is required"
            class="form-control form-control-sm"
            [required]="false"
            errorMessage="This field is required"
            [icons]="['awe_send']"
            iconColor="action"
            [(ngModel)]="regeneratePrompt"
            placeholder="Type your prompt here..."
          >
          </awe-input>
        </div>
      </div>
    </div>

    <!-- Projected Modal Footer -->
    <div awe-modal-footer class="edit-modal-footer">
      <!-- <button type="button" class="btn btn-secondary" (click)="closeEditModal()">Cancel</button> -->
      <!-- awe-modal handles close via X or backdrop -->
      <!-- <awe-button
        type="button"
        (userClick)="updateUnderstandingItem($event)"
        variant="primary"
        class="w-100"
        width="150px"
        height="50px"
        gradient="linear-gradient(45deg,#6566CD,#F96CAB)"
        hoverEffect="slide-bg"
        [loading]="loadingStates['submit']"
        loadingType="spinner"
      >
        Update
      </awe-button> -->

      <div awe-modal-footer>
        <div class="action-btn gap-4 mt-4 d-flex w-100 justify-content-center">
          <button
            type="button"
            class="btn-cancel px-5"
            (click)="closeEditModal()"
          >
            Cancel
          </button>
          <button
            type="button"
            class="btn-delete px-5"
            (click)="updateUnderstandingItem($event)"
          >
            Update
          </button>
        </div>
      </div>
    </div>
  </awe-modal>
</div>
