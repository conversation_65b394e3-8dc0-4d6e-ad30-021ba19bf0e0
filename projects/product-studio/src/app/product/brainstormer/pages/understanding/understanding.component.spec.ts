import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UnderstandingDataService } from '../../services/understanding-data.service';
import { UnderstandingComponent } from './understanding.component';
import { CanvasItem } from '../../services/understanding-data.service';

describe('UnderstandingComponent', () => {
  let component: UnderstandingComponent;
  let fixture: ComponentFixture<UnderstandingComponent>;
  let mockUnderstandingDataService: jasmine.SpyObj<UnderstandingDataService>;

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('UnderstandingDataService', [
      'getCostRevenueItems',
      'getCustomerSegmentItems',
      'getSolutionTenantsData',
      'getFirstRowProblemData',
      'getFirstRowSolutionCard',
      'getKeyPartnersData',
      'getValuePropositionData',
      'getKeyMetricsAlternativesData',
      'getAlternativesData'
    ]);

    await TestBed.configureTestingModule({
      imports: [UnderstandingComponent],
      providers: [
        { provide: UnderstandingDataService, useValue: spy }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(UnderstandingComponent);
    component = fixture.componentInstance;
    mockUnderstandingDataService = TestBed.inject(UnderstandingDataService) as jasmine.SpyObj<UnderstandingDataService>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('hasCardData', () => {
    it('should return true for card with meaningful data', () => {
      const cardWithData: CanvasItem = {
        id: 'test-1',
        title: 'Test Card',
        icon: 'test-icon',
        iconBg: 'blue',
        data: ['Valid data item', 'Another valid item']
      };

      expect(component.hasCardData(cardWithData)).toBe(true);
    });

    it('should return false for card with empty data array', () => {
      const cardWithEmptyData: CanvasItem = {
        id: 'test-2',
        title: 'Empty Card',
        icon: 'test-icon',
        iconBg: 'blue',
        data: []
      };

      expect(component.hasCardData(cardWithEmptyData)).toBe(false);
    });

    it('should return false for card with only empty strings', () => {
      const cardWithEmptyStrings: CanvasItem = {
        id: 'test-3',
        title: 'Empty Strings Card',
        icon: 'test-icon',
        iconBg: 'blue',
        data: ['', '   ', '']
      };

      expect(component.hasCardData(cardWithEmptyStrings)).toBe(false);
    });

    it('should return true for card with at least one non-empty string', () => {
      const cardWithMixedData: CanvasItem = {
        id: 'test-4',
        title: 'Mixed Data Card',
        icon: 'test-icon',
        iconBg: 'blue',
        data: ['', 'Valid item', '   ']
      };

      expect(component.hasCardData(cardWithMixedData)).toBe(true);
    });
  });

  describe('filtered data getters', () => {
    it('should filter cost revenue items with data', () => {
      const mockItems: CanvasItem[] = [
        {
          id: 'cost-1',
          title: 'Cost Structure',
          icon: 'cost-icon',
          iconBg: 'red',
          data: ['Valid cost data']
        },
        {
          id: 'revenue-1',
          title: 'Revenue Streams',
          icon: 'revenue-icon',
          iconBg: 'green',
          data: []
        }
      ];

      mockUnderstandingDataService.getCostRevenueItems.and.returnValue(mockItems);

      const filteredItems = component.costRevenueItemsWithData;
      expect(filteredItems.length).toBe(1);
      expect(filteredItems[0].id).toBe('cost-1');
    });
  });
});
