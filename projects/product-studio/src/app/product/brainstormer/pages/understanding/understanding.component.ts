import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ChangeDetectorRef, Component, OnInit, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import {
  BodyTextComponent,
  ButtonComponent,
  CaptionComponent,
  HeadingComponent,
  InputComponent,
} from '@awe/play-comp-library';
import { IconsComponent } from '../../../../shared/components/icons/icons.component';
import { AweCardComponent } from '../../components/awe-card/awe-card.component';
import { AweModalComponent } from '../../components/awe-modal/awe-modal.component';
import {
  UnderstandingDataService,
  CanvasItem,
} from '../../services/understanding-data.service';
import { ProductPipelineService } from '../../services/product-pipeline.service';
import { LBCData } from '../../interfaces/pipeline-api.interface';
import { SplitScreenService } from '../../services/split-screen.service';

@Component({
  selector: 'app-understanding',
  templateUrl: './understanding.component.html',
  styleUrls: ['./understanding.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    CaptionComponent,
    AweCardComponent,
    IconsComponent,
    AweModalComponent,
    HeadingComponent,
    BodyTextComponent,
    InputComponent,
    ButtonComponent,
  ],
})
export class UnderstandingComponent implements OnInit, OnDestroy {
  // Action Icons (used in the projected header)
  trashIcon: string = 'icons/awe_trash.svg';
  editIcon: string = 'icons/awe_edit.svg';
  errorIcon: string = 'icons/awe_error.svg';

  // Subscription management
  private subscription = new Subscription();

  // Card Data Icons (used in the projected header)
  problemIcon: string = 'cards-images/solution.png';
  keyPartnerCardImg: string = 'cards-images/key-partners.png';
  valuePropositionCardImg: string = 'cards-images/value-proposition.png';
  solutionCardImg: string = 'cards-images/solution-card.png';
  customerSegmentsCardImg: string = 'cards-images/ customer-segments.png';
  keyMetricsCardImg: string = 'cards-images/key-metrics.png';
  alternativesCardImg: string = 'cards-images/alternatives-card.png';
  costStructureCardImg: string = 'cards-images/cost_structure.png';
  revenueStreamsCardImg: string = 'cards-images/revenue-stream.png';
  solutionTenantsCardImg: string = 'cards-images/solutionTenants.png';

  button_bg: string =
    'linear-gradient(90deg, rgba(101, 102, 205, 0.40) -2.03%, rgba(249, 108, 171, 0.40) 109.39%);';
  loadingStates: Record<string, boolean> = {
    submit: false,
    save: false,
    delete: false,
    skeleton1: false,
    skeleton2: false,
  };

  // Main data source for the cards
  businessModelCanvas: CanvasItem[] = [];

  // Modal State
  isEditModalOpen = false;
  selectedItemForEdit: CanvasItem | null = null;
  editableItemData: string[] = [];
  regeneratePrompt: string = '';

  constructor(
    private cdRef: ChangeDetectorRef,
    private understandingDataService: UnderstandingDataService,
    private pipelineService: ProductPipelineService,
    private splitScreenService: SplitScreenService
  ) {}

  ngOnInit(): void {
    // Subscribe to data changes
    this.subscription.add(
      this.understandingDataService.businessModelCanvas$.subscribe((canvas) => {
        this.businessModelCanvas = canvas;
        this.updateIconsForCanvas();
      }),
    );

    // Subscribe to pipeline state changes to load appropriate data
    this.subscription.add(
      this.pipelineService.pipelineState$.subscribe((state) => {
        console.log('Pipeline state updated:', state);

        // Check if we have market research data (step: "market_research" with data)
        if (
          state.current_step === 'market_research' &&
          state.data &&
          Object.keys(state.data).length > 0
        ) {
          // Load market research data from API response
          this.loadMarketResearchData(state.data);
        } else if (state.data.lbc) {
          // Load LBC data from API response (for future LBC step)
          this.loadLBCData(state.data.lbc);
        } else if (state.run_id && state.current_step === 'market_research') {
          // Wait for API data - no more mock data loading
          console.log('Waiting for market research API data...');
        }
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  // Mock data loading method commented out - now using only API data
  // /**
  //  * Load initial data for understanding step
  //  */
  // private loadInitialData(): void {
  //   // For now, show default data until we get the actual market research data
  //   // In a real implementation, you might want to call the API here
  //   // this.pipelineService.progressToStep('market_research').subscribe(...)

  //   // Use the default data from the service for now
  //   // The actual market research data will be loaded when user clicks next
  // }

  /**
   * Load Market Research data from pipeline API response
   * This handles the new API response structure with LBC data
   */
  private loadMarketResearchData(apiData: any): void {
    console.log('Loading market research data:', apiData);

  }

  /**
   * Load LBC data from pipeline API response
   */
  private loadLBCData(lbcData: LBCData): void {
    const lbcCanvasItems: CanvasItem[] = [
      {
        id: 'problem',
        title: 'Problem',
        icon: this.problemIcon,
        iconBg: '#ffebee',
        data: lbcData.problem || [],
      },
      {
        id: 'key-partners',
        title: 'Key Partners',
        icon: this.keyPartnerCardImg,
        iconBg: '#e3f2fd',
        data: lbcData.key_partners || [],
      },
      {
        id: 'value-proposition',
        title: 'Value Proposition',
        icon: this.valuePropositionCardImg,
        iconBg: '#fff3e0',
        data: lbcData.value_proposition || [],
      },
      {
        id: 'customer-segments',
        title: 'Customer Segments',
        icon: this.customerSegmentsCardImg,
        iconBg: '#e8f5e8',
        data: lbcData.customer_segments || [],
      },
      {
        id: 'key-metrics',
        title: 'Key Metrics',
        icon: this.keyMetricsCardImg,
        iconBg: '#fff3e0',
        data: lbcData.key_metrics || [],
      },
      {
        id: 'solution',
        title: 'Solution',
        icon: this.solutionCardImg,
        iconBg: '#e8f5e8',
        data: lbcData.solution || [],
      },
      {
        id: 'alternatives',
        title: 'Alternatives',
        icon: this.alternativesCardImg,
        iconBg: '#e8f5e8',
        data: lbcData.alternatives || [],
      },
      {
        id: 'cost-structure',
        title: 'Cost Structure',
        icon: this.costStructureCardImg,
        iconBg: '#ffebee',
        data: lbcData.cost_structure || [],
      },
      {
        id: 'revenue-streams',
        title: 'Revenue Streams',
        icon: this.revenueStreamsCardImg,
        iconBg: '#e8f5e8',
        data: lbcData.revenue_streams || [],
      },
      {
        id: 'solution-tenants',
        title: 'Solution Tenants',
        icon: this.solutionCardImg,
        iconBg: '#e8f5e8',
        data: lbcData.solution_tenants || [],
      },
    ];

    // Update the understanding data service with LBC data
    this.understandingDataService.updateBusinessModelCanvas(lbcCanvasItems);
  }

  private updateIconsForCanvas(): void {
    // Update icons for canvas items based on their IDs
    this.businessModelCanvas.forEach((item) => {
      switch (item.id) {
        case 'problem':
          item.icon = this.problemIcon;
          break;
        case 'key-partners':
          item.icon = this.keyPartnerCardImg;
          break;
        case 'value-proposition':
          item.icon = this.valuePropositionCardImg;
          break;
        case 'solution':
          item.icon = this.solutionCardImg;
          break;
        case 'customer-segments':
          item.icon = this.customerSegmentsCardImg;
          break;
        case 'key-metrics':
          item.icon = this.keyMetricsCardImg;
          break;
        case 'alternatives':
          item.icon = this.alternativesCardImg;
          break;
        case 'cost-structure':
          item.icon = this.costStructureCardImg;
          break;
        case 'revenue-streams':
          item.icon = this.revenueStreamsCardImg;
          break;
      }
    });
  }

  get firstRowProblemCard(): CanvasItem[] {
    return this.understandingDataService.getFirstRowProblemData();
  }
  get firstRowSolutionCard(): CanvasItem[] {
    return this.understandingDataService.getFirstRowSolutionCard();
  }

  get keyPartnersData(): CanvasItem[] {
    return this.understandingDataService.getKeyPartnersData();
  }

  get valuePropositionData(): CanvasItem[] {
    return this.understandingDataService.getValuePropositionData();
  }

  get keyMetricsData(): CanvasItem[] {
    return this.understandingDataService.getKeyMetricsAlternativesData();
  }
  get alternativesData(): CanvasItem[] {
    return this.understandingDataService.getAlternativesData();
  }

  get solutionTenantsData(): CanvasItem[] {
    return this.understandingDataService.getCanvasItemById('solution-tenants')
      ? [
          this.understandingDataService.getCanvasItemById(
            'solution-tenants',
          ) as CanvasItem,
        ]
      : [];
  }

  get customerSegmentItems(): CanvasItem[] {
    return this.understandingDataService.getCustomerSegmentItems();
  }

  get costRevenueItems(): CanvasItem[] {
    return this.understandingDataService.getCostRevenueItems();
  }

  // Helper methods to check if cards have meaningful data
  hasCardData(item: CanvasItem): boolean {
    return item && item.data && item.data.length > 0 &&
           item.data.some(dataItem => dataItem && dataItem.trim().length > 0);
  }

  /**
   * Trims text data to display only the first sentence (up to the first period)
   * @param text - The text to trim
   * @returns The trimmed text ending with a period
   */
  trimToFirstSentence(text: string): string {
    if (!text || typeof text !== 'string') {
      return '';
    }

    const trimmedText = text.trim();
    if (trimmedText.length === 0) {
      return '';
    }

    // Find the first period
    const firstPeriodIndex = trimmedText.indexOf('.');

    if (firstPeriodIndex === -1) {
      // No period found, return the original text with a period added
      return trimmedText + '.';
    }

    // Return text up to and including the first period
    return trimmedText.substring(0, firstPeriodIndex + 1);
  }

  /**
   * Trims all data items in a CanvasItem to first sentences
   * @param item - The CanvasItem to process
   * @returns A new CanvasItem with trimmed data
   */
  getTrimmedCanvasItem(item: CanvasItem): CanvasItem {
    if (!item || !item.data) {
      return item;
    }

    return {
      ...item,
      data: item.data.map(dataItem => this.trimToFirstSentence(dataItem))
    };
  }

  /**
   * Trims all data items in an array of CanvasItems to first sentences
   * @param items - The array of CanvasItems to process
   * @returns A new array with trimmed data
   */
  getTrimmedCanvasItems(items: CanvasItem[]): CanvasItem[] {
    if (!items || !Array.isArray(items)) {
      return [];
    }

    return items.map(item => this.getTrimmedCanvasItem(item));
  }

  get costRevenueItemsWithData(): CanvasItem[] {
    return this.costRevenueItems.filter(item => this.hasCardData(item));
  }

  get customerSegmentItemsWithData(): CanvasItem[] {
    return this.customerSegmentItems.filter(item => this.hasCardData(item));
  }

  get solutionTenantsDataWithData(): CanvasItem[] {
    return this.solutionTenantsData.filter(item => this.hasCardData(item));
  }

  get firstRowProblemCardWithData(): CanvasItem[] {
    const filteredItems = this.firstRowProblemCard.filter(item => this.hasCardData(item));
    return this.getTrimmedCanvasItems(filteredItems);
  }

  get firstRowSolutionCardWithData(): CanvasItem[] {
    const filteredItems = this.firstRowSolutionCard.filter(item => this.hasCardData(item));
    return this.getTrimmedCanvasItems(filteredItems);
  }

  get keyPartnersDataWithData(): CanvasItem[] {
    return this.keyPartnersData.filter(item => this.hasCardData(item));
  }

  get valuePropositionDataWithData(): CanvasItem[] {
    return this.valuePropositionData.filter(item => this.hasCardData(item));
  }

  get keyMetricsDataWithData(): CanvasItem[] {
    return this.keyMetricsData.filter(item => this.hasCardData(item));
  }

  get alternativesDataWithData(): CanvasItem[] {
    return this.alternativesData.filter(item => this.hasCardData(item));
  }

  // Action handlers
  onEdit(item: CanvasItem): void {
    console.log('Edit:', item.title);
    this.openEditModal(item);
  }

  onDelete(item: CanvasItem): void {
    console.log('Delete:', item.title);
    // Implement delete functionality if needed
  }

  // Modal Methods
  openEditModal(item: CanvasItem): void {
    console.log('🔓 Opening edit modal for:', item.title);
    this.selectedItemForEdit = item;
    this.editableItemData = [...item.data];
    this.regeneratePrompt = '';
    this.isEditModalOpen = true;

    // Notify service that an edit modal is open
    // this.splitScreenService.setEditModalOpen(true);

    // Close split screen to show full-screen modal view
    this.splitScreenService.closeSplitScreen();
    console.log('🔒 Split screen closed for modal editing');
  }

  closeEditModal(): void {
    console.log('🔒 Closing edit modal');
    this.isEditModalOpen = false;
    this.selectedItemForEdit = null;
    this.editableItemData = [];
    this.regeneratePrompt = '';

    // Notify service that edit modal is closed
    // this.splitScreenService.setEditModalOpen(false);

    // Restore split screen when modal closes
    this.splitScreenService.openSplitScreen();
    console.log('🔓 Split screen restored after modal close');

    this.cdRef.detectChanges();
  }

  updateUnderstandingItem(event: Event): void {
    if (this.selectedItemForEdit) {
      // Update via service
      this.understandingDataService.updateCanvasItem(
        this.selectedItemForEdit.id,
        [...this.editableItemData],
      );

      // Handle regenerate prompt
      if (this.regeneratePrompt) {
        console.log('Regenerate with prompt:', this.regeneratePrompt);
        this.loadingStates['submit'] = true;
        setTimeout(() => {
          this.loadingStates['submit'] = false;
          console.log('Submit completed:', event);
        }, 2000);
        this.regeneratePrompt = '';
      }
      this.closeEditModal();
    }
  }

  // Methods for managing editableItemData in the modal
  addEditableDataItem(): void {
    this.editableItemData.push('');
    this.cdRef.detectChanges();
    setTimeout(() => {
      const inputs = document.querySelectorAll('.edit-data-item-input');
      const lastInput = inputs[inputs.length - 1] as HTMLInputElement;
      if (lastInput) {
        lastInput.focus();
      }
    });
  }

  removeEditableDataItem(index: number): void {
    this.editableItemData.splice(index, 1);
  }

  trackByFn(index: number, _item: any): any {
    return index;
  }
}
