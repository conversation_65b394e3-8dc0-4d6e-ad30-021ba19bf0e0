<div class="container-fluid">
  <!-- Header Section -->
  <div class="d-flex justify-content-end align-items-center mb-1">
    <button class="btn-add-new m-2" (click)="openAddModal()">
      Add new <span class="plus-icon">+</span>
    </button>
  </div>

  <!-- Personas Carousel/Grid with Navigation -->
  <div class="carousel-container" *ngIf="personas.length > 0; else noPersonas">
    <!-- Left Navigation Button -->
    <button
      class="carousel-nav-btn carousel-nav-left"
      [disabled]="currentPage === 1"
      (click)="previousPage()"
      *ngIf="totalPages > 1"
      aria-label="Previous page"
    >
      <awe-icons class="nav-icon" iconName="awe_chevron_left"></awe-icons>
    </button>

    <!-- Cards Container -->
    <div class="cards-container gap-3 d-flex justify-content-center align-content-center flex-wrap">
      <div
        *ngFor="let persona of currentPagePersonas; trackBy: trackByPersona"
        class="persona-card-wrapper px-1"
        (click)="onPersonaClick(persona)"
        style="cursor: pointer"
      >
        <awe-card [applyBodyPadding]="false" class="persona-card h-100">
          <!-- Card Body -->
          <div class="p-4 d-flex flex-column h-100">
            <!-- Profile Section -->
            <div class="profile-section texcenter">
              <div
                class="card-actions"
              >
                <div
                  class="three-dots-btn p-2"
                  (click)="$event.stopPropagation(); openDeleteModal(persona)"
                  aria-label="Delete Persona"
                >
                  <awe-icons iconName="awe_trash" iconSize="20px"></awe-icons>
                </div>
              </div>
              <div class="avatar-wrapper mx-auto">
                <img
                  [src]="persona.avatar"
                  [alt]="persona.name || persona.role"
                  class="avatar"
                />
              </div>
              <h2 class="d-flex align-items-center justify-content-center">{{ persona.name || persona.role }}</h2>
            </div>
            <!-- Info Section -->
            <!-- <div class="info-section">
              <div class="info-row">
                <span>Age</span><span>{{ persona.age }}</span>
              </div>
              <div class="info-row">
                <span>Education</span><span>{{ persona.education }}</span>
              </div>
              <div class="info-row">
                <span>Status</span><span>{{ persona.status }}</span>
              </div>
              <div class="info-row">
                <span>Location</span><span>{{ persona.location }}</span>
              </div>
              <div class="info-row">
                <span>Tech Literacy</span><span>{{ persona.techLiteracy }}</span>
              </div>
            </div> -->

            <!-- Quote Section -->
            <div class="quote-section mt-3">
              <img [src]="colonIcon" alt="Quote" class="quote-icon" />
              <p class="quote-text">{{ persona.quote }}</p>
            </div>

            <!-- Personality Section (grows to fill space) -->
            <div class="personality-section mt-">
              <h3 class="personality-title">Personality</h3>
              <div class="personality-tags">
                <span
                  *ngFor="let trait of persona.personality"
                  class="personality-tag"
                  >{{ trait }}</span
                >
              </div>
            </div>
          </div>
        </awe-card>
      </div>
    </div>

    <!-- Right Navigation Button -->
    <button
      class="carousel-nav-btn carousel-nav-right"
      [disabled]="currentPage === totalPages"
      (click)="nextPage()"
      *ngIf="totalPages > 1"
      aria-label="Next page"
    >
      <awe-icons class="nav-icon" iconName="awe_chevron_right"></awe-icons>
    </button>
  </div>

  <ng-template #noPersonas>
    <div class="text-center p-5 text-muted">
      No user personas found. Click 'Add new' to start.
    </div>
  </ng-template>


</div>

<!-- MODALS (Add/Delete) -->
<!-- New Add/Edit Modal -->
<awe-modal
  [isOpen]="isModalOpen"
  [showHeader]="true"
  [showFooter]="true"
  [width]="'600px'"
  [maxWidth]="'90vw'"
  (closed)="closeModal()"
>
  <div awe-modal-header class="mb-2">
    <awe-heading variant="s1" type="bold">{{
      modalMode === "edit" ? "Edit Persona" : "Add New Persona"
    }}</awe-heading>
  </div>
  <div awe-modal-body>
    <div class="modal-form" *ngIf="editData">
      <div class="profile-data row g-3">
        <div class="col-md-12 inp-container">
          <div class="label"><label>Name:</label></div>
          <div class="input-wrapper">
            <awe-input
              variant="fluid"
              [(ngModel)]="editData.name"
              placeholder="Enter name"
            ></awe-input>
          </div>
        </div>
        <div class="col-md-12 inp-container">
          <div class="label"><label>Role:</label></div>
          <div class="input-wrapper">
            <awe-input
              variant="fluid"
              [(ngModel)]="editData.role"
              placeholder="Enter role"
            ></awe-input>
          </div>
        </div>
        <div class="col-md-12 inp-container">
          <div class="label"><label>Age:</label></div>
          <div class="input-wrapper">
            <awe-input
              variant="fluid"
              [(ngModel)]="editData.age"
              type="number"
              placeholder="Enter age"
            ></awe-input>
          </div>
        </div>
        <div class="col-md-12 inp-container">
          <div class="label"><label>Education:</label></div>
          <div class="input-wrapper">
            <awe-input
              variant="fluid"
              [(ngModel)]="editData.education"
              placeholder="Enter education"
            ></awe-input>
          </div>
        </div>
        <div class="col-md-12 inp-container">
          <div class="label"><label>Status:</label></div>
          <div class="input-wrapper">
            <awe-input
              variant="fluid"
              [(ngModel)]="editData.status"
              placeholder="Enter status"
            ></awe-input>
          </div>
        </div>
        <div class="col-md-12 inp-container">
          <div class="label"><label>Location:</label></div>
          <div class="input-wrapper">
            <awe-input
              variant="fluid"
              [(ngModel)]="editData.location"
              placeholder="Enter location"
            ></awe-input>
          </div>
        </div>
        <div class="col-md-12 inp-container">
          <div class="label"><label>Tech Literacy:</label></div>
          <div class="input-wrapper">
            <awe-input
              variant="fluid"
              [(ngModel)]="editData.techLiteracy"
              placeholder="Enter tech literacy"
            ></awe-input>
          </div>
        </div>
        <div class="col-md-12 inp-container">
          <div class="label"><label>Quote:</label></div>
          <div class="input-wrapper">
            <awe-input
              variant="fluid"
              [(ngModel)]="editData.quote"
              placeholder="Enter quote"
            ></awe-input>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div awe-modal-footer class="delete-modal">
    <div class="action-btn gap-4 mt-4 d-flex w-100 justify-content-center">
      <button type="button" class="btn-cancel px-5" (click)="closeModal()">
        Cancel
      </button>
      <button type="button" class="btn-delete px-5" (click)="saveCardData()">
        {{ modalMode === "edit" ? "Update" : "Add Persona" }}
      </button>
    </div>
  </div>
</awe-modal>

<!-- Delete Confirmation Modal -->
<awe-modal
  class="delete-modal"
  [isOpen]="isDeleteModalOpen"
  modalClass="delete-modal-custom"
>
  <div
    awe-modal-body
    class="d-flex gap-3 mb-3 flex-column align-items-center justify-content-center py-2"
  >
    <div
      class="delete-icon-wrapper d-flex justify-content-center align-items-center mb-3"
    >
      <img [src]="awe_delete" alt="Delete" class="delete-icon" />
    </div>
    <awe-heading variant="s2" type="regular" class="text-center mb-4"
      >Are you sure you want to delete this?</awe-heading
    >
    <div class="action-btn gap-4 mt-4 d-flex w-100 justify-content-center">
      <button
        type="button"
        class="btn-cancel px-5"
        (click)="closeDeleteModal()"
      >
        Cancel
      </button>
      <button
        type="button"
        class="btn-delete px-5"
        (click)="confirmDeletePersona()"
      >
        Delete
      </button>
    </div>
  </div>
</awe-modal>
