:host {
  display: block;
  font-family:
    "<PERSON><PERSON><PERSON>",
    "<PERSON>",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    <PERSON><PERSON>,
    "Helvetica Neue",
    <PERSON><PERSON>,
    sans-serif;
}

.container-fluid {
  margin: 0 0;
  width: 100%;
  padding: 0 24px;
}

.btn-add-new {
  background-color: rgba(125, 99, 246, 1);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  &:hover {
    background-color: darken(#8a3ffc, 8%);
  }
}

.persona-card-wrapper {
  display: flex; /* Make wrapper a flex container */
  flex-direction: column;
  width: 20%; /* Ensure it takes full width of the column */
  flex: 1 0 200px; /* Allow cards to grow and shrink, but have a minimum width */
  max-width: 365px; /* Set a maximum width for the cards */
  min-width: 300px; /* Set a minimum width for the cards */
}
 .profile-section {
  height: 100%;
  
  .card-actions{
    display:none;
    position: absolute;
    top: 16px;
    right: 16px;
    z-index: 10;

  }
  &:hover{

   awe-card{
    
   } 
    .card-actions{

      display: inline-block;
      // position: absolute;
      // top: 16px;
      // right: 16px;
      // z-index: 10;
    }
  }
}



.persona-card {
  --awe-card-border: none;
  --awe-card-border-radius: 20px;
  --awe-card-box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.07), 0 2px 4px -2px rgba(0, 0, 0, 0.07);
  --awe-card-background: #ffffff;
  transition: all 0.2s ease-in-out;
 /* Ensure card fills the wrapper */

  &:hover {
    --awe-card-box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
  }
}

.three-dots-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  opacity: 0.5;
  transition: opacity 0.2s ease;
  border-radius: 4px;
}

.profile-section {
  .avatar-wrapper {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    background: linear-gradient(135deg, #fbcfe8, #f9a8d4);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5px;
    box-shadow: 0 4px 12px rgba(252, 165, 165, 0.4);
  }

  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
  }

  .role-title {
    margin-top: 1rem;
    font-size: 1.5rem;
    font-weight: 600;
    color: #1d4ed8;
  }
}

.info-section {
  margin: 24px 0;
  display: flex;
  flex-direction: column;
  gap: 8px;

  .info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    padding: 10px 16px;
    border-radius: 12px;
    background-color: rgba(245, 245, 250, 1);

    .info-label {
      color: #6b7280;
    }
    .info-value {
      color: #111827;
      font-weight: 500;
    }
  }
}

.quote-section {
  background-color: rgba(245, 245, 250, 1);
  border-radius: 16px;
  padding: 1rem;
  position: relative;
  margin-top: auto;
  flex-shrink: 0;

  .quote-icon {
    position: absolute;
    top: 1rem;
    left: 1rem;
    width: 20px;
    height: 20px;
    opacity: 0.5;
  }

  .quote-text {
    font-size: 0.875rem;
    line-height: 1.5;
    color: #4338ca;
    padding-left: 28px;
    margin-bottom: 0;
  }
}

.personality-section {
  margin-top: 24px;
  flex-shrink: 0;

  .personality-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: #374151;
  }

  .personality-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .personality-tag {
      padding: 6px 14px;
      border-radius: 16px;
      font-size: 0.75rem;
      font-weight: 500;

      &:nth-child(3n + 1) {
        background-color: #e0e7ff;
        color: rgba(101, 102, 205, 1);
      }
      &:nth-child(3n + 2) {
        background-color: #f3e8ff;
        color: rgba(101, 102, 205, 1);
      }
      &:nth-child(3n + 3) {
        background-color: #fce7f3;
        color: rgba(101, 102, 205, 1);
      }
    }
  }
}

/* Carousel Container with Navigation */
.carousel-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  position: relative;
  width: 100%;
  min-height: 500px; /* Ensure consistent height */
}

.cards-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-content: center;
  flex-wrap: wrap;
  gap: 1rem;
  max-width: calc(100% - 120px); /* Account for navigation buttons */
}

/* Navigation Buttons */
.carousel-nav-btn {
  background: white;
  border: 1px solid #e5e7eb;
  width: 60px;
  height: 150px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #6b7280;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  z-index: 10;
  flex-shrink: 0;

  .nav-icon {
    font-size: 20px;
    transition: transform 0.2s ease;
  }

  &:hover:not(:disabled) {
    border-color: #7c3aed;
    color: #7c3aed;
    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.2);
    // transform: scale(1.05);
  }

  &:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    background-color: #f9fafb;
    border-color: #e5e7eb;
    color: #9ca3af;

    &:hover {
     
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

.carousel-nav-left {
  position: absolute;
  left: 0;
  top: 50%;
  // transform: translateY(-50%);
}

.carousel-nav-right {
  position: absolute;
  right: 0;
  top: 50%;
  // transform: translateY(-50%);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .carousel-container {
    gap: 15px;
  }

  .cards-container {
    max-width: calc(100% - 100px);
  }

  .carousel-nav-btn {
    width: 42px;
    height: 42px;

    .nav-icon {
      font-size: 18px;
    }
  }
}

@media (max-width: 768px) {
  .carousel-container {
    gap: 10px;
    min-height: 450px;
  }

  .cards-container {
    max-width: calc(100% - 80px);
  }

  .carousel-nav-btn {
    width: 36px;
    height: 36px;

    .nav-icon {
      font-size: 16px;
    }
  }

  .carousel-nav-left {
    left: -5px;
  }

  .carousel-nav-right {
    right: -5px;
  }
}

@media (max-width: 480px) {
  .carousel-container {
    gap: 5px;
    min-height: 400px;
  }

  .cards-container {
    max-width: calc(100% - 60px);
  }

  .carousel-nav-btn {
    width: 32px;
    height: 32px;

    .nav-icon {
      font-size: 14px;
    }
  }

  .carousel-nav-left {
    left: -10px;
  }

  .carousel-nav-right {
    right: -10px;
  }

  .persona-card-wrapper {
    min-width: 280px;
    max-width: 320px;
  }
}

/* Modal Styles */
.delete-modal {
  button {
    border-radius: 50px;
    padding: 12px 24px;
    border: none;
  }
  .btn-cancel {
    border: 1px solid var(--Primary-500, #7c3aed);
    background: var(--Neutral-Neutral-colors-Solid, #fff);
  }
  .btn-delete {
    background: var(--Primary-500, #7c3aed);
    border: 1px solid var(--Primary-500, #7c3aed);
    color: #fff;
  }
}

.inp-container {
  display: flex;
  align-items: center;
  gap: 10px;
  .label {
    width: 100px;
    flex-shrink: 0;
  }
  .input-wrapper {
    flex-grow: 1;
  }
}
