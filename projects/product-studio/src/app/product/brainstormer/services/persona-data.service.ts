import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface Skill {
  name: string;
  level: number;
}

export interface PersonaData {
  id: string;
  name: string;
  role: string;
  age: number;
  education: string;
  status: string;
  location: string;
  techLiteracy: string;
  quote: string;
  personality: string[];
  painPoints: string[];
  goals: string[];
  motivation: string[];
  expectations: string[];
  skills: Skill[];
  devices: string[];
  avatar: string;
  gender?: string; // Added gender field for avatar mapping
}

export interface PersonaCard {
  id: string;
  title: string;
  type:
    | 'painPoints'
    | 'goals'
    | 'motivation'
    | 'expectations'
    | 'skills'
    | 'devices'
    | 'profile';
  data: string[] | Skill[] | string[] | any;
  icon?: string;
}

@Injectable({
  providedIn: 'root',
})
export class PersonaDataService {

  // Male avatar images
  private maleAvatars: string[] = [
    'assets/avatars/designer-avatar.svg',
    'assets/avatars/teacher-avatar.svg',
    'assets/avatars/designer-avatar.svg',
    'assets/avatars/designer-avatar.svg',
    'assets/avatars/teacher-avatar.svg',
    'assets/avatars/teacher-avatar.svg',
    
  ];

  // Female avatar images
  private femaleAvatars: string[] = [
    'assets/avatars/developer-avatar.svg',
    'assets/avatars/developer-avatar.svg',
    'assets/avatars/sales-avatar.svg',
    'assets/avatars/developer-avatar.svg',
  ];

  // Fallback/neutral avatars for non-binary or unspecified gender
  private neutralAvatars: string[] = [
    'assets/avatars/designer-avatar.svg',
    'assets/avatars/teacher-avatar.svg',
    'assets/avatars/teacher-avatar.svg',
     'assets/avatars/developer-avatar.svg',
    'assets/avatars/sales-avatar.svg',
    'assets/avatars/developer-avatar.svg',
  ];

  // Initialize with empty array - data will come from API
  private personasSubject = new BehaviorSubject<PersonaData[]>([]);
  private selectedPersonaSubject = new BehaviorSubject<PersonaData | null>(
    null,
  );

  personas$ = this.personasSubject.asObservable();
  selectedPersona$ = this.selectedPersonaSubject.asObservable();

  constructor() {
  
  }

  // Persona CRUD operations
  getPersonas(): PersonaData[] {
    return this.personasSubject.value;
  }

  getPersonaById(id: string): PersonaData | undefined {
    return this.personasSubject.value.find((persona) => persona.id === id);
  }

  addPersona(persona: Omit<PersonaData, 'id'>): void {
    const newPersona: PersonaData = {
      ...persona,
      id: this.generateId(),
    };
    const currentPersonas = this.personasSubject.value;
    this.personasSubject.next([...currentPersonas, newPersona]);
  }

  updatePersona(personaId: string, updates: Partial<PersonaData>): void {
    const currentPersonas = this.personasSubject.value;
    const updatedPersonas = currentPersonas.map((persona) =>
      persona.id === personaId ? { ...persona, ...updates } : persona,
    );
    this.personasSubject.next(updatedPersonas);

    // Update selected persona if it's the one being updated
    const selectedPersona = this.selectedPersonaSubject.value;
    if (selectedPersona && selectedPersona.id === personaId) {
      this.selectedPersonaSubject.next({ ...selectedPersona, ...updates });
    }
  }

  deletePersona(personaId: string): void {
    const currentPersonas = this.personasSubject.value;
    const filteredPersonas = currentPersonas.filter(
      (persona) => persona.id !== personaId,
    );
    this.personasSubject.next(filteredPersonas);

    // If deleted persona was selected, select the first available persona
    const selectedPersona = this.selectedPersonaSubject.value;
    if (selectedPersona && selectedPersona.id === personaId) {
      this.selectedPersonaSubject.next(
        filteredPersonas.length > 0 ? filteredPersonas[0] : null,
      );
    }
  }

  // Selected persona operations
  setSelectedPersona(personaId: string): void {
    const persona = this.getPersonaById(personaId);
    if (persona) {
      this.selectedPersonaSubject.next(persona);
    }
  }

  getSelectedPersona(): PersonaData | null {
    return this.selectedPersonaSubject.value;
  }

  // Card-specific operations for persona details
  updatePersonaCard(
    personaId: string,
    cardType: PersonaCard['type'],
    data: any,
  ): void {
    if (cardType === 'profile') {
      // Handle profile separately since it's not a direct property
      this.updatePersonaProfile(personaId, data);
      return;
    }

    const updates: Partial<PersonaData> = {};
    (updates as any)[cardType] = data;
    this.updatePersona(personaId, updates);
  }

  // Profile-specific update method
  updatePersonaProfile(personaId: string, profileData: any): void {
    // If gender is updated, regenerate avatar based on new gender
    let avatarUrl = profileData.avatar;
    if (profileData.gender && !profileData.avatar) {
      const index = this.personasSubject.value.findIndex(p => p.id === personaId);
      avatarUrl = this.getGenderBasedAvatar(profileData.gender, profileData.role, index);
    }

    const updates: Partial<PersonaData> = {
      name: profileData.name,
      role: profileData.role,
      age: profileData.age,
      education: profileData.education,
      status: profileData.status,
      location: profileData.location,
      techLiteracy: profileData.techLiteracy,
      quote: profileData.quote,
      gender: profileData.gender,
      avatar: avatarUrl,
      personality: profileData.personality,
    };
    this.updatePersona(personaId, updates);
  }

  // Utility methods
  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substring(2, 11);
  }

  /**
   * Get gender-based avatar for persona
   * @param gender - The gender of the persona ('male', 'female', or other)
   * @param role - The role of the persona (for role-based selection within gender)
   * @param index - Index for consistent avatar assignment
   * @returns Avatar path string
   */
  getGenderBasedAvatar(gender?: string, role?: string, index: number = 0): string {
    const normalizedGender = gender?.toLowerCase();

    // Select avatar array based on gender
    let avatarArray: string[];
    if (normalizedGender === 'male') {
      avatarArray = this.maleAvatars;
    } else if (normalizedGender === 'female') {
      avatarArray = this.femaleAvatars;
    } else {
      // Use neutral avatars for non-binary, unspecified, or other genders
      avatarArray = this.neutralAvatars;
    }

    // Try to match role to specific avatar within the gender category
    if (role) {
      const roleIndex = this.getRoleBasedAvatarIndex(role);
      if (roleIndex < avatarArray.length) {
        return avatarArray[roleIndex];
      }
    }

    // Fallback to index-based selection (ensures consistency)
    const avatarIndex = index % avatarArray.length;
    return avatarArray[avatarIndex];
  }

  /**
   * Get role-based index for avatar selection
   * @param role - The persona role
   * @returns Index corresponding to role type
   */
  private getRoleBasedAvatarIndex(role: string): number {
    const r = role.toLowerCase();
    if (r.includes('designer')) return 0;
    if (r.includes('developer')) return 1;
    if (r.includes('manager')) return 2;
    if (r.includes('sales')) return 3;
    if (r.includes('teacher')) return 4;
    if (r.includes('student')) return 5;
    if (r.includes('entrepreneur')) return 6;
    if (r.includes('analyst')) return 7;
    return 0; // Default to first avatar
  }

  getDefaultAvatar(role: string): string {
    const r = role.toLowerCase();
    if (r.includes('manager')) return '/svgs/manager-avatar.svg';
    if (r.includes('designer')) return '/svgs/designer-avatar.svg';
    if (r.includes('teacher')) return '/svgs/teacher-avatar.svg';
    if (r.includes('developer')) return '/svgs/developer-avatar.svg';
    return '/svgs/manager-avatar.svg';
  }

  // Convert legacy UserPersona to PersonaData
  convertUserPersonaToPersonaData(userPersona: any, index: number = 0): Omit<PersonaData, 'id'> {
    // Use gender-based avatar if available, otherwise fallback to default
    const avatarUrl = userPersona.avatar ||
      this.getGenderBasedAvatar(userPersona.gender, userPersona.role, index);

    return {
      name: userPersona.role || 'Unknown',
      role: userPersona.role || 'Unknown',
      age: userPersona.age || 0,
      education: userPersona.education || '',
      status: userPersona.status || '',
      location: userPersona.location || '',
      techLiteracy: userPersona.techLiteracy || 'Medium',
      quote: userPersona.quote || '',
      personality: userPersona.personality || [],
      gender: userPersona.gender || 'unspecified',
      avatar: avatarUrl,
      painPoints: [],
      goals: [],
      motivation: [],
      expectations: [],
      skills: [],
      devices: ['mobile'],
    };
  }

  /**
   * Update personas from API response
   * Handles the new API response structure with proper field mapping and gender-based avatars
   */
  updatePersonasFromAPI(apiPersonas: any[]): void {
    console.log('Processing API personas:', apiPersonas);

    const personas: PersonaData[] = apiPersonas.map((persona, index) => {
      // Extract name from the full name string (e.g., "Alex Chen, the Freelance Designer" -> "Alex Chen")
      const extractedName = persona.name
        ? persona.name.split(',')[0].trim()
        : `Persona ${index + 1}`;

      // Use gender-based avatar mapping if gender is provided, otherwise fallback to default
      const avatarUrl = persona.avatar ||
        this.getGenderBasedAvatar(persona.gender, persona.role, index);

      return {
        id: `persona-${index + 1}`,
        name: extractedName,
        role: persona.role || 'Unknown Role',
        age: persona.age || 30,
        education: persona.education || 'Not specified',
        status: persona.status || 'Not specified',
        location: persona.location || 'Not specified',
        techLiteracy: persona.tech_literacy || 'Medium',
        gender: persona.gender || 'unspecified', // Store gender for consistency
        avatar: avatarUrl,
        quote:
          persona.quote ||
          `"I am ${extractedName} and I need solutions that work for me."`,
        personality: persona.personality || [],
        painPoints: persona.pain_points || [],
        goals: persona.goals || [],
        motivation: persona.motivation || [],
        expectations: persona.expectations || [],
        skills: this.mapSkills(persona.skills || []),
        devices: persona.devices || ['mobile'],
      };
    });

    console.log('Processed personas with gender-based avatars:', personas);
    this.personasSubject.next(personas);

    // Set the first persona as selected by default
    if (personas.length > 0) {
      this.selectedPersonaSubject.next(personas[0]);
    }
  }

  /**
   * Map API skills to internal Skill interface
   */
  private mapSkills(apiSkills: any[]): Skill[] {
    return apiSkills.map((skill) => ({
      name: skill.name || 'Unknown Skill',
      level: skill.level || 50,
    }));
  }
}
