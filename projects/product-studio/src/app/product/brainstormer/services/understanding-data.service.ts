import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';


export interface CanvasItem {
  id: string;
  title: string;
  icon: string;
  iconBg: string;
  data: string[];
}

@Injectable({
  providedIn: 'root'
})
export class UnderstandingDataService {
  // Initialize with empty array - data will come from API
  private businessModelCanvasSubject = new BehaviorSubject<CanvasItem[]>([]);
  public businessModelCanvas$ = this.businessModelCanvasSubject.asObservable();

  constructor() {}

  /**
   * Update the business model canvas with new data
   */
  updateBusinessModelCanvas(canvasItems: CanvasItem[]): void {
    this.businessModelCanvasSubject.next(canvasItems);
  }

 

  // Get all canvas items
  getBusinessModelCanvas(): CanvasItem[] {
    return this.businessModelCanvasSubject.value;
  }

  // Get specific canvas item by ID
  getCanvasItemById(id: string): CanvasItem | undefined {
    return this.businessModelCanvasSubject.value.find(item => item.id === id);
  }

  // Update canvas item data
  updateCanvasItem(id: string, newData: string[]): void {
    const currentCanvas = this.businessModelCanvasSubject.value;
    const itemIndex = currentCanvas.findIndex(item => item.id === id);

    if (itemIndex !== -1) {
      const updatedCanvas = [...currentCanvas];
      updatedCanvas[itemIndex] = {
        ...updatedCanvas[itemIndex],
        data: [...newData]
      };
      this.businessModelCanvasSubject.next(updatedCanvas);
    }
  }

  // Add data item to specific canvas item
  addDataItem(canvasId: string, dataItem: string): void {
    const currentCanvas = this.businessModelCanvasSubject.value;
    const itemIndex = currentCanvas.findIndex(item => item.id === canvasId);

    if (itemIndex !== -1) {
      const updatedCanvas = [...currentCanvas];
      updatedCanvas[itemIndex] = {
        ...updatedCanvas[itemIndex],
        data: [...updatedCanvas[itemIndex].data, dataItem]
      };
      this.businessModelCanvasSubject.next(updatedCanvas);
    }
  }

  // Remove data item from specific canvas item
  removeDataItem(canvasId: string, dataIndex: number): void {
    const currentCanvas = this.businessModelCanvasSubject.value;
    const itemIndex = currentCanvas.findIndex(item => item.id === canvasId);

    if (itemIndex !== -1) {
      const updatedCanvas = [...currentCanvas];
      const newData = [...updatedCanvas[itemIndex].data];
      newData.splice(dataIndex, 1);

      updatedCanvas[itemIndex] = {
        ...updatedCanvas[itemIndex],
        data: newData
      };
      this.businessModelCanvasSubject.next(updatedCanvas);
    }
  }

  // Get grouped data for specific layouts
  getFirstRowProblemData(): CanvasItem[] {
    const canvas = this.getBusinessModelCanvas();
    return [canvas[0]]; // Problem
  }

  getFirstRowSolutionCard(): CanvasItem[] {
    const canvas = this.getBusinessModelCanvas();
    return [canvas[5]]; // Solution
  }

  getKeyPartnersData(): CanvasItem[] {
    const canvas = this.getBusinessModelCanvas();
    return [canvas[1]]; // Key Partners, Value Proposition
  }
    getValuePropositionData(): CanvasItem[] {
    const canvas = this.getBusinessModelCanvas();
    return [canvas[2]]; // Key Partners, Value Proposition
  }

  getKeyMetricsAlternativesData(): CanvasItem[] {
    const canvas = this.getBusinessModelCanvas();
    return [canvas[4]]; // Key Metrics, Alternatives
  }

  getAlternativesData(): CanvasItem[] {
    const canvas = this.getBusinessModelCanvas();
    return [canvas[6]]; // Key Metrics, Alternatives
  }


  getCustomerSegmentItems(): CanvasItem[] {
    const canvas = this.getBusinessModelCanvas();
    return [canvas[3],canvas[9]]; // Customer Segments
  }

  getCostRevenueItems(): CanvasItem[] {
    const canvas = this.getBusinessModelCanvas();
    return [canvas[7], canvas[8]]; // Cost Structure, Revenue Streams
  }

  // Reset to empty data - no more mock data
  resetToDefaults(): void {
    this.businessModelCanvasSubject.next([]);
  }
}