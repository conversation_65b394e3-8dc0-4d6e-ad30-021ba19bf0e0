import { Component, OnInit, signal, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
  FormControl,
} from '@angular/forms';
import { Router } from '@angular/router';

import { HttpErrorResponse } from '@angular/common/http';
import logintext from './login.json';
import {
  AvaTextboxComponent,
  IconComponent,
  ButtonComponent,
  PopupComponent,
} from '@ava/play-comp-library';
import { AuthService } from '@shared/auth/services/auth.service';
import { TokenStorageService } from '../../../auth/services/token-storage.service';

export interface SavedAccount {
  email: string;
  profilePic?: string;
  isSelected?: boolean;
}

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    AvaTextboxComponent,
    IconComponent,
    ButtonComponent,
    PopupComponent,
  ],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit {
  loginMode = signal<'sso' | 'form'>('sso');
  isLoading = signal(false);
  showPassword = signal(false);
  loginForm: FormGroup;
  errorMessage = signal<string | null>(null);
  showErrorPopup = signal(false);
  popupMessage = signal('');
  public labels: any = logintext;

  private authService = inject(AuthService);
  private tokenStorage = inject(TokenStorageService);

  constructor(
    private fb: FormBuilder,
    private router: Router,
  ) {
    this.loginForm = this.fb.group({
      username: ['', [Validators.required]],
      password: ['', Validators.required],
      keepSignedIn: [false],
    });
  }

  ngOnInit(): void {
    const storedLoginType = this.tokenStorage.getLoginType();
    if (storedLoginType === 'sso' || storedLoginType === 'basic') {
      this.loginMode.set(storedLoginType === 'basic' ? 'form' : 'sso');
    } else {
      this.loginMode.set('sso');
    }
  }

  getControl(name: string): FormControl {
    return this.loginForm.get(name) as FormControl;
  }

  onBasicLogin(): void {
    if (this.loginForm.valid) {
      this.isLoading.set(true);
      this.errorMessage.set(null);

      const { username, password } = this.loginForm.value;
      this.authService.basicLoginWithCredentials(username, password).subscribe({
        next: () => {
          this.tokenStorage.storeLoginType('basic');
          const redirectUrl = this.authService.getPostLoginRedirectUrl();
          this.router.navigate([redirectUrl]);
        },
        error: (error: HttpErrorResponse) => {
          this.isLoading.set(false);
          let errorMessage = 'Invalid username or password. Please try again.';
          
          if (error.error && typeof error.error === 'string') {
            errorMessage = error.error;
          }
          this.popupMessage.set(errorMessage);
          this.showErrorPopup.set(true);
          console.error('Login failed:', error);
        },
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  onCompanyLogin(): void {
    this.isLoading.set(true);
    this.errorMessage.set(null);

    this.authService.loginSSO().subscribe({
      next: () => {
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error('Login failed:', error);
        this.errorMessage.set('Failed to initiate login with company account.');
        this.isLoading.set(false);
      },
    });
  }

  togglePasswordVisibility(): void {
    this.showPassword.set(!this.showPassword());
  }

  clearInput(fieldName: string): void {
    this.loginForm.get(fieldName)?.setValue('');
    this.loginForm.get(fieldName)?.markAsTouched();
  }

  clearUsername(): void {
    this.clearInput('username');
  }

  private markFormGroupTouched(): void {
    Object.keys(this.loginForm.controls).forEach((key) => {
      const control = this.loginForm.get(key);
      control?.markAsTouched();
    });
  }

  getFieldError(fieldName: string): string {
    const field = this.loginForm.get(fieldName);
    if (field?.touched && field?.errors) {
      if (field.errors['required']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
      }
      if (field.errors['email']) {
        return 'Please enter a valid email address';
      }
      if (field.errors['minlength']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
    }
    return '';
  }

  // Check if both username and password fields are filled
  areFieldsFilled(): boolean {
    const username = this.loginForm.get('username')?.value;
    const password = this.loginForm.get('password')?.value;
    return !!username && !!password && username.trim() !== '' && password.trim() !== '';
  }

  onForgotPassword(): void {
    this.router.navigate(['/forgot-password']);
  }

  onTroubleSigningIn(): void {
    this.router.navigate(['/help']);
  }
}
