# Shared App Header Component

A reusable header component that can be used across all projects (<PERSON><PERSON><PERSON>, <PERSON>, Experience Studio, Product Studio) with configurable navigation and features.

## Features

- **Configurable Navigation**: Supports different navigation items per project
- **Dropdown Support**: Multi-level navigation with dropdown menus
- **Theme Toggle**: Optional theme switching functionality
- **Profile Management**: User profile dropdown with logout
- **Organization Selector**: Optional organization/project selection (mainly for Console)
- **Responsive Design**: Works on desktop and mobile devices
- **Accessibility**: Keyboard navigation and screen reader support

## Basic Usage

### 1. Import and Configure

```typescript
import { SharedAppHeaderComponent, HeaderConfig } from '@shared/components/app-header/app-header.component';

// Create your navigation configuration
const headerConfig: HeaderConfig = {
  logoSrc: 'assets/images/logo.svg',
  navItems: [
    {
      label: 'Dashboard',
      route: '/dashboard',
      selected: true,
      hasDropdown: false,
      icon: 'assets/icons/dashboard.svg',
    },
    {
      label: 'Features',
      route: '/features',
      selected: false,
      hasDropdown: true,
      icon: 'assets/icons/features.svg',
      dropdownItems: [
        {
          label: 'Feature 1',
          description: 'Description of feature 1',
          route: '/features/feature1',
          icon: 'assets/icons/feature1.svg',
        }
      ]
    }
  ],
  showOrgSelector: false,
  showThemeToggle: true,
  showProfileDropdown: true,
  projectName: 'My App',
  redirectUrl: '/dashboard'
};
```

### 2. Add to Component

```typescript
@Component({
  selector: 'app-root',
  imports: [SharedAppHeaderComponent],
  template: `
    <shared-app-header 
      [config]="headerConfig"
      (navigationEvent)="onNavigation($event)"
      (profileAction)="onProfileAction($event)"
      (themeToggle)="onThemeToggle($event)">
    </shared-app-header>
  `
})
export class AppComponent {
  headerConfig = headerConfig;

  onNavigation(route: string): void {
    console.log('Navigating to:', route);
  }

  onProfileAction(action: string): void {
    if (action === 'logout') {
      // Handle logout
    }
  }

  onThemeToggle(theme: 'light' | 'dark'): void {
    // Handle theme change
  }
}
```

## Configuration Options

### HeaderConfig Interface

```typescript
interface HeaderConfig {
  logoSrc: string;                    // Path to logo image
  navItems: SharedNavItem[];          // Navigation items array
  showOrgSelector?: boolean;          // Show organization selector (default: false)
  showThemeToggle?: boolean;          // Show theme toggle button (default: true)
  showProfileDropdown?: boolean;      // Show profile dropdown (default: true)
  projectName?: string;               // Project name for identification (default: 'Application')
  redirectUrl?: string;               // Default redirect URL (default: '/')
}
```

### SharedNavItem Interface

```typescript
interface SharedNavItem {
  label: string;                      // Display label
  route: string;                      // Navigation route
  selected: boolean;                  // Whether item is currently selected
  hasDropdown: boolean;               // Whether item has dropdown menu
  dropdownOpen?: boolean;             // Dropdown open state (default: false)
  icon: string;                       // Icon path
  dropdownItems?: SharedDropdownItem[]; // Dropdown menu items
  disabled?: boolean;                 // Whether item is disabled (default: false)
}
```

### SharedDropdownItem Interface

```typescript
interface SharedDropdownItem {
  label: string;                      // Display label
  description: string;                // Item description
  route: string;                      // Navigation route
  icon: string;                       // Icon path
}
```

## Events

### Output Events

- `navigationEvent`: Emitted when navigation occurs
- `dropdownItemSelected`: Emitted when dropdown item is selected
- `profileAction`: Emitted when profile actions occur (logout, etc.)
- `themeToggle`: Emitted when theme is toggled
- `orgConfigChange`: Emitted when organization configuration changes

## Project-Specific Examples

### Console Project

```typescript
const consoleHeaderConfig: HeaderConfig = {
  logoSrc: 'assets/svgs/ascendion-logo/header-ascendion-logo.svg',
  navItems: [
    {
      label: 'Build',
      route: '/build',
      selected: true,
      hasDropdown: true,
      icon: 'svgs/icons/awe_launch.svg',
      dropdownItems: [
        {
          label: 'Agents',
          description: 'Create, Manage and Edit Agents',
          route: '/build/agents',
          icon: 'svgs/icons/awe_agents.svg',
        }
      ]
    }
  ],
  showOrgSelector: true,    // Console needs org selector
  showThemeToggle: true,
  showProfileDropdown: true,
  projectName: 'Console'
};
```

### Elder Wand Project

```typescript
const elderWandHeaderConfig: HeaderConfig = {
  logoSrc: 'assets/icons/ascendion-logo.svg',
  navItems: [
    {
      label: 'Dashboard',
      route: '/dashboard',
      selected: true,
      hasDropdown: false,
      icon: 'assets/icons/dashboard.svg',
    }
  ],
  showOrgSelector: false,   // Elder Wand doesn't need org selector
  showThemeToggle: true,
  showProfileDropdown: true,
  projectName: 'Elder Wand'
};
```

## Styling

The component uses CSS variables for theming and can be customized by overriding these variables in your global styles:

```scss
:root {
  --nav-bg: #ffffff;
  --nav-text: #000000;
  --nav-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
```

## Dependencies

- `@awe/play-comp-library` or `@ava/play-comp-library` for base header component
- `@shared/auth` for authentication services
- Angular Router for navigation

## Browser Support

- Chrome/Edge: Latest 2 versions
- Firefox: Latest 2 versions
- Safari: Latest 2 versions
- Mobile browsers: iOS Safari, Chrome Mobile 