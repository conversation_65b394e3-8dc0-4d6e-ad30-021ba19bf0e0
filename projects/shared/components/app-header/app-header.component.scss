// ========================================
// SHARED APP HEADER COMPONENT STYLES
// ========================================

// ========================================
// 1. THEME VARIABLES & HOST CONFIGURATION
// ========================================

:host {
  // Light theme variables (default)
  --header-bg: #ffffff;
  --header-text: #374151;
  --header-border: rgba(0, 0, 0, 0.08);
  --nav-bg: #ffffff;
  --nav-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --nav-text: #000000;
  --nav-arrow: #222222;
  --dropdown-bg: #ffffff;
  --dropdown-text: #374151;
  --dropdown-border: #e5e7eb;
  --dropdown-hover: #f3f4f6;

  // Dark theme variables
  &.theme-dark,
  :host-context(.dark-theme) & {
    --header-bg: #1f2937;
    --header-text: #f9fafb;
    --header-border: rgba(255, 255, 255, 0.1);
    --nav-bg: #374151;
    --nav-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    --nav-text: #ffffff;
    --nav-arrow: #d1d5db;
    --dropdown-bg: #374151;
    --dropdown-text: #f9fafb;
    --dropdown-border: #4b5563;
    --dropdown-hover: #4b5563;
  }

  // Host layout
  display: block;
  padding: 1.5rem 0 0 0;
  margin: 0;
  width: 100%;
  position: relative;

  @media (min-width: 1200px) {
    padding-top: 0;
  }

  @media (min-width: 1400px) {
    padding-top: 0;
  }
}

// ========================================
// 2. CORE LAYOUT OVERRIDES
// ========================================

::ng-deep .outer-box.light {
  background-color: transparent !important;
  box-shadow: none !important;
  margin: 0 !important;
  min-height: auto !important;
  width: 100% !important;
}

::ng-deep awe-header {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  background: var(--header-bg) !important;
  color: var(--header-text) !important;
  border-bottom: 1px solid var(--header-border) !important;

  .header-content {
    gap: 0.125rem !important;
    padding: 0 0.25rem !important;
  }

  [left-content] {
    flex: 0 0 auto !important;
    margin-right: 0.125rem !important;
  }

  [center-content] {
    flex: 1 1 auto !important;
    display: flex !important;
    justify-content: center !important;
  }

  [right-content] {
    flex: 0 0 auto !important;
    margin-left: 0.125rem !important;
  }
}

// ========================================
// 3. HEADER STRUCTURE
// ========================================

.header-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  background: transparent;
  box-shadow: none;
  margin: 0;
  padding: 0;
}

.header-shadow {
  position: absolute;
  top: 0px;
  left: 50%;
  transform: translateX(-50%);
  width: 846px;
  height: 60px;
  background: #215ad6;
  border-radius: 0 0 55px 55px;
  filter: blur(10px);
  opacity: 0.18;
  z-index: 1;
  pointer-events: none;
}

// ========================================
// 4. LOGO STYLING
// ========================================

.header-logo {
  max-height: 40px !important;
  max-width: 160px !important;
  width: auto !important;
  height: auto !important;
  padding: 0 0.5rem !important;
  margin: 0px 0px 25px 0px;

  @media (min-width: 1200px) {
    max-height: 50px !important;
    padding: 0 !important;
    margin-top: 5.6px;
  }

  @media (min-width: 1400px) {
    max-height: 40px !important;
  }

  @media (max-width: 768px) {
    max-height: 28px !important;
    padding: 0 0.25rem !important;
  }
}

// ========================================
// 4.1 ANIMATED LOGO STYLING
// ========================================

.animated-logo-container {
  min-width: 150px;
  position: relative;
  display: inline-block;
  cursor: pointer;
  perspective: 1000px; // Enable 3D transformations

  .animated-logo {
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    transform: rotateY(0deg) scale(1);
    opacity: 1;
    filter: brightness(1) saturate(1);
    transform-style: preserve-3d;

    &.logo-transitioning {
      // Animation will be applied via specific style classes
    }

    &:hover {
      transform: rotateY(0deg) scale(1.05);
      filter: brightness(1.1) saturate(1.1);
    }
  }

  // Glow effect on hover
  &:hover::before {
    content: "";
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(
      45deg,
      rgba(139, 92, 246, 0.3),
      rgba(236, 72, 153, 0.3),
      rgba(59, 130, 246, 0.3)
    );
    border-radius: 8px;
    filter: blur(8px);
    opacity: 0;
    animation: logoGlow 0.3s ease-in-out forwards;
    z-index: -1;
    pointer-events: none;
  }
}

// Logo rotation animation - flips the logo while changing the image
@keyframes logoRotateTransition {
  0% {
    transform: rotateY(0deg) scale(1);
    opacity: 1;
  }
  25% {
    transform: rotateY(45deg) scale(1.1);
    opacity: 0.8;
  }
  50% {
    transform: rotateY(90deg) scale(1.2);
    opacity: 0.6;
  }
  75% {
    transform: rotateY(135deg) scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: rotateY(180deg) scale(1);
    opacity: 1;
  }
}

// Alternative: Simple fade rotation
@keyframes logoFadeRotation {
  0% {
    transform: rotate(0deg) scale(1);
    opacity: 1;
  }
  50% {
    transform: rotate(180deg) scale(0.8);
    opacity: 0.3;
  }
  100% {
    transform: rotate(360deg) scale(1);
    opacity: 1;
  }
}

// Quick flip animation
@keyframes logoFlip {
  0% {
    transform: rotateX(0deg) scale(1);
    opacity: 1;
  }
  50% {
    transform: rotateX(90deg) scale(0.8);
    opacity: 0.4;
  }
  100% {
    transform: rotateX(0deg) scale(1);
    opacity: 1;
  }
}

// Animation style classes
.animated-logo {
  &.logo-transitioning {
    &.logo-rotate {
      animation: logoRotateTransition 0.8s ease-in-out;
    }

    &.logo-fade-rotate {
      animation: logoFadeRotation 0.6s ease-in-out;
    }

    &.logo-flip {
      animation: logoFlip 0.4s ease-in-out;
    }
  }
}

// Glow effect animation
@keyframes logoGlow {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// Studio-specific logo hints (optional: show which studio is current)
.animated-logo-container::after {
  content: attr(data-studio);
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 10px;
  font-weight: 600;
  color: var(--nav-arrow);
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
  white-space: nowrap;
}

.animated-logo-container:hover::after {
  opacity: 0.7;
}

// Studio indicator dots
.studio-indicators {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;

  .studio-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--nav-arrow);
    opacity: 0.4;
    transition: all 0.3s ease;

    &.active {
      opacity: 1;
      background: linear-gradient(135deg, #8b5cf6, #ec4899);
      transform: scale(1.3);
    }
  }
}

.animated-logo-container:hover .studio-indicators {
  opacity: 1;
}

// Responsive adjustments for animated logo
@media (max-width: 768px) {
  .animated-logo-container {
    .animated-logo {
      &:hover {
        transform: scale(1.02); // Reduced hover effect on mobile
      }

      &.logo-transitioning {
        transform: scale(0.98); // Subtle transition on mobile
      }
    }

    // Hide studio name hint on mobile
    &::after {
      display: none;
    }

    // Simpler hover effect on mobile
    &:hover::before {
      display: none;
    }
  }
}

@media (max-width: 480px) {
  .animated-logo-container .animated-logo {
    transition: all 0.2s ease; // Faster transitions on small screens
  }
}

// ========================================
// 5. NAVIGATION MENU
// ========================================

.nav-menu {
  position: relative;
  z-index: 2;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.nav-items {
  display: flex;
  justify-content: center;
  align-items: center;
  height: auto;
  margin: 0px 0px 20px 0px;
  background: var(--nav-bg) !important;
  border-radius: 0 0 40px 40px;
  clip-path: url(#headerClip);
  min-width: 900px;
  min-height: 56px;
  gap: 8px;
  padding: 8px 16px;
  box-shadow: var(--nav-shadow) !important;
  border: 1px solid var(--header-border) !important;
}

.nav-item-wrapper {
  position: relative;
  z-index: 5;
}

// ========================================
// 6. NAVIGATION ITEMS OVERRIDE
// ========================================

::ng-deep shared-nav-item {
  .nav-item-container {
    position: relative;
  }

  .nav-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 10px 16px;
    border-radius: 24px;
    color: #64748b;
    background: transparent;
    border: none;
    white-space: nowrap;
    min-height: 40px;
    position: relative;
    overflow: hidden;

    &:hover {
      background: rgba(59, 130, 246, 0.08);
      color: #3b82f6;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    }

    &.selected {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      color: white;
      font-weight: 600;
      box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
      transform: translateY(-2px);

      .nav-icon {
        filter: brightness(0) invert(1);
      }

      .dropdown-arrow svg {
        color: white;
      }
    }

    &.disabled {
      opacity: 0.4;
      cursor: not-allowed;
      pointer-events: none;
      color: #94a3b8;

      &:hover {
        background: transparent;
        transform: none;
        box-shadow: none;
      }
    }

    &:focus-visible {
      outline: 2px solid #3b82f6;
      outline-offset: 2px;
    }

    .item-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      transition: all 0.3s ease;
    }

    .nav-icon {
      width: 20px;
      height: 20px;
      object-fit: contain;
      transition: all 0.3s ease;
    }

    .item-label {
      font-family: "Inter", "Segoe UI", sans-serif;
      font-size: 15px;
      font-weight: 500;
      letter-spacing: -0.01em;
      transition: all 0.3s ease;
    }

    .dropdown-arrow {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 16px;
      height: 16px;
      margin-left: 4px;
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &.open {
        transform: rotate(180deg);
      }

      svg {
        width: 14px;
        height: 14px;
        transition: color 0.3s ease;
      }
    }

    &::before {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: rgba(59, 130, 246, 0.3);
      transition:
        width 0.6s,
        height 0.6s,
        top 0.6s,
        left 0.6s;
      transform: translate(-50%, -50%);
      z-index: -1;
    }

    &:active::before {
      width: 200px;
      height: 200px;
      top: 50%;
      left: 50%;
    }
  }
}

// ========================================
// 7. USER INFO CONTAINER
// ========================================

.user-info-container {
  display: flex;
  align-items: center;
  gap: 12px;

  @media (min-width: 1200px) {
    gap: 16px;
  }
}

// ========================================
// 8. ORGANIZATION SELECTOR
// ========================================

.org-path-dropdown-container {
  position: relative;
  display: inline-block;

  .org-path-trigger {
    display: flex;
    align-items: center;
    background: var(--nav-bg) !important;
    border-radius: 20px;
    padding: 8px 14px;
    height: 40px;
    font-size: 14px;
    box-shadow: var(--nav-shadow) !important;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid var(--header-border) !important;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      transform: translateY(-1px);
    }

    .org-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;
      border-radius: 50%;
      background: transparent;
      flex-shrink: 0;

      img {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        object-fit: cover;
      }
    }

    .org-label-text {
      font-size: 14px;
      font-weight: 600;
      color: var(--nav-text) !important;
      margin-right: 8px;
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .org-dropdown-arrow {
      display: flex;
      align-items: center;
      transition: transform 0.3s ease;
      margin-left: auto;

      &.open {
        transform: rotate(180deg);
      }

      svg {
        width: 14px;
        height: 14px;
        color: var(--nav-arrow) !important;
        transition: transform 0.4s ease-in-out;
      }
    }

    @media screen and (min-width: 1200px) {
      padding: 10px 16px;
      height: 44px;

      .org-icon img {
        width: 24px;
        height: 24px;
      }

      .org-label-text {
        font-size: 15px;
      }
    }

    @media (max-width: 768px) {
      padding: 6px 10px;
      height: 36px;

      .org-label-text {
        display: none;
      }
    }
  }

  .org-path-popover {
    position: absolute;
    top: 100%;
    left: 0;
    margin-top: 8px;
    z-index: 20;
    background: var(--dropdown-bg) !important;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--dropdown-border) !important;
    padding: 24px;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: 80vh;
    max-width: 400px;
    // min-width: 360px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);

    &.right {
      left: auto;
      right: 0;
    }

    &.left {
      left: 0;
      right: auto;
    }

    .filter-config-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 20px;
      color: var(--dropdown-text) !important;
    }

    .dropdown-row-vertical {
      display: flex;
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
      width: 100%;
    }

    .filter-label {
      display: block;
      font-weight: 600;
      font-size: 14px;
      color: var(--dropdown-text) !important;
      margin-bottom: 6px;

      &.required::after {
        content: " *";
        color: #ef4444;
        font-weight: bold;
      }
    }

    .popover-actions {
      display: flex;
      gap: 12px;
      justify-content: flex-end;
      width: 100%;
      margin-top: 24px;
    }

    @media (max-width: 768px) {
      max-width: 95vw;
      min-width: 300px;
      padding: 20px;
    }

    @media (max-width: 480px) {
      max-width: 98vw;
      min-width: 280px;
      padding: 16px;
      margin-top: 4px;

      .filter-config-title {
        font-size: 18px;
        margin-bottom: 16px;
      }

      .dropdown-row-vertical {
        gap: 12px;
      }

      .popover-actions {
        margin-top: 20px;
      }
    }
  }
}

.org-path-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 19;
  cursor: default;
}

// ========================================
// 9. APP DRAWER
// ========================================

.app-drawer-container {
  position: relative;
  display: flex;
  align-items: center;

  .app-drawer-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--nav-arrow) !important;

    &:hover {
      background: var(--dropdown-hover) !important;
      color: var(--dropdown-text) !important;
    }

    &.active {
      background: rgba(139, 92, 246, 0.1);
      color: #8b5cf6;
    }

    svg {
      display: block;
    }
  }

  .app-drawer-dropdown {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    z-index: 1000;
    background: var(--dropdown-bg) !important;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--dropdown-border) !important;
    width: 320px;
    max-width: calc(100vw - 40px);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    overflow: hidden;

    &.visible {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .app-drawer-content {
      padding: 0;
      overflow: hidden;
    }

    .app-drawer-header {
      padding: 20px 24px 16px 24px;
      background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
      color: white;

      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 700;
        text-align: center;
      }
    }

    .app-drawer-grid {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 16px 20px 20px 20px;
    }

    .app-drawer-item {
      display: flex;
      align-items: center;
      padding: 12px;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
      border: 1px solid transparent;

      &:hover {
        background: var(--dropdown-hover) !important;
        border-color: rgba(139, 92, 246, 0.2);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(139, 92, 246, 0.1);
      }

      .app-icon {
        margin-right: 12px;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--dropdown-hover) !important;
        border-radius: 10px;
        flex-shrink: 0;

        img {
          width: 24px;
          height: 24px;
          object-fit: contain;
        }
      }

      .app-info {
        flex: 1;
        min-width: 0;

        .app-name {
          font-size: 14px;
          font-weight: 600;
          color: var(--dropdown-text) !important;
          margin-bottom: 2px;
          word-wrap: break-word;
          overflow-wrap: break-word;
        }

        .app-description {
          font-size: 11px;
          color: var(--nav-arrow) !important;
          line-height: 1.3;
          word-wrap: break-word;
          overflow-wrap: break-word;
          white-space: normal;
        }
      }
    }

    @media (max-width: 768px) {
      width: 280px;
      max-width: calc(100vw - 20px);
      right: -10px;

      .app-drawer-header {
        padding: 16px 20px 12px 20px;

        h3 {
          font-size: 16px;
        }
      }

      .app-drawer-grid {
        gap: 6px;
        padding: 12px 16px 16px 16px;
      }

      .app-drawer-item {
        padding: 10px;

        .app-icon {
          width: 36px;
          height: 36px;

          img {
            width: 20px;
            height: 20px;
          }
        }

        .app-info .app-name {
          font-size: 13px;
        }

        .app-info .app-description {
          font-size: 10px;
        }
      }
    }

    @media (max-width: 480px) {
      width: 260px;
      max-width: calc(100vw - 10px);
      right: -5px;

      .app-drawer-grid {
        padding: 10px 12px 12px 12px;
      }
    }
  }
}

// ========================================
// 10. THEME TOGGLE
// ========================================

.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);

  &:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  img {
    width: 20px;
    height: 20px;
    transition: transform 0.3s ease;
  }

  &:active img {
    transform: scale(0.95);
  }
}

// ========================================
// 11. PROFILE DROPDOWN
// ========================================

.profile-container {
  position: relative;
  display: flex;
  align-items: center;

  .profile-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.2s ease;
    padding: 2px;

    &:hover {
      background: rgba(0, 0, 0, 0.05);
    }

    &.active {
      background: rgba(0, 0, 0, 0.1);
    }

    .profile-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid rgba(255, 255, 255, 0.2);
    }
  }

  .profile-dropdown {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    z-index: 1000;
    background: var(--dropdown-bg) !important;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--dropdown-border) !important;
    min-width: 320px;
    max-width: 400px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);

    &.visible {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .profile-dropdown-content {
      padding: 0;
      overflow: hidden;
    }

    .profile-user-info {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 24px 24px 20px 24px;
      background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
      color: white;
      position: relative;

      .profile-avatar-large {
        flex-shrink: 0;

        img {
          width: 56px;
          height: 56px;
          border-radius: 50%;
          object-fit: cover;
          border: 3px solid rgba(255, 255, 255, 0.3);
        }
      }

      .profile-details {
        flex: 1;
        min-width: 0;

        .profile-name {
          font-size: 18px;
          font-weight: 700;
          margin-bottom: 4px;
          color: white;
        }

        .profile-designation {
          font-size: 14px;
          font-weight: 500;
          opacity: 0.9;
          margin-bottom: 2px;
          color: rgba(255, 255, 255, 0.9);
        }

        .profile-email {
          font-size: 13px;
          opacity: 0.8;
          color: rgba(255, 255, 255, 0.8);
          word-break: break-word;
        }
      }

      .profile-close-btn {
        position: absolute;
        top: 16px;
        right: 16px;
        background: none;
        border: none;
        color: rgba(255, 255, 255, 0.8);
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          color: white;
          background: rgba(255, 255, 255, 0.1);
        }

        svg {
          display: block;
        }
      }
    }

    .profile-divider {
      height: 1px;
      background: var(--dropdown-border) !important;
      margin: 0;
    }

    .profile-section {
      padding: 16px 24px;

      .profile-section-header {
        margin-bottom: 12px;

        .section-title {
          font-size: 14px;
          font-weight: 600;
          color: var(--dropdown-text) !important;
        }
      }
    }

    .theme-toggle-container {
      display: flex;
      gap: 8px;

      .theme-option {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 10px 12px;
        border: 1px solid var(--dropdown-border) !important;
        border-radius: 8px;
        background: var(--dropdown-bg) !important;
        color: var(--nav-arrow) !important;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          border-color: var(--dropdown-border) !important;
          background: var(--dropdown-hover) !important;
        }

        &.active {
          border-color: #8b5cf6;
          background: var(--dropdown-hover) !important;
          color: #8b5cf6;

          svg {
            color: #8b5cf6;
          }
        }

        svg {
          flex-shrink: 0;
          color: currentColor;
        }
      }
    }

    .language-options {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .language-option {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 10px 12px;
        border: none;
        border-radius: 6px;
        background: transparent;
        color: var(--nav-arrow) !important;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: left;

        &:hover {
          background: var(--dropdown-hover) !important;
          color: var(--dropdown-text) !important;
        }

        &.active {
          background: rgba(139, 92, 246, 0.1) !important;
          color: #8b5cf6;
          font-weight: 600;
        }
      }
    }

    .profile-actions {
      padding: 16px 24px 24px 24px;

      .profile-action-item {
        display: flex;
        align-items: center;
        gap: 12px;
        width: 100%;
        padding: 12px 16px;
        border: none;
        border-radius: 8px;
        background: transparent;
        color: #dc2626;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: #fef2f2;
          color: #b91c1c;
        }

        svg {
          flex-shrink: 0;
          color: currentColor;
        }
      }

      .logout-btn {
        justify-content: center;
        background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
        color: white;

        &:hover {
          background: linear-gradient(135deg, #7c3aed 0%, #db2777 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        }
      }
    }

    @media (max-width: 768px) {
      min-width: 280px;
      right: -20px;

      .profile-user-info {
        padding: 20px;

        .profile-avatar-large img {
          width: 48px;
          height: 48px;
        }

        .profile-details .profile-name {
          font-size: 16px;
        }
      }

      .profile-section {
        padding: 12px 20px;
      }

      .profile-actions {
        padding: 12px 20px 20px 20px;
      }
    }
  }
}

// ========================================
// 12. DROPDOWN PORTAL
// ========================================

.dropdown-portal-menu {
  position: fixed;
  z-index: 2000;
}

.dropdown-menu {
  background: var(--dropdown-bg) !important;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--dropdown-border) !important;
  min-width: 320px;
  padding: 8px;
  backdrop-filter: blur(20px);
  margin-top: 8px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    background: var(--dropdown-hover) !important;
    transform: translateX(2px);
  }
}

.dropdown-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
  flex-shrink: 0;
}

.dropdown-content {
  flex: 1;
}

.dropdown-label {
  font-weight: 600;
  font-size: 16px;
  color: var(--dropdown-text) !important;
  margin-bottom: 4px;
}

.dropdown-description {
  font-size: 14px;
  color: var(--nav-arrow) !important;
  line-height: 1.5;
}

// ========================================
// 13. RESPONSIVE DESIGN
// ========================================

@media (max-width: 900px) {
  .header-shadow,
  .nav-items {
    width: 98vw;
    min-width: unset;
    padding: 8px 12px;
  }
}

@media (max-width: 600px) {
  .header-shadow,
  .nav-items {
    width: 100vw;
    min-width: unset;
    padding: 6px 8px;
    gap: 4px;
  }

  .nav-items {
    min-height: 48px;
  }

  ::ng-deep shared-nav-item .nav-item {
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 20px;

    .item-label {
      font-size: 14px;
    }
  }
}

@media (max-width: 768px) {
  ::ng-deep shared-nav-item .nav-item {
    padding: 8px 14px;
    font-size: 15px;
  }

  .user-info-container {
    gap: 8px;
  }
}

// ========================================
// 14. ANIMATIONS
// ========================================

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.nav-items {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}
