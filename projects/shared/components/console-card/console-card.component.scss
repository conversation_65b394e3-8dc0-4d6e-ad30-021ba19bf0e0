::ng-deep svg {
  stroke: #2f5a8e !important;
}

.ava-console-card {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: 350px;
  height: 326px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0px 4px 12px 0px #87a1971f;
  backdrop-filter: blur(6px);
  transition: all 0.3s ease;

  &:hover:not(.disabled) {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
  }

  &:focus-within {
    outline-offset: 4px;
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  &.loading {
    pointer-events: none;
  }

  &.skeleton {
    pointer-events: none;
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: inherit;

    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #e0e0e0;
      border-top: 3px solid #e91e63;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  // Header section
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px;
    padding-bottom: 16px;

    .category-section {
      display: flex;
      align-items: center;
      gap: 8px;

      .category-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #e91e63;

        ava-icon {
          color: inherit !important;
        }
      }

      .category-title {
        font-weight: 400;
        font-size: 12px;
        color: #2f5a8e;
      }
    }

    .category-value {
      display: flex;
      align-items: center;
      gap: 6px;
      font-weight: 600;
      font-size: 12px;
      color: #2f5a8e;

      ava-icon {
        color: inherit !important;
      }

      span {
        color: inherit !important;
      }
    }
  }

  // Main content section
  .card-body {
    min-height: 200px;
    padding: 0 24px 24px;

    .card-title {
      font-weight: 700;
      font-size: 24px;
      color: #1a46a7;
      margin: 0;
      margin-bottom: 16px;
      line-height: 1.3;
      width: 100%;
      max-width: 100%;
      display: block;
      word-wrap: break-word;
      overflow-wrap: break-word;
      hyphens: auto;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
      position: relative;
    }

    .card-description {
      font-weight: 400;
      font-size: 14px;
      color: #2f5a8e;
      margin: 0;
      margin-top: 1.2rem;
      &.truncated {
        cursor: pointer;
        position: relative;
      }
    }
  }

  // Footer section
  .card-footer {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 0 24px 24px;

    .metadata {
      display: flex;
      flex-direction: column;
      gap: 6px;

      .author,
      .date {
        display: flex;
        align-items: center;
        gap: 6px;
        font-weight: 400;
        font-size: 12px;
        color: #2f5a8e;

        ava-icon {
          color: inherit !important;
        }

        span {
          color: inherit !important;
        }
      }
    }

    .actions {
      display: flex;
      align-items: center;
      gap: 8px;

      .action-btn {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border: none;
        border-radius: 8px;
        background: #ffffff;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: visible;

        ava-icon {
          color: inherit !important;
          transition: transform 0.2s ease;
        }

        // Subtle hover state
        &:hover {
          background: #f8fafc;
          border-color: #2f5a8e;
          color: #2f5a8e;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(47, 90, 142, 0.15);

          ava-icon {
            transform: scale(1.1);
          }

          // Show tooltip on hover
          &::after {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(0px);
          }
        }

        // Focus state
        &:focus {
          outline: 2px solid #2f5a8e;
          outline-offset: 2px;
          background: #f8fafc;
          border-color: #2f5a8e;
          color: #2f5a8e;
        }

        // Active state
        &:active {
          transform: translateY(0px);
          box-shadow: 0 2px 6px rgba(47, 90, 142, 0.2);
        }

        // Improved tooltip styles (removed arrow)
        &::after {
          content: attr(data-tooltip);
          position: absolute;
          bottom: 100%;
          left: 50%;
          transform: translateX(-50%) translateY(-8px);
          background: #1f2937;
          color: white;
          padding: 8px 12px;
          border-radius: 8px;
          font-size: 11px;
          font-weight: 500;
          white-space: nowrap;
          opacity: 0;
          visibility: hidden;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          z-index: 1000;
          margin-bottom: 6px;
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
          pointer-events: none;
        }

        // Primary button styling
        &.primary {
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(26, 70, 167, 0.25);
          }

          &:active {
            transform: translateY(0px);
          }
        }

        // Disabled state
        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          pointer-events: none;

          &:hover {
            transform: none;
            box-shadow: none;
          }
        }
      }
    }
  }

  // Size variants
  &.small {
    width: 240px;
    height: 320px;

    .card-body .card-title {
      font-size: 18px;
    }

    .card-footer .actions .action-btn {
      width: 28px;
      height: 28px;
    }
  }

  &.large {
    width: 320px;
    height: 440px;

    .card-body .card-title {
      font-size: 22px;
    }

    .card-footer .actions .action-btn {
      width: 36px;
      height: 36px;
    }
  }

  // Skeleton Loader Styles
  .skeleton-loader {
    padding: 24px;

    .skeleton-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;

      .skeleton-category {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .skeleton-value {
        display: flex;
        align-items: center;
        gap: 6px;
      }
    }

    .skeleton-body {
      margin-bottom: 60px;

      .skeleton-title {
        margin-bottom: 16px;
      }
    }

    .skeleton-footer {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;

      .skeleton-metadata {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .skeleton-meta-item {
        display: flex;
        align-items: center;
        gap: 6px;
      }

      .skeleton-actions {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }

  // Skeleton element base styles
  .skeleton-text,
  .skeleton-icon,
  .skeleton-icon-small,
  .skeleton-button {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
  }

  // Skeleton text variations
  .skeleton-text {
    height: 16px;

    &.skeleton-category-title {
      width: 60px;
      height: 12px;
    }

    &.skeleton-number {
      width: 20px;
      height: 12px;
    }

    &.skeleton-title {
      width: 85%;
      height: 24px;
      border-radius: 6px;
    }

    &.skeleton-description-1 {
      width: 100%;
      height: 14px;
      margin-bottom: 8px;
    }

    &.skeleton-description-2 {
      width: 90%;
      height: 14px;
      margin-bottom: 8px;
    }

    &.skeleton-description-3 {
      width: 75%;
      height: 14px;
    }

    &.skeleton-author {
      width: 70px;
      height: 12px;
    }

    &.skeleton-date {
      width: 55px;
      height: 12px;
    }
  }

  // Skeleton icon styles
  .skeleton-icon {
    width: 18px;
    height: 18px;
    border-radius: 50%;
  }

  .skeleton-icon-small {
    width: 12px;
    height: 12px;
    border-radius: 50%;
  }

  // Skeleton button styles
  .skeleton-button {
    width: 32px;
    height: 32px;
    border-radius: 8px;

    &.skeleton-button-primary {
      background: linear-gradient(
        90deg,
        #2f5a8e40 25%,
        #1a46a740 50%,
        #2f5a8e40 75%
      );
      background-size: 200% 100%;
    }
  }
}

// Shimmer animation for skeleton
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

// Spin animation for loading spinner
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// Tooltip fade-in animation
@keyframes tooltipFadeIn {
  0% {
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
  }

  100% {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

// Grid container for responsive layout
.console-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  width: 100%;
  justify-items: center;
  padding: 24px;

  @media (min-width: 1400px) {
    grid-template-columns: repeat(4, 1fr);
    max-width: 1200px;
    margin: 0 auto;
  }

  @media (min-width: 1024px) and (max-width: 1399px) {
    grid-template-columns: repeat(3, 1fr);
    max-width: 900px;
    margin: 0 auto;
  }

  @media (min-width: 768px) and (max-width: 1023px) {
    grid-template-columns: repeat(2, 1fr);
    max-width: 600px;
    margin: 0 auto;
  }

  @media (max-width: 767px) {
    grid-template-columns: 1fr;
    max-width: 320px;
    margin: 0 auto;
  }
}
