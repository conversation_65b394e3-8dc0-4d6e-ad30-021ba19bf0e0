// Shared Nav Item Component Styles
.nav-item-container {
  position: relative;
  display: flex;
  align-items: center;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 999px;
  cursor: pointer;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  white-space: nowrap;
  position: relative;

  &:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
  }

  &.selected {
    background: rgba(102, 126, 234, 0.15);
    color: #667eea;
    font-weight: 600;

    .nav-icon {
      filter: brightness(1.2);
    }
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;

    .item-icon,
    .item-label,
    .dropdown-arrow {
      opacity: 0.6;
    }

    .nav-icon {
      opacity: 0.4;
      filter: grayscale(100%);
    }
  }
}

.item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  transition: all 0.3s ease;

  &.selected {
    transform: scale(1.1);
  }
}

.nav-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
  transition: all 0.3s ease;

  &.selected {
    filter: brightness(1.2) saturate(1.3);
  }
}

.item-label {
  font-family: 'Mulish', sans-serif;
  transition: all 0.3s ease;
}

.dropdown-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;

  &.open {
    transform: rotate(180deg);
  }

  svg {
    color: currentColor;
    transition: color 0.3s ease;
  }
}

// Responsive design
@media (max-width: 768px) {
  .nav-item {
    padding: 8px 12px;
    font-size: 13px;
  }

  .item-icon {
    width: 18px;
    height: 18px;
  }

  .nav-icon {
    width: 18px;
    height: 18px;
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .nav-item {
    color: #e5e7eb;

    &:hover {
      background: rgba(139, 92, 246, 0.15);
      color: #a78bfa;
    }

    &.selected {
      background: rgba(139, 92, 246, 0.2);
      color: #a78bfa;
    }
  }
} 