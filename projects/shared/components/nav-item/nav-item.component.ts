import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedDropdownItem } from '../app-header/app-header.component';

@Component({
  selector: 'shared-nav-item',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './nav-item.component.html',
  styleUrl: './nav-item.component.scss',
})
export class SharedNavItemComponent {
  @Input() label: string = '';
  @Input() route: string = '';
  @Input() selected: boolean = false;
  @Input() hasDropdown: boolean = false;
  @Input() dropdownOpen: boolean = false;
  @Input() dropdownItems: SharedDropdownItem[] = [];
  @Input() icon: string = '';
  @Input() disabled: boolean = false;

  @Output() toggleDropdownEvent = new EventEmitter<void>();
  @Output() navigateEvent = new EventEmitter<string>();
  @Output() selectEvent = new EventEmitter<void>();
  @Output() dropdownItemSelected = new EventEmitter<{
    route: string;
    label: string;
  }>();
  @Output() dropdownPortalOpen = new EventEmitter<{
    rect: DOMRect;
    items: SharedDropdownItem[];
    parentLabel: string;
    navItemId: string;
  }>();

  toggleDropdown(): void {
    this.toggleDropdownEvent.emit();
  }

  navigate(): void {
    this.navigateEvent.emit(this.route);
  }

  select(): void {
    this.selectEvent.emit();
  }

  onClick(event: MouseEvent): void {
    if (this.disabled) {
      event.preventDefault();
      event.stopPropagation();
      return;
    }

    if (this.hasDropdown) {
      event.stopPropagation();
      const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
      this.dropdownPortalOpen.emit({ 
        rect, 
        items: this.dropdownItems, 
        parentLabel: this.label, 
        navItemId: this.label 
      });
      this.toggleDropdown();
    } else {
      this.navigate();
      this.select();
    }
  }

  onDropdownItemSelected(event: { route: string; label: string }): void {
    this.dropdownItemSelected.emit(event);
  }
} 