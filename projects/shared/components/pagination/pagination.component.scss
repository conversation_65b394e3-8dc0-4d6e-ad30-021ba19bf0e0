.pagination-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 0;
  width: 100%;
}

.page-nav {
  color: var(--Global-colors-Royal-blue-700, #1a46a7);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(.disabled) {
    color: var(--Global-colors-Royal-blue-800, #143681);
    background: var(--Global-colors-Royal-blue-100, #bbcff9);
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  svg {
    width: 16px;
    height: 16px;
  }
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-number {
  background-color: var(--Global-colors-Royal-blue-50, #e9effd);
  color: var(--Global-colors-Royal-blue-700, #1a46a7);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: 400;
  transition: all 0.2s ease;

  &:hover:not(.active) {
    color: var(--Global-colors-Royal-blue-800, #143681);
    background: var(--Global-colors-Royal-blue-100, #bbcff9);
  }

  &.active {
    color: var(--Global-colors-Royal-blue-800, #143681);
    background: var(--Global-colors-Royal-blue-100, #bbcff9);
  }
}

.ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  font-size: 16px;
  font-weight: 500;
  color: var(--nav-item-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pagination-container {
    padding: 12px 0;
  }

  .page-nav,
  .page-number {
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 480px) {
  .page-nav,
  .page-number {
    width: 32px;
    height: 32px;
  }

  .page-numbers {
    gap: 4px;
  }

  .ellipsis {
    width: 20px;
  }
}
