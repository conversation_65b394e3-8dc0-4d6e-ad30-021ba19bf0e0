<div class="playground-container">
  <div class="button-container">
    <!-- Agent Selection Dropdown -->
    <div class="dropdown-container" *ngIf="showDropdown">
      <ava-dropdown
        *ngIf="!isMinimalView"
        dropdownTitle="Choose Agent"
        [options]="promptOptions"
        [selectedValue]="selectedPrompt"
        [enableSearch]="true"
        [singleSelect]="true"
        (selectionChange)="onPromptChange($event)">
      </ava-dropdown>
    </div>

    <!-- Disabled Agent Name Input Field -->
    <div class="agent-name-display" *ngIf="showAgentNameInput">
      <ava-textbox
        [placeholder]="agentNamePlaceholder"
        [formControl]="agentNameDisplayControl"
        variant="default"
        size="md"
        class="disabled-agent-name-input">
      </ava-textbox>
    </div>

    <div class="btn-menu">
      <!-- <button class="action-btn">Send for Approval</button> -->
      <ava-button *ngIf="showApprovalButton" label="Send for Approval" size="small" state="active"
        variant="primary" [customStyles]="{
          background:
            'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
          '--button-effect-color': '33, 90, 214',
        }" (userClick)="onApprovalClick()"></ava-button>
      <div class="menu-icon" (click)="toggleMenu()" #menuIcon>
        <span></span>
        <span></span>
        <span></span>
      </div>

      <div class="dot-dropdown-menu" *ngIf="isMenuOpen">
        <button (click)="save()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
            <path d="M17 3H5a2 2 0 0 0-2 2v14l4-4h10a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2z" />
          </svg>
          Save
        </button>

        <button (click)="export()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
            <path d="M5 20h14v-2H5m14-9h-4V3H9v6H5l7 7 7-7z" />
          </svg>
          Export
        </button>
      </div>
    </div>
  </div>
  <div class="layout" #messagesContainer>
    <!-- Messages in normal order -->
    <div *ngFor="let msg of messages; trackBy: trackByIndex" class="message-row" [ngClass]="msg.from">
      <div [ngClass]="{
          'bot-response': msg.from === 'ai',
          'user-message': msg.from === 'user',
        }">
        {{ msg.text }}
        <button *ngIf="msg.from === 'ai'" class="copy-btn" (click)="copyToClipboard(msg.text)" title="Copy">
          <ava-icon slot="icon-start" iconName="copy" [iconSize]="16" iconColor="var(--color-brand-primary)">
          </ava-icon>
        </button>
      </div>
      <div *ngIf="showCopiedToast" class="copied-toast">Copied!</div>
    </div>

    <!-- Animated loading indicator when API call is in progress -->
    <div *ngIf="isLoading && showLoader" class="message-row ai">
      <div class="bot-response loading-message">
        <div class="typing-indicator">
          <span class="dot"></span>
          <span class="dot"></span>
          <span class="dot"></span>
        </div>
      </div>
    </div>

    <!-- Simple text loading indicator for tool testing -->
    <div *ngIf="isLoading && !showLoader" class="message-row ai">
      <div class="bot-response">...</div>
    </div>
  </div>

 
  <div class="input-container">
  <textarea
    [(ngModel)]="inputText"
    [disabled]="isDisabled || isLoading"
    (keydown.enter)="handleSendMessage(); $event.preventDefault()"
    placeholder="Enter something to test"
  ></textarea>

  <button *ngIf="showFileUploadButton" class="attach-btn" title="Attach File" (click)="fileInput.click()">
    <ava-icon slot="icon-start" iconName="paperclip" [iconSize]="16" iconColor="var(--color-brand-primary)">
        </ava-icon>
  </button> 

    <!-- Hidden file input -->
    <input
      #fileInput
      type="file"
      [accept]="acceptedFileType"
      multiple
      style="display: none;"
      (change)="onFileSelected($event)">

    <!-- Display uploaded files -->
    <div class="uploaded-files" *ngIf="filesUploadedData.length > 0">
      <div class="file-item" *ngFor="let file of filesUploadedData; let i = index">
        <span class="file-name">{{ file.documentName }}</span>
        <button class="remove-file" (click)="removeFile(i)" title="Remove file"></button>
      </div>
    </div>

  <div class="right-icons">
    <!-- <button class="edit-btn" title="Edit">
     <ava-icon slot="icon-start" iconName="wand-sparkles" [iconSize]="16" iconColor="var(--color-brand-primary)">
        </ava-icon>
    </button> -->
    <button class="send-btn" title="Send" (click)="handleSendMessage()" [disabled]="isLoading || isDisabled">
      <ava-icon slot="icon-start" iconName="send-horizontal" [iconSize]="16" iconColor="var(--color-brand-primary)">
        </ava-icon>
    </button>
  </div>
</div>



  <!-- Toggles Container - All toggles in same line when present -->
  <div class="toggle-container" *ngIf="showChatInteractionToggles || showAiPrincipleToggle">
    <!-- Conversational Toggle -->
    <div class="toggle-row" *ngIf="showChatInteractionToggles">
      <ava-toggle
        [checked]="isConvChecked"
        [title]="'Conversational'"
        [position]="'left'"
        size="small"
        (checkedChange)="onConversationalToggle($event)">
      </ava-toggle>
    </div>

    <!-- Use Template Toggle -->
    <div class="toggle-row" *ngIf="showChatInteractionToggles">
      <ava-toggle
        [checked]="isUseTemplate"
        [title]="'Use Template'"
        [position]="'left'"
        size="small"
        (checkedChange)="onTemplateToggle($event)">
      </ava-toggle>
    </div>

    <!-- AI Principles Toggle -->
    <!-- <div class="toggle-row" *ngIf="showAiPrincipleToggle">
      <ava-toggle
        [checked]="true"
        [title]="'AI Principles'"
        [position]="'left'"
        size="small"
        (checkedChange)="onAiPrincipleToggle($event)">
      </ava-toggle>
    </div> -->
  </div>
</div>