import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil, switchMap, finalize, catchError, of } from 'rxjs';
import { FormBuilder, FormGroup, FormsModule } from '@angular/forms';

// Import child components
import { AgentExecutionPlaygroundComponent } from './components/agent-execution-playground/agent-execution-playground.component';
import { ChatMessage } from '@shared/components/chat-window/chat-window.component';
import { IconComponent, TabItem, DropdownOption } from '@ava/play-comp-library';
import { AgentServiceService } from '../services/agent-service.service';
import { AgentPlaygroundService } from '../build-agents/services/agent-playground.service';
import { environment } from '@shared/environments/environment';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { LoaderService } from '@shared/services/loader/loader.service';
import { ToolExecutionService } from '@shared/services/tool-execution/tool-execution.service';
import { AvaTab } from '@shared/models/tab.model';
import { ExecutionStatus, ActivityLog, ExecutionDetails, OutputItem } from '@shared/models/execution.model';

// Remove duplicate definitions - they're now in shared models

@Component({
  selector: 'app-agent-execution',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AgentExecutionPlaygroundComponent,
    IconComponent,
  ],
  templateUrl: './agent-execution.component.html',
  styleUrls: ['./agent-execution.component.scss'],
})
export class AgentExecutionComponent implements OnInit, OnDestroy {
  navigationTabs: TabItem[] = [
    { id: 'nav-home', label: 'Agent Activity' },
    { id: 'nav-products', label: 'Agent Output' },
    { id: 'nav-services', label: 'Preview', disabled: true },
  ];

  // Agent details
  agentId: string | null = null;
  agentType: string = 'individual';
  agentName: string = 'Agent';
  agentDetail: string = '';

  @ViewChild(AgentExecutionPlaygroundComponent, { static: false })
  playgroundComp!: AgentExecutionPlaygroundComponent;

  // Activity logs
  activityLogs: ActivityLog[] = [];
  activityProgress: number = 0;
  executionDetails?: ExecutionDetails;
  isRunning: boolean = false;
  status: ExecutionStatus = ExecutionStatus.notStarted;

  // Chat messages
  chatMessages: ChatMessage[] = [];
  isProcessingChat: boolean = false;
  inputText = '';

  // Agent outputs
  agentOutputs: OutputItem[] = [];
  latestAgentResponse: any = null; // Store the latest agent response for display
  public agentForm!: FormGroup;
  public fileType: string =
    '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';

  // Execution state
  executionStartTime: Date | null = null;
  executionCompleted: boolean = false;
  executionId!: string;

  enableStreamingLog = environment.enableLogStreaming || 'all';

  public isExecutionComplete: boolean = false;
  progressInterval: any;

  private destroy$ = new Subject<void>();
  selectedTab: string = 'Agent Activity';
  demoTabs: AvaTab[] = [
    { id: 'tab1', label: 'History' },
    { id: 'tab2', label: 'Blueprint' },
    { id: 'tab3', label: 'Agent Output' },
  ];

  errorMsg = false;
  resMessage: any;
  taskMessage: any[] = [];
  isJsonValid = false;
  disableChat: boolean = false;
  selectedFiles: File[] = [];
  agentNodes: any[] = [];
  userInputList: any[] = [];
  progress = 0;
  isLoading = false;
  loaderColor: string = '';

  inputFieldOrder: string[] = [];
  currentInputIndex: number = 0;
  activeTabId: string = 'nav-home';

  // Panel state properties
  isLeftPanelCollapsed: boolean = false;
  activeRightTab: string = 'blueprint';

  // Agent-specific properties
  currentAgentDetails: any = null;
  buildAgentNodes: any[] = [];
  canvasNodes: any[] = [];
  canvasEdges: any[] = [];
  selectedPrompt: string = '';
  selectedAgentMode: string = '';
  selectedUseCaseIdentifier: string = '';
  agentFilesUploadedData: any[] = [];
  agentAttachment: string[] = [];
  isAgentPlaygroundLoading = false;
  agentPlaygroundDestroy = new Subject<boolean>();
  agentChatPayload: any[] = [];
  agentCode: string = '';
  promptOptions: DropdownOption[] = [];

  // Custom Blueprint Display Properties
  blueprintCompletionPercentage: number = 0;
  blueprintPromptNodes: any[] = [];
  blueprintModelNodes: any[] = [];
  blueprintKnowledgeNodes: any[] = [];
  blueprintGuardrailNodes: any[] = [];
  blueprintToolNodes: any[] = [];

  // Blueprint zone expansion state
  private blueprintZonesExpanded: { [key: string]: boolean } = {
    prompt: true,
    model: true,
    knowledge: true,
    guardrail: true,
    tool: true,
  };

  // Blueprint panel properties (using existing arrays above)

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private agentService: AgentServiceService,
    private agentPlaygroundService: AgentPlaygroundService,
    private tokenStorage: TokenStorageService,
    private loaderService: LoaderService,
    private formBuilder: FormBuilder,
    private toolExecutionService: ToolExecutionService,
  ) {
    this.agentForm = this.formBuilder.group({
      isConversational: [true],
      isUseTemplate: [false],
    });
  }

  ngOnInit(): void {
    console.log('🌟 SHARED COMPONENT INITIALIZED! 🌟');
    this.executionId = crypto.randomUUID();

    this.route.params.subscribe((params) => {
      this.agentType = params['type'] || 'individual';
      console.log('🌟 SHARED: Agent type set to:', this.agentType);
    });

    this.route.queryParams.subscribe((params) => {
      if (params['id']) {
        this.agentId = params['id'];
        this.loadAgentData(params['id']);
      }
    });

    // Initialize chat messages
    this.chatMessages = [
      {
        from: 'ai',
        text: `Hi there! I am ${this.agentName || 'your agent'}. How can I help you today?`,
      },
    ];
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
    }
  }

  onTabChange(event: { id: string; label: string }) {
    this.activeTabId = event.id;
    this.selectedTab = event.label;
  }

  loadAgentData(agentId: string): void {
    this.isLoading = true;

    // Load agent data based on type
    if (this.agentType === 'collaborative') {
      this.agentService.getCollaborativeAgentDetailsById(agentId).subscribe({
        next: (response: any) => {
          this.handleAgentDataResponse(response);
        },
        error: (error: any) => {
          console.error('Error loading collaborative agent:', error);
          this.isLoading = false;
        },
      });
    } else {
      this.agentService.getAgentById(agentId).subscribe({
        next: (response: any) => {
          this.handleAgentDataResponse(response);
        },
        error: (error: any) => {
          console.error('Error loading individual agent:', error);
          this.isLoading = false;
        },
      });
    }
  }

  private handleAgentDataResponse(response: any): void {
    this.isLoading = false;

    // Extract agent details
    let agentData;
    if (
      response.agentDetails &&
      Array.isArray(response.agentDetails) &&
      response.agentDetails.length > 0
    ) {
      agentData = response.agentDetails[0];
    } else if (response.agentDetail) {
      agentData = response.agentDetail;
    } else if (response.data) {
      agentData = response.data;
    } else {
      agentData = response;
    }

    if (agentData) {
      this.currentAgentDetails = agentData;
      this.agentName = agentData.name || agentData.agentName || 'Agent';
      this.agentDetail = agentData.description || agentData.agentDetail || '';

      // For individual agents, set up the required properties for playground functionality
      if (this.agentType === 'individual') {
        // Set selectedPrompt to agent name or useCaseName to bypass dropdown requirement
        this.selectedPrompt =
          agentData.useCaseName || agentData.name || 'loaded-agent';

        // Set selectedAgentMode for API calls - use useCaseCode if available
        this.selectedAgentMode =
          agentData.useCaseCode ||
          agentData.useCaseName ||
          agentData.name ||
          '';

        // Set useCaseIdentifier - use organizationPath if available
        if (agentData.organizationPath) {
          this.selectedUseCaseIdentifier = agentData.organizationPath;
        } else if (agentData.useCaseCode) {
          this.selectedUseCaseIdentifier = agentData.useCaseCode;
        } else if (agentData.useCaseName) {
          this.selectedUseCaseIdentifier = agentData.useCaseName;
        }
      }

      // Update chat message with agent name
      if (this.chatMessages.length > 0) {
        this.chatMessages[0].text = `Hi there! I am ${this.agentName}. How can I help you today?`;
      }

      // Load agent nodes and configuration
      this.loadAgentNodes(agentData);
    }
  }

  private loadAgentNodes(agentData: any): void {
    // Map agent configuration to blueprint panel
    this.mapAgentConfigurationToBlueprint(agentData);
  }

  handleChatMessage(message: string): void {
    if (this.agentType === 'individual') {
      // For individual agents, use the loaded agent details instead of requiring dropdown selection
      if (
        !this.currentAgentDetails &&
        (!this.selectedPrompt || this.selectedPrompt === 'default')
      ) {
        this.showAgentError(
          'Agent details are not loaded. Please try refreshing the page.',
        );
        return;
      }

      let displayMessage = message;
      if (this.agentFilesUploadedData.length > 0) {
        const fileNames = this.agentFilesUploadedData
          .map((file) => file.documentName)
          .join(', ');
        displayMessage = `${message}\n\n📎 Attached files: ${fileNames}`;
      }

      this.chatMessages = [
        ...this.chatMessages,
        { from: 'user', text: displayMessage },
      ];
      this.isProcessingChat = true;

      const isConversational =
        this.agentForm.get('isConversational')?.value || false;
      const isUseTemplate = this.agentForm.get('isUseTemplate')?.value || false;

      console.log(
        'Chat message handling - isConversational:',
        isConversational,
        'isUseTemplate:',
        isUseTemplate,
      );

      // Use agent details from the loaded agent data instead of dropdown selection
      // Mode should be the useCaseCode, not useCaseName
      const agentMode =
        this.agentCode ||
        this.selectedAgentMode ||
        this.currentAgentDetails?.useCaseCode ||
        this.currentAgentDetails?.useCaseName ||
        this.currentAgentDetails?.name ||
        this.selectedPrompt;

      let useCaseIdentifier = this.selectedUseCaseIdentifier;
      if (!useCaseIdentifier) {
        // Use organizationPath if available, otherwise build from agent details
        if (this.currentAgentDetails?.organizationPath) {
          useCaseIdentifier = this.currentAgentDetails.organizationPath;
        } else {
          const orgPath = this.buildOrganizationPath();
          const agentIdentifier =
            this.currentAgentDetails?.useCaseCode ||
            this.currentAgentDetails?.useCaseName ||
            this.currentAgentDetails?.name ||
            agentMode;
          useCaseIdentifier = `${agentIdentifier}${orgPath}`;
        }
      }

      if (this.agentFilesUploadedData.length > 0) {
        this.processAgentFilesAndSendMessage(
          message,
          agentMode,
          useCaseIdentifier,
          isConversational,
          isUseTemplate,
        );
        return;
      }

      this.sendAgentMessageToAPI(
        message,
        agentMode,
        useCaseIdentifier,
        isConversational,
        isUseTemplate,
      );
    } else if (this.agentType === 'collaborative') {
      this.isProcessingChat = true;
      let payload = {
        executionId: this.executionId,
        agentId: Number(this.agentId),
        user: this.tokenStorage.getDaUsername() || '<EMAIL>',
        userInputs: { question: message },
      };

      if (this.agentFilesUploadedData.length > 0) {
        const fileWrapper = this.agentFilesUploadedData[0];
        let displayMessage: string;
        if (this.agentFilesUploadedData.length > 0) {
          const fileNames = this.agentFilesUploadedData
            .map((file) => file.documentName)
            .join(', ');
          displayMessage = `📎 Attached files: ${fileNames}`;

          this.chatMessages = [{ from: 'user', text: displayMessage }];
        }
        this.agentPlaygroundService
          .submitAgentExecuteWithFile(payload, fileWrapper)
          .pipe(
            finalize(() => {
              this.isProcessingChat = false;
              this.isAgentPlaygroundLoading = false;
            }),
            takeUntil(this.agentPlaygroundDestroy),
          )
          .subscribe({
            next: (res) => this.handleAgentExecuteResponse(res, message),
            error: (err: any) => {
              this.chatMessages = [
                ...this.chatMessages,
                { from: 'user', text: message },
                {
                  from: 'ai',
                  text:
                    err?.error?.message ||
                    err?.message ||
                    'Something went wrong.',
                },
              ];
            },
          });
      } else {
        this.agentPlaygroundService
          .submitAgentExecute(payload)
          .pipe(
            finalize(() => {
              this.isProcessingChat = false;
              this.isAgentPlaygroundLoading = false;
            }),
            takeUntil(this.agentPlaygroundDestroy),
          )
          .subscribe({
            next: (res) => this.handleAgentExecuteResponse(res, message),
            error: (err: any) => {
              this.chatMessages = [
                ...this.chatMessages,
                { from: 'user', text: message },
                {
                  from: 'ai',
                  text:
                    err?.error?.message ||
                    err?.message ||
                    'Something went wrong.',
                },
              ];
            },
          });
      }
    }
  }

  onPromptChanged(prompt: DropdownOption): void {
    this.inputText = prompt.name || String(prompt.value) || '';
  }

  onPlaygroundConversationalToggle(value: boolean): void {
    // Update the form control
    this.agentForm.get('isConversational')?.setValue(value);

    // When conversational mode is turned off, clear the conversation history
    // This ensures that the next message will be treated as a fresh start
    if (!value) {
      this.agentChatPayload = [];
      console.log(
        'Conversational mode disabled - cleared chat payload history',
      );
    } else {
      console.log('Conversational mode enabled - will maintain chat history');
    }
  }

  onPlaygroundTemplateToggle(value: boolean): void {
    // Update the form control
    this.agentForm.get('isUseTemplate')?.setValue(value);
    console.log('Template mode toggled:', value);
  }

  onFilesSelected(files: any[]): void {
    this.selectedFiles = files;
    // Update agentFilesUploadedData for agent execution
    this.agentFilesUploadedData = files;
  }

  onApprovalRequested(): void {
    // Handle approval request
  }

  saveLogs(): void {
    // Save execution logs
  }

  exportResults(section: 'activity' | 'output'): void {
    // Export results
  }

  handleControlAction(action: 'play' | 'pause' | 'stop'): void {
    // Handle execution control actions
  }

  navigateBack(): void {
    this.router.navigate(['/build/agents', this.agentType], {
      queryParams: { id: this.agentId, mode: 'view' },
    });
  }

  editAgent(): void {
    this.router.navigate(['/build/agents', this.agentType], {
      queryParams: { id: this.agentId, mode: 'edit' },
    });
  }

  navigateToAgentsList(): void {
    this.router.navigate(['/build/agents']);
  }

  toggleLeftPanel(): void {
    this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;
  }

  setActiveRightTab(tab: string): void {
    this.activeRightTab = tab;
  }

  // Blueprint zone management methods
  toggleBlueprintZone(zoneType: string): void {
    this.blueprintZonesExpanded[zoneType] =
      !this.blueprintZonesExpanded[zoneType];
  }

  isBlueprintZoneExpanded(zoneType: string): boolean {
    return this.blueprintZonesExpanded[zoneType] || false;
  }

  // API and helper methods from build-agents component
  private showAgentError(message: string): void {
    this.chatMessages = [...this.chatMessages, { from: 'ai', text: message }];
  }

  private buildOrganizationPath(): string {
    // Simple implementation - in real scenario this would be from navbar/metadata
    return '';
  }

  private getMetadataFromNavbar(): { levelId?: number } {
    // Simple implementation - in real scenario this would get org level mapping
    return {};
  }

  handleAgentExecuteResponse(response: any, message: string): void {
    try {
      // Store the latest response for display in the output panel
      this.latestAgentResponse = response;

      const outputRaw = response?.agentResponse?.agent?.output;
      let formattedOutput = '';

      if (outputRaw) {
        // Directly replace escaped \n with real newlines
        formattedOutput = outputRaw.replace(/\\n/g, '\n');
      } else {
        formattedOutput = response?.agentResponse?.detail;
      }

      this.chatMessages = [
        ...this.chatMessages,
        { from: 'user', text: message },
        { from: 'ai', text: formattedOutput || 'No response from agent.' },
      ];
    } catch (err: any) {
      this.chatMessages = [
        ...this.chatMessages,
        {
          from: 'ai',
          text: err?.message || 'Agent response could not be processed.',
        },
      ];
    }
  }

  private processAgentFilesAndSendMessage(
    message: string,
    mode: string,
    useCaseIdentifier: string,
    isConversational: boolean,
    isUseTemplate: boolean,
  ): void {
    const formData = new FormData();
    this.agentFilesUploadedData.forEach((fileData) => {
      if (fileData.file) {
        formData.append('files', fileData.file);
      }
    });

    if (formData.has('files')) {
      this.agentPlaygroundService
        .getFileToContent(formData)
        .pipe(
          switchMap((fileResponse) => {
            const fileContent =
              fileResponse?.fileResponses
                ?.map((response: any) => response.fileContent)
                ?.join('\n') || '';
            this.sendAgentMessageToAPIWithFiles(
              message,
              mode,
              useCaseIdentifier,
              isConversational,
              isUseTemplate,
              fileContent,
            );
            return of(null);
          }),
          catchError((error) => {
            console.error('Error parsing files:', error);
            this.sendAgentMessageToAPI(
              message,
              mode,
              useCaseIdentifier,
              isConversational,
              isUseTemplate,
            );
            return of(null);
          }),
        )
        .subscribe();
    } else {
      this.sendAgentMessageToAPI(
        message,
        mode,
        useCaseIdentifier,
        isConversational,
        isUseTemplate,
      );
    }
  }

  private sendAgentMessageToAPI(
    message: string,
    mode: string,
    useCaseIdentifier: string,
    isConversational: boolean,
    isUseTemplate: boolean,
  ): void {
    console.log('API Call Parameters:', {
      message,
      mode,
      useCaseIdentifier,
      isConversational,
      isUseTemplate,
      currentChatPayloadLength: this.agentChatPayload.length,
    });

    if (isConversational) {
      this.agentChatPayload.push({ content: message, role: 'user' });
    }

    const payload = isConversational ? this.agentChatPayload : message;
    const { levelId } = this.getMetadataFromNavbar();

    console.log('Final payload being sent:', payload);

    this.agentPlaygroundService
      .generatePrompt(
        payload,
        mode,
        isConversational,
        isUseTemplate,
        this.agentAttachment,
        useCaseIdentifier,
        '',
        levelId,
      )
      .pipe(
        finalize(() => {
          this.isProcessingChat = false;
          this.isAgentPlaygroundLoading = false;
        }),
        takeUntil(this.agentPlaygroundDestroy),
      )
      .subscribe({
        next: (generatedResponse: any) => {
          // Store the latest response for display in the output panel
          this.latestAgentResponse = generatedResponse;

          if (
            generatedResponse?.response &&
            generatedResponse?.response?.choices
          ) {
            const aiResponseText = generatedResponse.response.choices[0].text;
            this.chatMessages = [
              ...this.chatMessages,
              { from: 'ai', text: aiResponseText },
            ];
            if (isConversational) {
              this.agentChatPayload.push({
                content: aiResponseText,
                role: 'assistant',
              });
            }
          } else {
            console.warn('Unexpected API response format:', generatedResponse);
            this.showAgentError(
              'Received unexpected response format from API.',
            );
          }
        },
        error: (error: any) => {
          console.error('API Error:', error);
          const errorMessage =
            error?.error?.message ||
            'An error occurred while processing your request.';
          this.showAgentError(errorMessage);
          if (isConversational && this.agentChatPayload.length > 0) {
            this.agentChatPayload.pop();
          }
        },
      });
  }

  private sendAgentMessageToAPIWithFiles(
    message: string,
    mode: string,
    useCaseIdentifier: string,
    isConversational: boolean,
    isUseTemplate: boolean,
    fileContents: string,
  ): void {
    if (isConversational) {
      this.agentChatPayload.push({ content: message, role: 'user' });
    }
    const payload = isConversational ? this.agentChatPayload : message;
    const { levelId } = this.getMetadataFromNavbar();

    this.agentPlaygroundService
      .generatePrompt(
        payload,
        mode,
        isConversational,
        isUseTemplate,
        this.agentAttachment,
        useCaseIdentifier,
        fileContents,
        levelId,
      )
      .pipe(
        finalize(() => {
          this.isProcessingChat = false;
          this.isAgentPlaygroundLoading = false;
        }),
        takeUntil(this.agentPlaygroundDestroy),
      )
      .subscribe({
        next: (generatedResponse: any) => {
          if (
            generatedResponse?.response &&
            generatedResponse?.response?.choices
          ) {
            const aiResponseText = generatedResponse.response.choices[0].text;
            this.chatMessages = [
              ...this.chatMessages,
              { from: 'ai', text: aiResponseText },
            ];
            if (isConversational) {
              this.agentChatPayload.push({
                content: aiResponseText,
                role: 'assistant',
              });
            }
          } else {
            console.warn('Unexpected API response format:', generatedResponse);
            this.showAgentError(
              'Received unexpected response format from API.',
            );
          }
        },
        error: (error: any) => {
          console.error('API Error:', error);
          const errorMessage =
            error?.error?.message ||
            'An error occurred while processing your request.';
          this.showAgentError(errorMessage);
          if (isConversational && this.agentChatPayload.length > 0) {
            this.agentChatPayload.pop();
          }
        },
      });
  }

  // Blueprint panel methods
  private mapAgentConfigurationToBlueprint(agentData: any): void {
    if (!agentData) {
      console.warn('No agent data provided for blueprint');
      return;
    }

    console.log('🔍 DEBUG: Full agent data received:', agentData);
    console.log('🔍 DEBUG: Agent type:', this.agentType);
    console.log('🔍 DEBUG: Agent data keys:', Object.keys(agentData));

    // Clear existing nodes
    this.buildAgentNodes = [];
    this.canvasNodes = [];

    let nodeCounter = 1;

    // Map agent configuration to nodes based on agent type
    if (this.agentType === 'individual') {
      this.mapIndividualAgentToBlueprint(agentData, nodeCounter);
    } else if (this.agentType === 'collaborative') {
      this.mapCollaborativeAgentToBlueprint(agentData, nodeCounter);
    }

    console.log('🎯 Blueprint nodes mapped:', {
      buildAgentNodes: this.buildAgentNodes,
      canvasNodes: this.canvasNodes,
      totalNodes: this.buildAgentNodes.length,
    });
  }

  private mapIndividualAgentToBlueprint(
    agentData: any,
    nodeCounter: number,
  ): void {
    console.log('🔍 Individual agent mapping - checking fields:', {
      config: agentData.config,
      configLength: agentData.config?.length,
      useCaseName: agentData.useCaseName,
      prompt: agentData.prompt,
      useCaseDetails: agentData.useCaseDetails,
    });

    // Clear existing blueprint nodes
    this.blueprintPromptNodes = [];
    this.blueprintModelNodes = [];
    this.blueprintKnowledgeNodes = [];
    this.blueprintGuardrailNodes = [];

    // Add prompt node from "prompt" field
    if (agentData.prompt) {
      this.blueprintPromptNodes.push({
        id: `prompt-${nodeCounter++}`,
        name: agentData.prompt,
        type: 'prompt',
      });
      console.log('✅ Added prompt node:', agentData.prompt);
    }

    // Process the config array to extract model, knowledge bases, and guardrails
    if (agentData.config && Array.isArray(agentData.config)) {
      console.log(
        '🔍 Processing config array with length:',
        agentData.config.length,
      );

      agentData.config.forEach((category: any, categoryIndex: number) => {
        console.log(
          `🔍 Category ${categoryIndex} (ID: ${category.categoryId}):`,
          category.categoryName,
        );

        if (category.config && Array.isArray(category.config)) {
          console.log(
            `🔍 Category ${categoryIndex} has ${category.config.length} config items`,
          );

          category.config.forEach((configItem: any, itemIndex: number) => {
            console.log(`🔍 Config item ${categoryIndex}-${itemIndex}:`, {
              configKey: configItem.configKey,
              configValue: configItem.configValue,
              categoryId: configItem.categoryId,
            });

            // Handle AI Model from categoryId 1
            if (
              configItem.categoryId === 1 &&
              configItem.configKey === 'MODEL' &&
              configItem.configValue
            ) {
              console.log(
                '✅ Adding AI model node from categoryId 1:',
                configItem.configValue,
              );
              this.blueprintModelNodes.push({
                id: `model-${nodeCounter++}`,
                name: `${configItem.configKey}`,
                type: 'model',
              });
            }

            // Handle Knowledge Base from categoryId 2
            if (
              configItem.categoryId === 2 &&
              configItem.configKey === 'RAG_KNOWLEDGEBASE_NAME' &&
              configItem.configValue
            ) {
              console.log(
                '✅ Adding knowledge base nodes from categoryId 2:',
                configItem.configValue,
              );
              const kbValue = configItem.configValue.toString();
              const kbIds = kbValue
                .split(',')
                .map((id: string) => id.trim())
                .filter((id: string) => id);

              kbIds.forEach((kbId: string) => {
                this.blueprintKnowledgeNodes.push({
                  id: `knowledge-${nodeCounter++}`,
                  name: `Knowledge Base: ${kbId}`,
                  type: 'knowledge',
                });
              });
            }

            // Handle Guardrails from categoryId 3 where configValue is true
            if (
              configItem.categoryId === 3 &&
              configItem.configValue === 'true'
            ) {
              console.log('✅ Found enabled guardrail from categoryId 3:', {
                key: configItem.configKey,
                value: configItem.configValue,
              });

              if (configItem.configKey === 'ENABLE_GUARDRAILS') {
                // Only add one general guardrail node if not already added
                if (this.blueprintGuardrailNodes.length === 0) {
                  this.blueprintGuardrailNodes.push({
                    id: `guardrail-${nodeCounter++}`,
                    name: 'Guardrails Enabled',
                    type: 'guardrail',
                  });
                }
              } else if (configItem.configKey.startsWith('GUARDRAIL_')) {
                // Add specific guardrail nodes for enabled guardrails
                let guardrailName = configItem.configKey;
                if (guardrailName.startsWith('GUARDRAIL_')) {
                  guardrailName = guardrailName
                    .replace('GUARDRAIL_', '')
                    .replace(/_/g, ' ');
                }

                this.blueprintGuardrailNodes.push({
                  id: `guardrail-${nodeCounter++}`,
                  name: `${guardrailName}`,
                  type: 'guardrail',
                });
              }
            }
          });
        }
      });
    }

    console.log('🎯 Final blueprint nodes:', {
      promptNodes: this.blueprintPromptNodes,
      modelNodes: this.blueprintModelNodes,
      knowledgeNodes: this.blueprintKnowledgeNodes,
      guardrailNodes: this.blueprintGuardrailNodes,
    });

    // Calculate completion percentage
    const totalRequired = 2; // Prompt + Model are required
    const currentRequired =
      this.blueprintPromptNodes.length + this.blueprintModelNodes.length;
    this.blueprintCompletionPercentage = Math.round(
      (currentRequired / totalRequired) * 100,
    );
  }

  private mapCollaborativeAgentToBlueprint(
    agentData: any,
    nodeCounter: number,
  ): void {
    console.log(
      '🚀 SHARED COMPONENT: mapCollaborativeAgentToBlueprint called!',
    );
    console.log('🔍 DEBUG: Collaborative agent data received:', agentData);
    console.log(
      '🔍 DEBUG: Collaborative agent data keys:',
      Object.keys(agentData),
    );
    console.log('🔍 DEBUG: Agent type in component:', this.agentType);
    console.log('🔍 DEBUG: Current nodeCounter:', nodeCounter);

    // Clear existing blueprint nodes
    this.blueprintPromptNodes = [];
    this.blueprintModelNodes = [];
    this.blueprintKnowledgeNodes = [];
    this.blueprintToolNodes = [];
    this.blueprintGuardrailNodes = [];

    console.log('🔍 DEBUG: Cleared all blueprint node arrays');

    // Add prompt node - handle different prompt structures for collaborative agents
    const shouldCreatePromptNode =
      agentData.goal || agentData.role || agentData.description;

    console.log('🔍 DEBUG: Checking prompt node creation:', {
      goal: agentData.goal,
      role: agentData.role,
      description: agentData.description,
      shouldCreatePromptNode,
    });

    if (shouldCreatePromptNode) {
      let promptNodeName =
        agentData.goal ||
        agentData.role ||
        agentData.description ||
        'Collaborative Agent Prompt';

      // Truncate prompt if too long for display
      if (promptNodeName.length > 150) {
        promptNodeName = promptNodeName.substring(0, 150) + '...';
      }

      this.blueprintPromptNodes.push({
        id: `prompt-${nodeCounter++}`,
        name: promptNodeName,
        type: 'prompt',
      });
      console.log('✅ Added collaborative prompt node:', promptNodeName);
    }

    // Add model nodes - handle both old and new API formats like build-agents
    let modelReferences = [];

    console.log('🔍 DEBUG: Checking model data:', {
      hasAgentConfigs: !!agentData.agentConfigs,
      agentConfigs: agentData.agentConfigs,
      model: agentData.model,
      modelName: agentData.modelName,
      modelDetails: agentData.modelDetails,
    });

    // New API format: agentConfigs.modelRef (array of model IDs or objects)
    if (agentData.agentConfigs && agentData.agentConfigs.modelRef) {
      const modelRefs = Array.isArray(agentData.agentConfigs.modelRef)
        ? agentData.agentConfigs.modelRef
        : [agentData.agentConfigs.modelRef];

      modelReferences = modelRefs.map((ref: any) => {
        if (typeof ref === 'number' || typeof ref === 'string') {
          return { modelId: ref };
        }
        return ref;
      });
    }
    // Old API format: modelDetails
    else if (agentData.modelDetails) {
      modelReferences = [agentData.modelDetails];
    }
    // Fallback: check for model or modelName directly
    else if (agentData.model || agentData.modelName) {
      modelReferences = [{ modelId: agentData.model || agentData.modelName }];
    }

    modelReferences.forEach((modelRef: any) => {
      const modelId = modelRef.modelId || modelRef.id;
      const modelName =
        modelRef.model ||
        modelRef.modelDeploymentName ||
        `Model ID: ${modelId}`;

      this.blueprintModelNodes.push({
        id: `model-${nodeCounter++}`,
        name: modelName,
        type: 'model',
      });
      console.log('✅ Added collaborative model node:', modelName);
    });

    // Add knowledge base nodes - handle both old and new API formats
    let knowledgeReferences = [];

    // New API format: agentConfigs.knowledgeBaseRef (array of KB IDs or objects)
    if (agentData.agentConfigs && agentData.agentConfigs.knowledgeBaseRef) {
      const kbRefs = Array.isArray(agentData.agentConfigs.knowledgeBaseRef)
        ? agentData.agentConfigs.knowledgeBaseRef
        : [agentData.agentConfigs.knowledgeBaseRef];

      knowledgeReferences = kbRefs.map((ref: any) => {
        if (typeof ref === 'number' || typeof ref === 'string') {
          return { knowledgeBaseId: ref };
        }
        return ref;
      });
    }
    // Old API format: knowledgeBase
    else if (
      agentData.knowledgeBase &&
      Array.isArray(agentData.knowledgeBase)
    ) {
      knowledgeReferences = agentData.knowledgeBase;
    }

    knowledgeReferences.forEach((kbRef: any) => {
      const kbId = kbRef.knowledgeBaseId || kbRef.id;
      const collectionName = kbRef.indexCollectionName || kbRef.name;
      const kbName = collectionName || `Knowledge Base ID: ${kbId}`;

      this.blueprintKnowledgeNodes.push({
        id: `knowledge-${nodeCounter++}`,
        name: kbName,
        type: 'knowledge',
      });
      console.log('✅ Added collaborative knowledge node:', kbName);
    });

    // Add tool nodes - handle both old and new API formats like build-agents
    let toolReferences = [];
    let userToolReferences = [];

    console.log('🔍 DEBUG: Checking tool data:', {
      hasAgentConfigs: !!agentData.agentConfigs,
      agentConfigsContent: agentData.agentConfigs,
      hasTools: agentData.tools,
      toolsContent: agentData.tools,
      hasUserTools: agentData.userTools,
      userToolsContent: agentData.userTools,
    });

    // New API format: agentConfigs.toolRef and agentConfigs.userToolRef
    if (agentData.agentConfigs) {
      if (agentData.agentConfigs.toolRef) {
        const toolRefs = Array.isArray(agentData.agentConfigs.toolRef)
          ? agentData.agentConfigs.toolRef
          : [agentData.agentConfigs.toolRef];

        toolReferences = toolRefs.map((ref: any) => {
          if (typeof ref === 'number' || typeof ref === 'string') {
            return { toolId: ref };
          }
          return ref;
        });
      }
      if (agentData.agentConfigs.userToolRef) {
        const userToolRefs = Array.isArray(agentData.agentConfigs.userToolRef)
          ? agentData.agentConfigs.userToolRef
          : [agentData.agentConfigs.userToolRef];

        userToolReferences = userToolRefs.map((ref: any) => {
          if (typeof ref === 'number' || typeof ref === 'string') {
            return { toolId: ref };
          }
          return ref;
        });
      }
    }
    // Old API format: tools and userTools
    else {
      if (agentData.tools && Array.isArray(agentData.tools)) {
        toolReferences = agentData.tools;
      }
      if (agentData.userTools && Array.isArray(agentData.userTools)) {
        userToolReferences = agentData.userTools;
      }
    }

    // Process built-in tools
    toolReferences.forEach((tool: any) => {
      const toolId = tool.toolId || tool.id;
      const toolName = tool.toolName || tool.name || `Tool ID: ${toolId}`;

      this.blueprintToolNodes.push({
        id: `tool-${nodeCounter++}`,
        name: toolName,
        type: 'tool',
      });
      console.log('✅ Added collaborative builtin tool node:', toolName);
    });

    // Process user tools
    userToolReferences.forEach((userTool: any) => {
      const userToolId = userTool.toolId || userTool.id;
      const userToolName =
        userTool.toolName || userTool.name || `User Tool ID: ${userToolId}`;

      this.blueprintToolNodes.push({
        id: `tool-${nodeCounter++}`,
        name: userToolName,
        type: 'tool',
      });
      console.log('✅ Added collaborative user tool node:', userToolName);
    });

    console.log('🎯 Final collaborative blueprint nodes:', {
      promptNodes: this.blueprintPromptNodes,
      modelNodes: this.blueprintModelNodes,
      knowledgeNodes: this.blueprintKnowledgeNodes,
      toolNodes: this.blueprintToolNodes,
      guardrailNodes: this.blueprintGuardrailNodes,
    });

    // Debug: Check blueprint node arrays lengths
    console.log('📊 Blueprint node counts:', {
      promptCount: this.blueprintPromptNodes.length,
      modelCount: this.blueprintModelNodes.length,
      knowledgeCount: this.blueprintKnowledgeNodes.length,
      toolCount: this.blueprintToolNodes.length,
      guardrailCount: this.blueprintGuardrailNodes.length,
    });

    // Debug: Check if tools zone will be visible
    console.log('🔧 Tools zone debug:', {
      agentType: this.agentType,
      isCollaborative: this.agentType === 'collaborative',
      hasToolNodes: this.blueprintToolNodes.length > 0,
      toolNodeNames: this.blueprintToolNodes.map((t) => t.name),
    });

    // Calculate completion percentage
    const totalRequired = 2; // Prompt + Model are required
    const currentRequired =
      this.blueprintPromptNodes.length + this.blueprintModelNodes.length;
    this.blueprintCompletionPercentage = Math.round(
      (currentRequired / totalRequired) * 100,
    );
  }
}
