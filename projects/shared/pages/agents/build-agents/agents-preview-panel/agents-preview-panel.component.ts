import { CommonModule, DatePipe } from '@angular/common';
import { Component, Input } from '@angular/core';
import { AvaTextboxComponent, ButtonComponent, IconComponent, SliderComponent } from '@ava/play-comp-library';
import { PreviewPanelComponent } from '../../../../components/preview-panel/preview-panel.component';

@Component({
  selector: 'app-agents-preview-panel',
  imports: [PreviewPanelComponent,IconComponent,ButtonComponent,DatePipe, CommonModule,SliderComponent,AvaTextboxComponent],
  templateUrl: './agents-preview-panel.component.html',
  styleUrl: './agents-preview-panel.component.scss'
})
export class AgentsPreviewPanelComponent {
  @Input() previewData: any = null;
  @Input() closePreview!: () => void;
  
  showMoreConfig = false;

  toggleConfigDetails(): void {
    this.showMoreConfig = !this.showMoreConfig;
  }

  onButtonClick(event: any): void {
    this.closePreview();
  }

  getAdditionalFields(data: any): { key: string; value: any }[] {
    const excludeFields = ['id', 'name', 'description', 'labelCode', 'categoryName', 'categoryId'];
    return Object.keys(data)
      .filter(key => !excludeFields.includes(key) && data[key] != null)
      .map(key => ({ key, value: data[key] }));
  }

  getFileIconColor(index: number): string {
    const colors = ['#dc2626', '#2563eb', '#16a34a', '#f59e0b', '#8b5cf6'];
    return colors[index % colors.length];
  }

  getButtonLabel(): string {
    const type = this.previewData?.type;
    switch (type) {
      case 'model':
        return 'Edit Model';
      case 'tool':
        return 'Edit Tool';
      case 'prompt':
        return 'Edit Prompt';
      case 'knowledge':
        return 'Edit Knowledge Base';
      case 'guardrail':
        return 'Edit Guardrail';
      default:
        return 'Edit';
    }
  }
}
