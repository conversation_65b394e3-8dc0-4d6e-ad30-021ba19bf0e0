<!-- <h2>Agent Builder</h2> -->
<div class="build-agents-container">
  <!-- Top Navigation Bar -->
  <div class="top-nav-bar">
    <div class="nav-left">
      <button
        class="back-button"
        (click)="navigateToAgentsList()"
        type="button"
      >
        <ava-icon
          iconName="ArrowLeft"
          iconSize="16"
          iconColor="#374151"
        ></ava-icon>
        <span style="font-size: 16px; font-weight: 800">Agent Builder</span>
      </button>
      <div class="nav-center">
        <div class="agent-type-toggle">
          <button
            class="toggle-btn"
            [class.active]="currentAgentType === 'individual'"
            (click)="switchAgentType('individual')"
            type="button"
          >
            Individual
          </button>
          <button
            class="toggle-btn"
            [class.active]="currentAgentType === 'collaborative'"
            (click)="switchAgentType('collaborative')"
            type="button"
          >
            Collaborative
          </button>
        </div>
      </div>
    </div>

    <div class="nav-right">
      <ava-button
        [label]="'Save'"
        variant="primary"
        [customStyles]="{
          background:
            'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
          '--button-effect-color': '33, 90, 214',
          'border-radius': '8px',
        }"
        size="small"
        iconName="Save"
        iconColor="white"
        (userClick)="saveAgentFromTopBar()"
        [disabled]="!canSaveAgent()"
      >
      </ava-button>
    </div>
  </div>

  <!-- Header Navigation -->
  <div class="header-nav">
    <!-- Main Content Area -->
    <div class="main-content" [class.execute-mode]="isExecuteMode">
      <!-- Full Width Canvas Area -->
      <div class="canvas-area" [class.execute-mode]="isExecuteMode">
        <!-- Configure Agent Floating Panel - Visible in both build and execute modes -->
        <div
          class="configure-agent-panel"
          [ngClass]="{ 'p-3': !isSidebarCollapsed }"
          [class.collapsed]="isSidebarCollapsed"
        >
          <div class="panel-header" (click)="toggleSidebar()">
            <h3>Configure Agent</h3>
            <ava-icon
              [iconName]="isSidebarCollapsed ? 'ChevronDown' : 'ChevronUp'"
              iconSize="16"
              iconColor="var(--text-secondary)"
            >
            </ava-icon>
          </div>

          <!-- Panel Content -->
          <div class="panel-content" [class.hidden]="isSidebarCollapsed">
            <!-- Tool Items Section -->
            <div class="tools-section">
              <!-- Ava Tabs with Custom PNG Icons -->
              <div class="custom-tabs-container">
                <app-custom-tabs
                  [tabs]="customTabs"
                  [activeTab]="activeTab"
                  [variant]="'icon'"
                  (tabChange)="onCustomTabChange($event)"
                  class="builder-custom-tabs"
                >
                </app-custom-tabs>
              </div>

              <!-- Search Section -->
              <div class="search-section">
                <form [formGroup]="searchForm">
                  <ava-textbox
                    [placeholder]="'Search ' + activeTab"
                    hoverEffect="glow"
                    pressedEffect="solid"
                    formControlName="search"
                  >
                    <ava-icon
                      slot="icon-start"
                      iconName="search"
                      [iconSize]="16"
                      iconColor="var(--color-brand-primary)"
                    >
                    </ava-icon>
                  </ava-textbox>
                </form>
              </div>

              <!-- Tool Items List -->
              <div class="tools-list">
                <!-- Show tools when available -->
                <div
                  *ngFor="let tool of filteredTools"
                  class="tool-item"
                  draggable="true"
                  (dragstart)="onDragStart($event, tool)"
                >
                  <!-- Header with icon, name and user count in one line -->
                  <div class="tool-header">
                    <div class="tool-icon-box">
                      <!-- Use Lucide icons for all types including prompts -->
                      <ava-icon
                        *ngIf="tool.type === 'prompt'"
                        iconName="FileText"
                        iconSize="20"
                        iconColor="var(--color-brand-primary)"
                      >
                      </ava-icon>
                      <ava-icon
                        *ngIf="tool.type === 'model'"
                        iconName="Box"
                        iconSize="20"
                        iconColor="var(--color-brand-primary)"
                      >
                      </ava-icon>
                      <ava-icon
                        *ngIf="tool.type === 'knowledge'"
                        iconName="BookOpen"
                        iconSize="20"
                        iconColor="var(--color-brand-primary)"
                      >
                      </ava-icon>
                      <ava-icon
                        *ngIf="tool.type === 'tool'"
                        iconName="Wrench"
                        iconSize="20"
                        iconColor="var(--color-brand-primary)"
                      >
                      </ava-icon>
                      <ava-icon
                        *ngIf="tool.type === 'guardrail'"
                        iconName="Swords"
                        iconSize="20"
                        iconColor="var(--color-brand-primary)"
                      >
                      </ava-icon>
                    </div>
                    <h4 class="tool-name">{{ tool.name }}</h4>
                    <div class="tool-count">
                      <ava-icon
                        iconName="Users"
                        iconSize="16"
                        iconColor="#9CA3AF"
                      >
                      </ava-icon>
                      <span class="count-text">120</span>
                    </div>
                  </div>

                  <!-- Description -->
                  <p class="tool-description" *ngIf="tool.type !== 'prompt'">
                    {{ tool.description }}
                  </p>
                  <!-- Prompt Description for prompts -->
                  <p class="tool-description" *ngIf="tool.type === 'prompt'">
                    {{ tool["promptDescription"] || tool.description }}
                  </p>

                  <!-- Preview button -->
                  <div class="tool-actions">
                    <ava-button
                      label="Preview"
                      size="small"
                      [pill]="true"
                      variant="secondary"
                      (userClick)="onItemPreview(tool)"
                      class="preview-btn"
                    >
                    </ava-button>
                  </div>
                </div>

                <!-- Show no results message when search returns empty and search query exists -->
                <div
                  *ngIf="
                    filteredTools.length === 0 &&
                    searchQuery &&
                    searchQuery.trim().length > 0
                  "
                  class="no-results-message"
                >
                  <div class="no-results-content">
                    <ava-icon
                      iconName="Search"
                      iconSize="24"
                      iconColor="#9CA3AF"
                    >
                    </ava-icon>
                    <p>
                      No {{ activeTab }} found matching your search criteria
                    </p>
                  </div>
                </div>
              </div>

              <!-- Create New Item Button -->
              <div class="create-tool-section">
                <ava-button
                  [label]="'Create New ' + getActiveTabLabel()"
                  variant="primary"
                  [customStyles]="{
                    background:
                      'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                    '--button-effect-color': '33, 90, 214',
                    'border-radius': '8px',
                  }"
                  size="large"
                  iconName="Plus"
                  iconColor="white"
                  (userClick)="onCreateNewItem()"
                  [width]="'100%'"
                >
                </ava-button>
              </div>
            </div>
          </div>
        </div>

        <div class="editor-canvas">
          <!-- Agent Details Floating Panel -->
          <div
            class="agent-details-floater"
            *ngIf="!isExecuteMode"
            [class.collapsed]="isAgentDetailsCollapsed"
            [class.pulsating]="!agentName"
          >
            <div class="floater-header" (click)="toggleAgentDetailsFloater()">
              <h4>
                Agent Details
                <span style="color: red">*</span>
              </h4>
              <ava-icon
                [iconName]="
                  isAgentDetailsCollapsed ? 'ChevronDown' : 'ChevronUp'
                "
                iconSize="16"
                iconColor="var(--text-secondary)"
              >
              </ava-icon>
            </div>

            <div class="floater-content" *ngIf="!isAgentDetailsCollapsed">
              <div class="form-fields">
                <!-- Agent Name Field -->
                <div class="field-group">
                  <label class="field-label">Agent Name</label>
                  <ava-textbox
                    placeholder="Enter agent name"
                    [(ngModel)]="agentName"
                    variant="primary"
                    size="md"
                    [fullWidth]="true"
                    class="agent-name-field"
                  >
                  </ava-textbox>
                </div>

                <!-- Agent Description Field -->
                <div class="field-group">
                  <label class="field-label">Description</label>
                  <ava-textarea
                    placeholder="Enter agent description"
                    [(ngModel)]="agentDetail"
                    class="agent-description-field"
                  >
                  </ava-textarea>
                </div>
              </div>
            </div>
          </div>

          <!-- New Drop Zone Canvas -->
          <app-drop-zone-canvas
            [config]="dropZoneConfig"
            [nodes]="canvasNodes"
            (nodeDropped)="onDropZoneNodeDropped($event)"
            (nodeDeleted)="onDropZoneNodeDeleted($event)"
            (guardrailToggled)="onGuardrailToggled($event)"
          >
            <!-- Node template for rendering agent nodes in drop zones -->
            <ng-template
              #nodeTemplate
              let-node
              let-selected="selected"
              let-onDelete="onDelete"
              let-canvasMode="canvasMode"
            >
              <app-build-agent-node
                [node]="node"
                [selected]="selected"
                [mouseInteractionsEnabled]="true"
                [canvasMode]="canvasMode"
                [executeNodeData]="getExecuteNodeData(node)"
                (deleteNode)="onDelete($event)"
              >
              </app-build-agent-node>
            </ng-template>
          </app-drop-zone-canvas>
        </div>
      </div>

      <!-- Playground Component - Shows when in execute mode -->
      <div class="playground-area" *ngIf="isExecuteMode && showChatInterface">
        <button
          class="exit-execute-btn"
          (click)="onExitExecuteMode()"
          title="Exit Execute Mode"
        ></button>

        <app-playground
          [messages]="chatMessages"
          [isLoading]="isProcessingChat"
          [agentType]="currentAgentType"
          [showChatInteractionToggles]="currentAgentType === 'individual'"
          [showAiPrincipleToggle]="true"
          [showApprovalButton]="showApprovalButton"
          [showDropdown]="false"
          [showAgentNameInput]="true"
          [showFileUploadButton]="true"
          [displayedAgentName]="agentName"
          [agentNamePlaceholder]="'Current Agent Name'"
          [acceptedFileType]="
            currentAgentType === 'collaborative'
              ? '.zip'
              : '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg'
          "
          (promptChange)="onPromptChanged($event)"
          (messageSent)="handleChatMessage($event)"
          (conversationalToggle)="onPlaygroundConversationalToggle($event)"
          (templateToggle)="onPlaygroundTemplateToggle($event)"
          (filesSelected)="onFilesSelected($event)"
          (approvalRequested)="onApprovalRequested()"
        >
        </app-playground>
      </div>

      <!-- Preview Panel -->
    </div>
  </div>

  <!-- Success Popup -->
  <ava-popup
    [show]="showSuccessPopup"
    [message]="popupMessage"
    [showHeaderIcon]="true"
    headerIconName="check-circle"
    iconColor="#28a745"
    [showClose]="true"
    [showCancel]="true"
    [showConfirm]="true"
    [confirmButtonLabel]="'Execute Agent'"
    [cancelButtonLabel]="'Go to Agents Library'"
    [confirmButtonVariant]="'primary'"
    [cancelButtonVariant]="'secondary'"
    [confirmButtonBackground]="
      'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)'
    "
    (confirm)="onSuccessExecuteAgent()"
    (cancel)="onSuccessGoToLibrary()"
    (closed)="closeSuccessPopup()"
  >
  </ava-popup>

  <!-- Error Popup -->
  <ava-popup
    [show]="showErrorPopup"
    [message]="popupMessage"
    [showHeaderIcon]="true"
    headerIconName="alert-circle"
    iconColor="#dc3545"
    [showClose]="true"
    [showCancel]="false"
    [showConfirm]="true"
    [confirmButtonLabel]="'OK'"
    [confirmButtonVariant]="'primary'"
    [confirmButtonBackground]="'#dc3545'"
    (confirm)="closeErrorPopup()"
    (closed)="closeErrorPopup()"
  >
  </ava-popup>

  <!-- Warning Popup -->
  <ava-popup
    [show]="showWarningPopup"
    [message]="popupMessage"
    [showHeaderIcon]="true"
    headerIconName="alert-triangle"
    iconColor="#ffc107"
    [showClose]="true"
    [showCancel]="true"
    [showConfirm]="true"
    [confirmButtonLabel]="'Continue'"
    [cancelButtonLabel]="'Cancel'"
    [confirmButtonVariant]="'primary'"
    [confirmButtonBackground]="'#ffc107'"
    (confirm)="onWarningConfirm()"
    (cancel)="onWarningCancel()"
    (closed)="closeWarningPopup()"
  >
  </ava-popup>

  <!-- Approval Confirmation Popup removed - approval now handled through canvas button -->
</div>
