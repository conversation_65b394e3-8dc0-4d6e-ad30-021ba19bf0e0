
.preview-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.panel-container {
  position: relative;
  z-index: 1000;
  width: 480px;
  max-height: 90vh;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
  display: flex;
  flex-direction: column;
}


.panel-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.close-btn {
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f3f4f6;
  }
}
.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.preview-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: calc(90vh - 140px);  
  padding-top:40px;
  border-top: 1px solid #e5e7eb;
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(0, 0, 0, 0.5);
    }
  }
}

.preview-field {
  margin-bottom: 20px;
  
  label {
    display: block;
    font-weight: 600;
    margin-bottom: 6px;
    color: #374151;
    font-size: 0.875rem;
  }
  
  span {
    display: block;
    color: #1f2937;
    font-size: 0.875rem;
    line-height: 1.5;
  }
}

.agent-preview {
  .agent-section {
    margin-bottom: 24px;
    
    h3 {
      font-size: 0.875rem;
      font-weight: 700;
      color: #1f2937;
      margin: 0 0 16px 0;
      letter-spacing: 0.05em;
    }
    
    .agent-name {
      font-size: 1.125rem;
      font-weight: 600;
      color: #1f2937;
      line-height: 1.5;
    }
    
    .agent-description {
      font-size: 0.875rem;
      color: #1f2937;
      line-height: 1.6;
    }
  }

  .agent-meta {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e5e7eb;
    
    .meta-row {
      display: flex;
      gap: 4rem;
      
      .meta-item {
        label {
          display: block;
          font-size: 0.875rem;
          color: #1f2937;
          margin-bottom: 4px;
          font-weight: 600;
          letter-spacing: 0.05em;
        }
        
        .meta-value {
          font-weight: 500;
          color: #1f2937;
          font-size: 0.875rem;
        }
      }
    }
  }

  ava-accordion {
    margin-bottom: 16px;
    border-radius: 6px;

    ::ng-deep {
      .accordion-container {
        border: 1px solid #e5e7eb !important;
        background: #ffffff;
        box-shadow: none;
        border-radius: 6px;
        
        .accordion-header {
          padding: 12px 16px;
          background: #ffffff;
          border: none;
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: space-between;
          
          &:hover {
            background: #f9fafb;
          }
        }
        
        .accordion-icon {
          display: flex !important;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
          border: 1px solid #d1d5db;
          border-radius: 4px;
          background: #ffffff;
          transition: all 0.2s ease;
          
          &:hover {
            border-color: #9ca3af;
            background: #f9fafb;
          }
          
          ava-icon {
            width: 12px;
            height: 12px;
          }
        }
        
        .accordion-content {
          .accordion-body {
            padding: 0 !important;
          }
        }
      }
    }
  }

  .config-header-content {
    display: flex;
    align-items: center;
    flex: 1;
    
    ava-icon:first-child {
      background: #f3f4f6;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      padding: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      height: auto !important;
    }
    
    .config-label {
      margin-left: 12px;
      font-size: 1rem;
      font-weight: 700;
      color: #374151;
      flex: 1;
    }
  }

  .config-content {
    padding: 16px;
    
    .prompt-details,
    .model-details,
    .knowledgebase-details,
    .tools-details {
      .config-field {
        margin-bottom: 20px;
        
        label {
          display: block;
          font-size: 0.875rem;
          font-weight: 600;
          color: #1a1d29;
          margin-bottom: 8px;
        }
        
        .field-value {
          font-size: 0.875rem;
          color: #1f2937;
          line-height: 1.6;
          
          &.description-text {
            color: #6b7280;
          }
        }
        
        .prompt-content {
          padding: 16px;
          background: #f8f9fa;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          color: #1a1d29;
          font-size: 0.875rem;
          line-height: 1.6;
          min-height: 120px;
          white-space: pre-wrap;
          max-height: 300px;
          overflow-y: auto;
        }
      }
    }
    
    .guardrails-details {
      .guardrail-item {
        margin-bottom: 24px;
        
        .guardrail-separator {
          border: none;
          border-top: 1px solid #e5e7eb;
          margin: 24px 0;
        }
      }
    }
    
    .no-config {
      text-align: center;
      color: #6b7280;
      font-style: italic;
      padding: 20px;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .panel-container {
    width: 95vw;
    max-width: 400px;
    margin: 0 auto;
  }

  
  .panel-title {
    font-size: 1.25rem;
  }
  
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .panel-container {
    background: #1f2937;
    color: #f9fafb;
  }
  
  .preview-header {
    background: #111827;
    border-bottom-color: #374151;
  }
  
  .panel-title {
    color: #f9fafb;
  }
  
  .close-btn:hover {
    background-color: #374151;
  }
  
  .preview-field {
    label {
      color: #d1d5db;
    }
    
    span {
      color: #9ca3af;
    }
  }
  
  .agent-preview .agent-name {
    color: #f9fafb;
  }
}

:host ::ng-deep ava-accordion .accordion-container {
  max-width: none !important;
  width: 100% !important; // reinforces full width
}

:host ::ng-deep ava-accordion .accordion-container .accordion-content {
  max-height: 100%;
}
ava-accordion ::ng-deep .accordion-container .accordion-body {
    padding: 0%;
  }
:host ::ng-deep .accordion-container .accordion-header {
  width: auto !important;
  max-width: none !important;
}


.model-separator,
.kb-separator {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 20px 0;
}

.model-item,
.kb-item {
  margin-bottom: 20px;
}
 .model-section {
    margin-bottom: 32px;
    
    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1a1d29;
      margin: 0 0 24px 0;
    }
  }
  .code-content {
      padding: 16px;
      background: #f8f9fa;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      color: #1a1d29;
      font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
      font-size: 0.875rem;
      line-height: 1.6;
      min-height: 200px;
      max-height: 400px;
      white-space: pre-wrap;
      
      // Always show the border and background, even when empty
      &:empty::before {
        content: 'No configuration available';
        color: #9ca3af;
        font-style: italic;
      }  

  // Hide scrollbar while keeping scroll functionality
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
  }
}
::ng-deep app-preview-panel .preview-panel > .preview-header {
  padding: 24px !important;
}

::ng-deep app-preview-panel .preview-panel > .preview-content {
  padding: 0 24px !important;
}

