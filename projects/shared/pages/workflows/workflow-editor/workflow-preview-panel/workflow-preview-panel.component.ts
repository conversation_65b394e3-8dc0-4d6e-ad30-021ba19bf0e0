import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PreviewPanelComponent } from '@shared/components/preview-panel/preview-panel.component';
import {
  IconComponent,
  AccordionComponent,
} from '@ava/play-comp-library';

@Component({
  selector: 'app-workflow-preview-panel',
  imports: [ PreviewPanelComponent,IconComponent,CommonModule,AccordionComponent],
  templateUrl: './workflow-preview-panel.component.html',
  styleUrl: './workflow-preview-panel.component.scss'
})
export class WorkflowPreviewPanelComponent implements OnInit {
  @Input() previewData: any = null;
  @Input() closePreview!: () => void;
  
  ngOnInit() {
    console.log('Preview Data:', this.previewData);
    console.log('Guardrails Data:', this.previewData?.data?.agentConfigs?.guardrailRef);
  }
  
  onButtonClick(event: any): void {
    this.closePreview();
  }

  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  }

  hasPromptData(): boolean {
    return !!(this.previewData?.data?.role || 
              this.previewData?.data?.goal || 
              this.previewData?.data?.backstory);
  }

  hasModelData(): boolean {
    return !!(this.previewData?.data?.agentConfigs?.modelRef && 
              this.previewData.data.agentConfigs.modelRef.length > 0);
  }

  hasKnowledgebaseData(): boolean {
    return !!(this.previewData?.data?.agentConfigs?.knowledgeBaseRef && 
              this.previewData.data.agentConfigs.knowledgeBaseRef.length > 0);
  }

  hasGuardrailsData(): boolean {
    return !!(this.previewData?.data?.agentConfigs?.guardrailRef && 
              this.previewData.data.agentConfigs.guardrailRef.length > 0);
  }

  hasToolsData(): boolean {
    return !!((this.previewData?.data?.agentConfigs?.toolRef && 
               this.previewData.data.agentConfigs.toolRef.length > 0) ||
              (this.previewData?.data?.agentConfigs?.userToolRef && 
               this.previewData.data.agentConfigs.userToolRef.length > 0));
  }
}
