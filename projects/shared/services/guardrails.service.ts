import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { EnvironmentService } from './environment.service';
import { Observable, map, catchError, of } from 'rxjs';
import { Guardrail } from '../models/card.model';

@Injectable({
  providedIn: 'root'
})
export class GuardrailsService {
  private headers = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    })
  };

  constructor(
    private http: HttpClient,
    private environmentService: EnvironmentService
  ) { }

  private get baseUrl(): string {
    return this.environmentService.consoleApi;
  }

  /**
   * Fetches a specific guardrail by ID
   * @param guardrailId - The ID of the guardrail to fetch
   * @returns Observable emitting the guardrail data
   */
  getGuardrailById(guardrailId: string | number): Observable<any> {
    const url = `${this.baseUrl}/ava/force/guardrail?guardrailId=${guardrailId}`;
    return this.http.get<any>(url, this.headers).pipe(
      map((response: any) => {
        return response.guardrail;
      }),
      catchError((error: any) => {
        console.error('API error fetching guardrail by ID:', error);
        return of(null);
      })
    );
  }
  
  /**
   * Fetches a specific guardrail by name
   * @param guardrailName - The name of the guardrail to fetch
   * @returns Observable emitting the guardrail data or null if not found
   */
  getGuardrailByName(guardrailName: string): Observable<any> {
    const url = `${this.baseUrl}/ava/force/guardrail?guardrailName=${guardrailName}`;
    return this.http.get<any>(url, this.headers).pipe(
      map((response: any) => response.guardrails?.[0] || response.guardrail || null),
      catchError((error: any) => {
        console.error('API error fetching guardrail by name:', error);
        return of(null);
      })
    );
  }
  
  /**
   * Fetch all guardrails
   * @returns Observable of guardrails list
   */
  fetchAllGuardrails(): Observable<Guardrail[]> {
  const url = `${this.baseUrl}/ava/force/guardrail`;
  return this.http.get<{ guardrails: Guardrail[] }>(url, this.headers).pipe(
    map(response => response.guardrails ?? []),
    catchError(err => {
      console.error('Error fetching guardrails', err);
      return of([]);
    })
  );
}

  /**
   * Add a new guardrail
   * @param payload Data for new guardrail
   */
  addGuardrail(payload: any): Observable<any> {
    const url = `${this.baseUrl}/ava/force/guardrail`;
    return this.http.post(url, payload, this.headers).pipe(
      map(res => res),
      catchError(error => {
        console.error('Error adding guardrail', error);
        return of(error);
      })
    );
  }

  /**
   * Update an existing guardrail
   * @param payload Updated guardrail object (must include id)
   */
  updateGuardrail(payload: any): Observable<any> {
    const url = `${this.baseUrl}/ava/force/guardrail`;
    return this.http.put(url, payload, this.headers).pipe(
      map(res => res),
      catchError(error => {
        console.error('Error updating guardrail', error);
        return of(error);
      })
    );
  }

  /**
   * Delete guardrail by ID
   * @param guardrailId The ID to delete
   */
  deleteGuardrail(guardrailId: number): Observable<any> {
    const url = `${this.baseUrl}/ava/force/guardrail`;
    const params = new HttpParams().set('guardrailId', guardrailId.toString());
    return this.http.delete(url, { ...this.headers, params });
  }
}
