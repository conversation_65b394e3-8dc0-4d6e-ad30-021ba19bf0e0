# =============================================================================
# SAMPLE ENVIRONMENT VARIABLES FOR DEVOPS
# =============================================================================
# This file shows all environment variables that must be provided by DevOps
# Copy these variables to your deployment pipeline/infrastructure configuration
# =============================================================================

# =============================================================================
# Core Configuration
# =============================================================================
NODE_ENV=production
BASE_URL=https://your-domain.com

# =============================================================================
# Application URLs
# =============================================================================
ELDER_WAND_URL=https://your-domain.com/launchpad/dashboard
EXPERIENCE_STUDIO_URL=https://your-domain.com/experience/
PRODUCT_STUDIO_URL=https://your-domain.com/product
CONSOLE_REDIRECT_URL=https://your-domain.com/console
CONSOLE_URL=https://your-domain.com/console
CONSOLE_REDIRECT_URI=https://your-domain.com/console

# =============================================================================
# API Configuration
# =============================================================================
API_VERSION=v1
CONSOLE_API=https://your-domain.com/v1/api/admin
CONSOLE_API_V2=https://your-domain.com/v2/api/admin
CONSOLE_API_AUTH_URL=https://your-domain.com/api/auth
CONSOLE_EMBEDDING_API=https://your-domain.com/v1/api/embedding
CONSOLE_INSTRUCTION_API=https://your-domain.com/v1/api/instructions
CONSOLE_LANGFUSE_URL=https://your-langfuse-instance.com/project/your-project-id
CONSOLE_TRUELENS_URL=https://your-truelens-instance.com/
CONSOLE_PIPELINE_API=https://your-domain.com/force/platform/pipeline/api/v1
EXPERIENCE_API_URL=https://your-domain.com/api/experience
PRODUCT_API_URL=https://your-domain.com/api/product

# =============================================================================
# Experience Studio Specific Variables
# =============================================================================
EXPERIENCE_BASE_URL=https://your-experience-api-domain.com
EXPERIENCE_API_AUTH_URL=https://your-domain.com/api/auth
EXPERIENCE_REDIRECT_URL=https://your-domain.com/experience/

# =============================================================================
# Product Studio Specific Variables
# =============================================================================
PIPELINE_API_BASE_URL=https://your-domain.com/server/product/api/v1

# =============================================================================
# Logging and Application Settings
# =============================================================================
ENABLE_LOG_STREAMING=all
LOG_STREAMING_API_URL=wss://your-domain.com/ws-pipeline-log-stream
APP_VERSION=1.0.0
WORKFLOW_EXECUTION_MODE=all
USE_BASIC_LOGIN=false

# =============================================================================
# Security
# =============================================================================
ACCESS_KEY=your-secure-access-key

# =============================================================================
# DEPLOYMENT INSTRUCTIONS FOR DEVOPS
# =============================================================================
# 1. Replace all "your-domain.com" with your actual domain
# 2. Set appropriate values for your environment (dev/staging/prod)
# 3. Ensure ACCESS_KEY is securely generated and stored
# 4. Configure your container orchestration to inject these as environment variables
# 5. These variables will be processed by envsubst in the Docker container startup
# ============================================================================= 