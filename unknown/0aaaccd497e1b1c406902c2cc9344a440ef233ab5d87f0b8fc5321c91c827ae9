import {
  Component,
  Input,
  OnInit,
  AfterViewInit,
  Output,
  EventEmitter,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '@ava/play-comp-library';

export interface ConsoleCardAction {
  id: string;
  icon: string;
  label: string;
  tooltip: string;
  isPrimary?: boolean;
  disabled?: boolean;
}

@Component({
  selector: 'ava-console-card',
  standalone: true,
  imports: [CommonModule, IconComponent],
  templateUrl: './console-card.component.html',
  styleUrl: './console-card.component.scss',
})
export class ConsoleCardComponent implements OnInit, AfterViewInit {
  @Input() title = '';
  @Input() description = '';
  @Input() categoryIcon = 'bot';
  @Input() categoryTitle = 'Agents';
  @Input() categoryValue = '75';
  @Input() author = '';
  @Input() date = '';
  @Input() variant:
    | 'primary'
    | 'secondary'
    | 'tertiary'
    | 'quaternary'
    | 'quinary'
    | 'senary' = 'primary';
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() disabled = false;
  @Input() loading = false;
  @Input() skeleton = false;

  // Dynamic action buttons
  @Input() actions: ConsoleCardAction[] = [
    {
      id: 'view',
      icon: 'calendar-days',
      label: 'View details',
      tooltip: 'View Details',
    },
    {
      id: 'delete',
      icon: 'trash',
      label: 'Delete item',
      tooltip: 'Delete',
    },
    {
      id: 'copy',
      icon: 'copy',
      label: 'Copy to clipboard',
      tooltip: 'Copy',
    },
    {
      id: 'play',
      icon: 'play',
      label: 'Execute or play',
      tooltip: 'Play',
      isPrimary: true,
    },
  ];

  // Event emitters for action clicks
  @Output() actionClick = new EventEmitter<{
    actionId: string;
    action: ConsoleCardAction;
  }>();

  constructor() {
    // console.log('🟡 ConsoleCardComponent constructor called');
  }

  ngOnInit() {
    // console.log('🟢 ConsoleCard ngOnInit - inputs:', {
    //   title: this.title,
    //   description: this.description,
    //   categoryTitle: this.categoryTitle,
    //   categoryValue: this.categoryValue,
    //   author: this.author,
    //   date: this.date,
    //   variant: this.variant,
    //   skeleton: this.skeleton,
    //   actions: this.actions,
    // });
  }

  ngAfterViewInit() {
    // console.log(
    //   '🔵 ConsoleCard ngAfterViewInit - all inputs should be set now:',
    //   {
    //     title: this.title,
    //     description: this.description,
    //     categoryTitle: this.categoryTitle,
    //     categoryValue: this.categoryValue,
    //     author: this.author,
    //     date: this.date,
    //     skeleton: this.skeleton,
    //     actions: this.actions,
    //   },
    // );
  }

  onActionClick(action: ConsoleCardAction): void {
    if (!action.disabled && !this.disabled && !this.loading && !this.skeleton) {
      console.log('🔄 Action clicked:', action);
      this.actionClick.emit({ actionId: action.id, action });
    }
  }

  /**
   * Get truncated description (200 characters max)
   */
  get truncatedDescription(): string {
    if (!this.description) return '';
    if (this.description.length <= 200) return this.description;
    return this.description.substring(0, 200) + '...';
  }

  /**
   * Check if description is truncated
   */
  get isDescriptionTruncated(): boolean {
    return !!(this.description && this.description.length > 200);
  }
}
