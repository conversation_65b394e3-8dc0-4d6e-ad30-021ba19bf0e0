import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TestimonialCardComponent, Testimonial } from '../testimonial-card/testimonial-card.component';

@Component({
  selector: 'app-testimonials-section',
  standalone: true,
  imports: [CommonModule, TestimonialCardComponent],
  templateUrl: './testimonials-section.component.html',
  styleUrl: './testimonials-section.component.scss',
})
export class TestimonialsSectionComponent {
  testimonials: Testimonial[] = [
    {
      id: 1,
      avatar: 'assets/icons/ellipse-avatar.svg',
      quote: 'Simply the best. Better than all the rest. I\'d recommend this product to beginners and advanced users.',
      authorName: '<PERSON>',
      authorRole: 'Backend Engineer'
    },
    {
      id: 2,
      avatar: 'assets/icons/ellipse-avatar.svg',
      quote: 'Must have book for all, who want to be Product Designer or Interaction Designer.',
      author<PERSON>ame: '<PERSON>',
      authorRole: 'Designer'
    },
    {
      id: 3,
      avatar: 'assets/icons/ellipse-avatar.svg',
      quote: 'Must have book for all, who want to be Product Designer or Interaction Designer.',
      authorName: '<PERSON>',
      authorRole: 'Designer'
    },
    {
      id: 4,
      avatar: 'assets/icons/ellipse-avatar.svg',
      quote: 'Must have book for all, who want to be Product Designer or Interaction Designer.',
      authorName: 'Isabella Chavez',
      authorRole: 'Designer'
    }
  ];

  trackByTestimonial(index: number, testimonial: Testimonial): any {
    return testimonial.id || index;
  }
} 