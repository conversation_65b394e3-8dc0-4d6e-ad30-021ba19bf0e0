.right-panel-header {
  display: flex;
  min-height: 54px;
  padding: 9px 26px;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  border-radius: 12px;
  background: var(--page-header-bg);
  margin-bottom: 1rem;

  .header-title-container {
    display: flex;
    align-items: center;
    min-height: 40px; // Prevent layout shift during loading
  }

  .header-title {
    font-size: 20px;
    font-weight: bold;
    color: #fff;
    margin: 0;
  }

  // Header Loading Styles
  .header-loading-container {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .header-loader {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .header-spinner {
      width: 24px;
      height: 24px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top: 2px solid #fff;
      border-radius: 50%;
      animation: header-spin 1s linear infinite;
    }

    .header-loading-text {
      color: #fff;
      font-size: 16px;
      font-weight: 600;
      letter-spacing: -0.2px;
    }
  }

  .header-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;

    .nav-button {
      min-width: 0;
      border: none;
      height: 40px;
      width: 40px;
      border-radius: 50px;
      background: #fff;
      margin-top: 0;
      line-height: none;
      min-height: 0;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &:hover:not(:disabled):not(.loading) {
        background-color: #f8fafc;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }

      &:active:not(:disabled):not(.loading) {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &.disabled,
      &:disabled {
        opacity: 0.4;
        cursor: not-allowed;
        background-color: #f9fafb;
        transform: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &.loading {
        background-color: #7c3aed;
        cursor: not-allowed;
        
        .button-loader {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .button-spinner {
          width: 16px;
          height: 16px;
          border: 2px solid rgba(255, 255, 255, 0.3);
          border-top: 2px solid white;
          border-radius: 50%;
          animation: button-spin 1s linear infinite;
        }
      }

      .nav-icon {
        width: 18px;
        height: 18px;
        color: #6b7280;
        transition: color 0.2s ease;
      }

      &:hover:not(:disabled):not(.loading) .nav-icon {
        color: #7c3aed;
      }
    }
  }
}

// Header Navigation Animations
@keyframes header-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes button-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
