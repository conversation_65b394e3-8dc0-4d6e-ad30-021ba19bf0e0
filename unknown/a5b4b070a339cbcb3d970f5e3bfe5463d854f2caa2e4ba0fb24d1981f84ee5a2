# Environment Variables Setup for DevOps

## Overview
This application requires environment variables to be provided by the DevOps pipeline/infrastructure. **No environment variables should be hardcoded in the application code.**

## How It Works

### 1. Template Processing
- The application uses `env-config-template.js` as a template
- Docker containers use `envsubst` to replace `${VARIABLE_NAME}` placeholders with actual environment variable values
- The processed file becomes `/assets/env.js` which is loaded by the frontend

### 2. Environment Variable Injection
The Docker containers expect environment variables to be injected at runtime:

```bash
# Example Docker run command
docker run -e BASE_URL=https://production.domain.com \
           -e CONSOLE_API=https://production.domain.com/v1/api/admin \
           -e ACCESS_KEY=secure-production-key \
           your-image:tag
```

### 3. Kubernetes/Docker Compose Example

#### Kubernetes ConfigMap
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: aava-config
data:
  BASE_URL: "https://aava-prod.company.com"
  CONSOLE_API: "https://aava-prod.company.com/v1/api/admin"
  API_VERSION: "v1"
  EXPERIENCE_BASE_URL: "https://aava-experience-api.company.com"
  PIPELINE_API_BASE_URL: "https://aava-prod.company.com/server/product/api/v1"
  # ... other variables
```

#### Kubernetes Secret
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: aava-secrets
type: Opaque
data:
  ACCESS_KEY: <base64-encoded-access-key>
```

#### Docker Compose
```yaml
version: '3.8'
services:
  console:
    image: aava-console:latest
    environment:
      - BASE_URL=https://aava-prod.company.com
      - CONSOLE_API=https://aava-prod.company.com/v1/api/admin
      - ACCESS_KEY=${ACCESS_KEY}
    env_file:
      - .env.production
```

## Required Environment Variables

See `sample.env` for the complete list of required environment variables.

### Critical Variables (Must be provided)
- `BASE_URL` - The base URL of your deployment
- `CONSOLE_API` - Console API endpoint
- `CONSOLE_API_AUTH_URL` - Authentication endpoint
- `NODE_ENV` - Environment type (production/development)

### Application-Specific Variables
- `EXPERIENCE_BASE_URL` - Experience Studio API base URL
- `PIPELINE_API_BASE_URL` - Product Studio pipeline API URL
- `EXPERIENCE_API_AUTH_URL` - Experience Studio auth URL
- `EXPERIENCE_REDIRECT_URL` - Experience Studio redirect URL

### Security Variables
- `ACCESS_KEY` - Secure access key (store in secrets, not plain text)

## Environment-Specific Configurations

### Development
```bash
BASE_URL=https://aava-dev.company.com
NODE_ENV=development
CONSOLE_API=https://aava-dev.company.com/v1/api/admin
EXPERIENCE_BASE_URL=https://aava-experience-api-dev.company.com
PIPELINE_API_BASE_URL=https://aava-dev.company.com/server/product/api/v1
```

### Staging
```bash
BASE_URL=https://aava-staging.company.com
NODE_ENV=staging
CONSOLE_API=https://aava-staging.company.com/v1/api/admin
EXPERIENCE_BASE_URL=https://aava-experience-api-staging.company.com
PIPELINE_API_BASE_URL=https://aava-staging.company.com/server/product/api/v1
```

### Production
```bash
BASE_URL=https://aava-prod.company.com
NODE_ENV=production
CONSOLE_API=https://aava-prod.company.com/v1/api/admin
EXPERIENCE_BASE_URL=https://aava-experience-api-prod.company.com
PIPELINE_API_BASE_URL=https://aava-prod.company.com/server/product/api/v1
```

## Validation

The application includes environment variable validation:
- Missing required variables will be logged as errors
- The browser console will show whether environment variables were loaded successfully
- Look for these messages:
  - ✅ `Environment configuration loaded from DevOps`
  - ❌ `Missing required environment variables`

## Local Development

For local development only, the application falls back to hardcoded values when running on `localhost`. This should **never** be used in production.

## Troubleshooting

### "Environment variable 'baseUrl' is not defined"
- Environment variables are not being injected properly
- Check your deployment configuration
- Verify the Docker container startup command includes `envsubst`

### "Environment variable 'experienceBaseUrl' is not defined"
- Experience Studio specific variable is missing
- Add `EXPERIENCE_BASE_URL` to your environment configuration
- See `sample.env` for the complete list of required variables

### "Environment variable 'pipelineApiBaseUrl' is not defined"
- Product Studio specific variable is missing
- Add `PIPELINE_API_BASE_URL` to your environment configuration
- See `sample.env` for the complete list of required variables

### "MIME type ('text/html') is not executable"
- The `/assets/env.js` file is not being generated
- Check that `envsubst` is running in the container startup
- Verify nginx configuration is serving the file with correct MIME type

### Environment variables show as `${VARIABLE_NAME}`
- Environment variables are not set in the deployment environment
- Check your container orchestration configuration
- Ensure variables are available to the container at runtime

## Container Startup Command

The Docker containers should start with:
```bash
envsubst < /usr/share/nginx/html/assets/env-config.template.js > /usr/share/nginx/html/assets/env.js && exec nginx -g 'daemon off;'
```

This command:
1. Processes the template file with `envsubst`
2. Generates the final `env.js` file
3. Starts nginx to serve the application

## Complete Deployment Checklist

### ✅ Environment Variables
- [ ] All variables from `sample.env` are configured in your deployment pipeline
- [ ] Sensitive variables (ACCESS_KEY) are stored in secrets management
- [ ] Environment-specific values are set correctly (dev/staging/prod)

### ✅ Docker Configuration
- [ ] All Dockerfiles copy `env-config-template.js` to the correct location
- [ ] Container startup command includes `envsubst` processing
- [ ] Environment variables are injected at runtime

### ✅ Nginx Configuration
- [ ] `/assets/env.js` is served with `Content-Type: application/javascript`
- [ ] Nested route handling works (e.g., `/console/build/agents/assets/env.js`)
- [ ] All app base paths are configured (`/console/`, `/launchpad/`, `/experience/`, `/product/`)

### ✅ Application Configuration
- [ ] All `index.html` files use dynamic script loading
- [ ] Local development uses `localEnvSetup.js` only
- [ ] Production uses DevOps-provided `assets/env.js` only

### ✅ Testing
- [ ] Local development works without errors
- [ ] Production deployment loads environment variables correctly
- [ ] Deep/nested routes work without MIME type errors
- [ ] All applications (Console, Elder Wand, Experience Studio, Product Studio) work correctly 