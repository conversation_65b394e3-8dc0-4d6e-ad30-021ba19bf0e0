import { Component, OnInit } from '@angular/core';
import { CommonModule, formatDate } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Router } from '@angular/router';
import { SharedApiServiceService } from '../../shared/services/shared-api-service.service';
import {
  ApprovalCardComponent,
  AvaTagComponent,
  ButtonComponent,
  DropdownComponent,
  DropdownOption,
  IconComponent,
  LinkComponent,
  TextCardComponent,
} from '@ava/play-comp-library';
import { QuickActionsComponent } from '../../shared/components/quick-actions/quick-actions.component';
import {
  ActivityMonitoringI,
  DashboardDetailI,
} from './models/dashboard.model';
import { ApprovalService } from '../../shared/services/approval.service';
import {
  ACTIVE_MONITORING_OPTIONS,
  ActiveMonitorings,
  APIKeys,
  DASHBOARD_CARD_DETAILS,
} from './constants/dashoard.constant';
import { RequestStatus } from '../approval/approval.component';
import approvalText from '../approval/constants/approval.json';

// Interfaces
interface UserLog {
  id: string;
  username: string;
  avatar: string;
  securityToken: string;
  status: 'Active' | 'Inactive' | 'Pending';
}

interface ModelUsage {
  id: string;
  name: string;
  publisher: {
    name: string;
    logo: string;
  };
  agentsCount: number;
}

interface PendingApproval {
  id: string;
  name: string;
  type: string;
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TextCardComponent,
    ButtonComponent,
    ApprovalCardComponent,
    IconComponent,
    DropdownComponent,
    QuickActionsComponent,
    AvaTagComponent,
    LinkComponent,
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss',
})
export class DashboardComponent implements OnInit {
  options: DropdownOption[] = ACTIVE_MONITORING_OPTIONS;
  public labels: any = approvalText.labels;

  quickActions: any[] = [
    { id: 'build-agent', label: 'Build Agent', icon: 'svgs/agents.svg' },
    {
      id: 'build-workflow',
      label: 'Build Workflow',
      icon: 'svgs/Workflows.svg',
    },
    { id: 'create-prompt', label: 'Create Prompt', icon: 'svgs/Prompts.svg' },
    { id: 'create-tool', label: 'Create Tool', icon: 'svgs/Tools.svg' },
    {
      id: 'create-guardrail',
      label: 'Create Guardrail',
      icon: 'svgs/Guardrails.svg',
    },
    {
      id: 'create-knowledge-base',
      label: 'Create Knowledge Base',
      icon: 'svgs/Knowledgebase.svg',
    },
  ];

  approvalCardData = {
    header: {
      iconName: '', // Optional, if you want an icon
      title: 'High Priority Approvals',
      viewAll: true,
    },
    contents: [
      {
        session1: {
          title: 'Autonomous Systems..... Agent',
          labels: [
            {
              name: 'Agent',
              color: 'info',
              background: '#B6F0FA',
              type: 'pill',
            },
            {
              name: 'High',
              color: 'error',
              background: '#E53935',
              type: 'pill',
            },
          ],
        },
        session2: [
          {
            name: 'Individual',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
          {
            name: 'Ascendion',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
          {
            name: 'Platform Engineering',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
          {
            name: 'Digital Ascender',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
          {
            name: 'Revamp Demo',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
        ],
        session3: [
          { iconName: 'user', label: '<EMAIL>' },
          { iconName: 'calendar-days', label: '12 May 2025' },
        ],
        session4: {
          status: 'Pending Approval',
          iconName: 'circle-check',
        },
      },
      // Additional cards
      {
        session1: {
          title: 'Invoice Processing & Approval',
          labels: [
            {
              name: 'Workflow',
              color: 'info',
              background: '#B6F0FA',
              type: 'pill',
            },
            {
              name: 'Critical',
              color: 'error',
              background: '#E53935',
              type: 'pill',
            },
          ],
        },
        session2: [
          {
            name: 'Finance',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
          {
            name: 'Ascendion',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
        ],
        session3: [
          { iconName: 'user', label: '<EMAIL>' },
          { iconName: 'calendar-days', label: '15 May 2025' },
        ],
        session4: {
          status: 'Pending Approval',
          iconName: 'circle-check',
        },
      },
      {
        session1: {
          title: 'Customer Support Chatbot',
          labels: [
            {
              name: 'Agent',
              color: 'info',
              background: '#B6F0FA',
              type: 'pill',
            },
            {
              name: 'Medium',
              color: 'warning',
              background: '#FFD600',
              type: 'pill',
            },
          ],
        },
        session2: [
          {
            name: 'Support',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
          {
            name: 'Ascendion',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
        ],
        session3: [
          { iconName: 'user', label: '<EMAIL>' },
          { iconName: 'calendar-days', label: '18 May 2025' },
        ],
        session4: {
          status: 'Awaiting Review',
          iconName: 'circle-check',
        },
      },
      {
        session1: {
          title: 'AI-Powered Code Review Assistant',
          labels: [
            {
              name: 'Agent',
              color: 'info',
              background: '#B6F0FA',
              type: 'pill',
            },
            {
              name: 'Low',
              color: 'success',
              background: '#4CAF50',
              type: 'pill',
            },
          ],
        },
        session2: [
          {
            name: 'Engineering',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
          {
            name: 'Ascendion',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
        ],
        session3: [
          { iconName: 'user', label: '<EMAIL>' },
          { iconName: 'calendar-days', label: '20 May 2025' },
        ],
        session4: {
          status: 'Completed',
          iconName: 'circle-check',
        },
      },
    ],
    footer: {},
  };
  dashboardDetails: DashboardDetailI[] = [
    // {
    //   icon: '',
    //   title: '',
    //   value: 0,
    //   subtitle: '',
    //   badge: '',
    // },
    // {
    //   icon: 'trending-up',
    //   title: 'Active Workflows',
    //   value: 80,
    //   subtitle: 'Agents actively running',
    //   badge: 'S',
    // },
    // {
    //   icon: 'trending-up',
    //   title: 'Active Agents',
    //   value: 1240,
    //   subtitle: 'Agents active',
    //   badge: 'S',
    // },
    // {
    //   icon: 'trending-up',
    //   title: 'Agents Approval',
    //   value: 120,
    //   subtitle: 'Agents actively running',
    //   badge: 'S',
    // },
    // {
    //   icon: 'trending-up',
    //   title: 'Active Workflows',
    //   value: 80,
    //   subtitle: 'Agents actively running',
    //   badge: 'S',
    // },
  ];
  // Dashboard metrics
  totalAgents: number = 0;
  newAgentsCreated: number = 50;

  totalWorkflows: number = 124;
  newWorkflowsCreated: number = 50;

  totalUsers: number = 300;
  newUsersAdded: number = 50;

  selectedActiveMonitoring = ActiveMonitorings.tools;

  activityMonitoringCount = 5;

  // Current user info (would come from auth service in real app)
  currentUser: { name: string } = { name: 'Akash Raj' };

  // Footer info
  currentYear: number = new Date().getFullYear();

  // User logs data
  userLogs: UserLog[] = [
    {
      id: '1',
      username: 'Michael Scott',
      avatar: 'assets/images/avatars/michael-scott.jpg',
      securityToken: 'X9D7K2B4MQ',
      status: 'Active',
    },
    {
      id: '2',
      username: 'Jim Halpert',
      avatar: 'assets/images/avatars/jim-halpert.jpg',
      securityToken: 'QWE2349SDF',
      status: 'Active',
    },
    {
      id: '3',
      username: 'Dwight Schrute',
      avatar: 'assets/images/avatars/dwight-schrute.jpg',
      securityToken: 'OWDF1230JS',
      status: 'Active',
    },
    {
      id: '4',
      username: 'Kevin Malone',
      avatar: 'assets/images/avatars/kevin-malone.jpg',
      securityToken: 'SDVP9I23EJ',
      status: 'Active',
    },
  ];

  // Model usage data
  modelUsage: ModelUsage[] = [
    {
      id: '1',
      name: 'GPT 3',
      publisher: {
        name: 'Open AI',
        logo: 'assets/images/logos/openai-logo.png',
      },
      agentsCount: 48,
    },
    {
      id: '2',
      name: 'Claude 2',
      publisher: {
        name: 'Anthropic',
        logo: 'assets/images/logos/anthropic-logo.png',
      },
      agentsCount: 24,
    },
    {
      id: '3',
      name: 'Gemini',
      publisher: {
        name: 'Google',
        logo: 'assets/images/logos/google-logo.png',
      },
      agentsCount: 20,
    },
    {
      id: '4',
      name: 'GPT-4',
      publisher: {
        name: 'Open AI',
        logo: 'assets/images/logos/openai-logo.png',
      },
      agentsCount: 8,
    },
  ];

  // Pending approvals
  pendingApprovals: PendingApproval[] = [
    {
      id: '1',
      name: 'Test Ruby to Springboot',
      type: 'migration',
    },
    {
      id: '2',
      name: 'Customer Support Chatbot',
      type: 'agent',
    },
    {
      id: '3',
      name: 'Invoice Processing & Approval',
      type: 'workflow',
    },
    {
      id: '4',
      name: 'Invoice Processing & Approval',
      type: 'workflow',
    },
    {
      id: '5',
      name: 'AI-Powered Code Review Assistant',
      type: 'agent',
    },
    {
      id: '6',
      name: 'AI-Powered Code Review Assistant',
      type: 'agent',
    },
    {
      id: '7',
      name: 'AI-Powered Code Review Assistant',
      type: 'agent',
    },
    {
      id: '8',
      name: 'AI-Powered Code Review Assistant',
      type: 'agent',
    },
  ];

  activityMonitoring: ActivityMonitoringI[] = [
    // {
    //   agentName: 'Agent Name will be here',
    //   status: 'Active',
    //   user: 'Michael Scott',
    //   date: '12 June 2025',
    //   totalRuns: 264,
    // },
    // {
    //   agentName: 'Agent Name will be here',
    //   status: 'Active',
    //   user: 'Michael Scott',
    //   date: '12 June 2025',
    //   totalRuns: 264,
    // },
    // {
    //   agentName: 'Agent Name will be here',
    //   status: 'Active',
    //   user: 'Michael Scott',
    //   date: '12 June 2025',
    //   totalRuns: 264,
    // },
    // {
    //   agentName: 'Agent Name will be here',
    //   status: 'Active',
    //   user: 'Michael Scott',
    //   date: '12 June 2025',
    //   totalRuns: 264,
    // },
    // {
    //   agentName: 'Agent Name will be here',
    //   status: 'Active',
    //   user: 'Michael Scott',
    //   date: '12 June 2025',
    //   totalRuns: 264,
    // },
  ];
  workflowApprovals: any[] = [];

  constructor(
    private router: Router,
    private apiService: SharedApiServiceService,
    private approvalService: ApprovalService,
  ) {}

  // seprating the tool and agents.
  activityMonitoringTools: ActivityMonitoringI[] = [];
  activityMonitoringAgents: ActivityMonitoringI[] = [];

  setActiveMonitoringData() {
    // INsted of re-formating, just switch the values
    if (this.selectedActiveMonitoring === ActiveMonitorings.tools) {
      this.activityMonitoring = this.activityMonitoringTools;
    } else {
      this.activityMonitoring = this.activityMonitoringAgents;
    }
  }

  mapToActivityMonitoringItem = (
    item: any,
    nameKey: APIKeys,
    usageKey: APIKeys,
  ): ActivityMonitoringI => ({
    agentName: item[nameKey] || '',
    totalRuns: Number(item[usageKey]) || 0,
    status: '',
    user: '',
    date: '',
  });

  public toRequestStatus(value: string | null | undefined): RequestStatus {
    return value === 'approved' || value === 'rejected' || value === 'review'
      ? value
      : 'review';
  }

  initApiCalls() {
    const date = new Date();
    const dateEnd = this.apiService.formatDate(date);
    date.setDate(1);
    const dateStart = this.apiService.formatDate(date);
    this.apiService
      .getCollabrativeAgentAnalytics(dateStart, dateEnd)
      .subscribe((response: Record<string, any>) => {
        // setting dashboard card values
        this.dashboardDetails = DASHBOARD_CARD_DETAILS.map((cardDetail) => {
          cardDetail.value =
            (response[cardDetail.field] as number) || this.totalAgents;
          return cardDetail as DashboardDetailI;
        });

        // Active Monitoring
        // Extracting tools and agents to seprate varibales to reduce frequent re-formatig as the dropdown value changes
        this.activityMonitoringTools = (response[APIKeys.toolUsage] as any[])
          .slice(0, this.activityMonitoringCount)
          .map((toolUsage) =>
            this.mapToActivityMonitoringItem(
              toolUsage,
              APIKeys.toolName,
              APIKeys.usageCount,
            ),
          );

        this.activityMonitoringAgents = (
          response[APIKeys.agentMetrics] as any[]
        )
          .slice(0, this.activityMonitoringCount)
          .map((agentMetric) =>
            this.mapToActivityMonitoringItem(
              agentMetric,
              APIKeys.agentName,
              APIKeys.workflowCount,
            ),
          );

        this.setActiveMonitoringData();
      });

    this.approvalService.getAllReviewAgents(1, 100, false).subscribe((res) => {
      const agentReviewDetails = res?.agentReviewDetails || [];
      // Extracting agents which are under review
      this.totalAgents =
        agentReviewDetails.filter(
          (agentReviewDetail: any) => agentReviewDetail.status !== 'approved',
        )?.length || 0;

      // If this API call is late then will set approval count here.
      const toolCardDetial = this.dashboardDetails.at(-1);
      if (toolCardDetial) {
        toolCardDetial.value = this.totalAgents;
      }
    });

    this.approvalService
      .getAllReviewWorkflows(1, 5, false)
      .subscribe((response) => {
        const type = 'workflow';

        this.workflowApprovals = response.workflowReviewDetails?.map(
          (req: any) => {
            const statusIcons: Record<RequestStatus, string> = {
              approved: 'circle-check-big',
              rejected: 'circle-x',
              review: 'clock',
            };
            const statusTexts: Record<RequestStatus, string> = {
              approved: this.labels.approved,
              rejected: this.labels.rejected,
              review: this.labels.review,
            };
            const statusKey = this.toRequestStatus(req?.status);
            let specificId = 0;
            let title = '';

            specificId = req.workflowId;
            title = req.workflowName;

            return {
              id: req.id,
              refId: specificId,
              type: type,
              session1: {
                title: title,
                labels: [
                  {
                    name: type,
                    color: 'success',
                    background: 'red',
                    type: 'normal',
                  },
                  {
                    name: req.changeRequestType,
                    color:
                      req.changeRequestType === 'update' ? 'error' : 'info',
                    background: 'red',
                    type: 'pill',
                  },
                ],
              },
              session2: [
                {
                  name: type,
                  color: 'default',
                  background: 'red',
                  type: 'normal',
                },
                {
                  name: req.status,
                  color: 'default',
                  background: 'red',
                  type: 'normal',
                },
              ],
              session3: [
                {
                  iconName: 'user',
                  label: req.requestedBy,
                },
                {
                  iconName: 'calendar-days',
                  label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN'),
                },
              ],
              session4: {
                status: statusTexts[statusKey],
                iconName: statusIcons[statusKey],
              },
            };
          },
        );
      });
  }

  ngOnInit(): void {
    // The layout is now managed with fixed heights in CSS
    // No need for recalculateLayout

    this.initApiCalls();
  }

  // Navigate to a route
  navigateTo(route: string): void {
    this.router.navigate([route]);
  }

  // Approve a pending item
  approveItem(id: string): void {
    // In a real app, you would call a service to approve the item
    // Then remove it from the pendingApprovals array or refresh the list
  }

  // Test method to demonstrate loader functionality
  testLoader(): void {
    this.apiService.getConfigLabels().subscribe({
      next: (response) => {},
      error: (error) => {},
    });
  }

  uClick(index: any) {
    this.router.navigate(['/approval']);
  }

  onSelectionChange(data: any) {
    this.selectedActiveMonitoring = data.selectedValue;
    this.setActiveMonitoringData();
  }

  onQuickActionClick(action: any) {
    console.log('Quick action clicked:', action);
    // Handle navigation or action based on action.id
    switch (action.id) {
      case 'build-agent':
        this.router.navigate(['/build/agents/create']);
        break;
      case 'build-workflow':
        this.router.navigate(['/build/workflows/create']);
        break;
      case 'create-prompt':
        this.router.navigate(['/libraries/prompts/create']);
        break;
      case 'create-tool':
        this.router.navigate(['/libraries/tools/create']);
        break;
      case 'create-guardrail':
        this.router.navigate(['/libraries/guardrails/create']);
        break;
      case 'create-knowledge-base':
        this.router.navigate(['/libraries/knowledge-base/create']);
        break;
      default:
        console.log('Unknown action:', action.id);
    }
  }
}
